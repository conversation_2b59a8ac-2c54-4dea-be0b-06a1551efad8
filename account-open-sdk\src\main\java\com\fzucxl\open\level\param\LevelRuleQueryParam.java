﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 交易ID
     */
    private String transactionId;}
