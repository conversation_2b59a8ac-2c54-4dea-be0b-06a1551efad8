﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分计算参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointCalculateParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 事件数据
     */
    private java.util.Map<String, Object> eventData;    
    /**
     * 是否仅计算不实际发放
     */
    private Boolean calculateOnly;    
    /**
     * 交易ID
     */
    private String transactionId;}
