﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 批量发放勋章结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeBatchAwardResult extends Extensible {    
    /**
     * 总用户数
     */
    private Integer totalCount;    
    /**
     * 成功数量
     */
    private Integer successCount;    
    /**
     * 失败数量
     */
    private Integer failCount;    
    /**
     * 失败详情
     */
    private java.util.List<BadgeBatchFailDetail> failDetails;}
