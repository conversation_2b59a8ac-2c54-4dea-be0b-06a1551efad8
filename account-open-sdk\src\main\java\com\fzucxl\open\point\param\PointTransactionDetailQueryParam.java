﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询交易详情参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointTransactionDetailQueryParam extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;}
