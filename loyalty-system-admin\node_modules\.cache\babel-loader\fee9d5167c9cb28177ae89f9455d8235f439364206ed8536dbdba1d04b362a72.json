{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemInputContext } from '../form/context';\nimport useStyle from './style';\n/** @deprecated Please use `Space.Compact` */\nconst Group = props => {\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className\n  } = props;\n  const prefixCls = getPrefixCls('input-group', customizePrefixCls);\n  const inputPrefixCls = getPrefixCls('input');\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(inputPrefixCls);\n  const cls = classNames(prefixCls, cssVarCls, {\n    [`${prefixCls}-lg`]: props.size === 'large',\n    [`${prefixCls}-sm`]: props.size === 'small',\n    [`${prefixCls}-compact`]: props.compact,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, className);\n  const formItemContext = useContext(FormItemInputContext);\n  const groupFormItemContext = useMemo(() => Object.assign(Object.assign({}, formItemContext), {\n    isFormItemInput: false\n  }), [formItemContext]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Input.Group');\n    warning.deprecated(false, 'Input.Group', 'Space.Compact');\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", {\n    className: cls,\n    style: props.style,\n    onMouseEnter: props.onMouseEnter,\n    onMouseLeave: props.onMouseLeave,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: groupFormItemContext\n  }, props.children)));\n};\nexport default Group;", "map": {"version": 3, "names": ["React", "useContext", "useMemo", "classNames", "devUseW<PERSON>ning", "ConfigContext", "FormItemInputContext", "useStyle", "Group", "props", "getPrefixCls", "direction", "prefixCls", "customizePrefixCls", "className", "inputPrefixCls", "wrapCSSVar", "hashId", "cssVarCls", "cls", "size", "compact", "formItemContext", "groupFormItemContext", "Object", "assign", "isFormItemInput", "process", "env", "NODE_ENV", "warning", "deprecated", "createElement", "style", "onMouseEnter", "onMouseLeave", "onFocus", "onBlur", "Provider", "value", "children"], "sources": ["D:/code/private/fast-account/loyalty-system-admin/node_modules/antd/es/input/Group.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemInputContext } from '../form/context';\nimport useStyle from './style';\n/** @deprecated Please use `Space.Compact` */\nconst Group = props => {\n  const {\n    getPrefixCls,\n    direction\n  } = useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className\n  } = props;\n  const prefixCls = getPrefixCls('input-group', customizePrefixCls);\n  const inputPrefixCls = getPrefixCls('input');\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(inputPrefixCls);\n  const cls = classNames(prefixCls, cssVarCls, {\n    [`${prefixCls}-lg`]: props.size === 'large',\n    [`${prefixCls}-sm`]: props.size === 'small',\n    [`${prefixCls}-compact`]: props.compact,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, className);\n  const formItemContext = useContext(FormItemInputContext);\n  const groupFormItemContext = useMemo(() => Object.assign(Object.assign({}, formItemContext), {\n    isFormItemInput: false\n  }), [formItemContext]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Input.Group');\n    warning.deprecated(false, 'Input.Group', 'Space.Compact');\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", {\n    className: cls,\n    style: props.style,\n    onMouseEnter: props.onMouseEnter,\n    onMouseLeave: props.onMouseLeave,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: groupFormItemContext\n  }, props.children)));\n};\nexport default Group;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,OAAOC,QAAQ,MAAM,SAAS;AAC9B;AACA,MAAMC,KAAK,GAAGC,KAAK,IAAI;EACrB,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGV,UAAU,CAACI,aAAa,CAAC;EAC7B,MAAM;IACJO,SAAS,EAAEC,kBAAkB;IAC7BC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMG,SAAS,GAAGF,YAAY,CAAC,aAAa,EAAEG,kBAAkB,CAAC;EACjE,MAAME,cAAc,GAAGL,YAAY,CAAC,OAAO,CAAC;EAC5C,MAAM,CAACM,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAACQ,cAAc,CAAC;EAChE,MAAMI,GAAG,GAAGhB,UAAU,CAACS,SAAS,EAAEM,SAAS,EAAE;IAC3C,CAAC,GAAGN,SAAS,KAAK,GAAGH,KAAK,CAACW,IAAI,KAAK,OAAO;IAC3C,CAAC,GAAGR,SAAS,KAAK,GAAGH,KAAK,CAACW,IAAI,KAAK,OAAO;IAC3C,CAAC,GAAGR,SAAS,UAAU,GAAGH,KAAK,CAACY,OAAO;IACvC,CAAC,GAAGT,SAAS,MAAM,GAAGD,SAAS,KAAK;EACtC,CAAC,EAAEM,MAAM,EAAEH,SAAS,CAAC;EACrB,MAAMQ,eAAe,GAAGrB,UAAU,CAACK,oBAAoB,CAAC;EACxD,MAAMiB,oBAAoB,GAAGrB,OAAO,CAAC,MAAMsB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,eAAe,CAAC,EAAE;IAC3FI,eAAe,EAAE;EACnB,CAAC,CAAC,EAAE,CAACJ,eAAe,CAAC,CAAC;EACtB,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAG1B,aAAa,CAAC,aAAa,CAAC;IAC5C0B,OAAO,CAACC,UAAU,CAAC,KAAK,EAAE,aAAa,EAAE,eAAe,CAAC;EAC3D;EACA,OAAOf,UAAU,CAAC,aAAahB,KAAK,CAACgC,aAAa,CAAC,MAAM,EAAE;IACzDlB,SAAS,EAAEK,GAAG;IACdc,KAAK,EAAExB,KAAK,CAACwB,KAAK;IAClBC,YAAY,EAAEzB,KAAK,CAACyB,YAAY;IAChCC,YAAY,EAAE1B,KAAK,CAAC0B,YAAY;IAChCC,OAAO,EAAE3B,KAAK,CAAC2B,OAAO;IACtBC,MAAM,EAAE5B,KAAK,CAAC4B;EAChB,CAAC,EAAE,aAAarC,KAAK,CAACgC,aAAa,CAAC1B,oBAAoB,CAACgC,QAAQ,EAAE;IACjEC,KAAK,EAAEhB;EACT,CAAC,EAAEd,KAAK,CAAC+B,QAAQ,CAAC,CAAC,CAAC;AACtB,CAAC;AACD,eAAehC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}