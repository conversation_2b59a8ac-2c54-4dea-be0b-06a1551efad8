package com.fzucxl.entity.event;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.data.annotation.*;
import io.micronaut.serde.annotation.Serdeable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务事件实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedEntity("business_event")
@Introspected
@Serdeable
public class BusinessEvent {
    
    @Id
    @GeneratedValue(GeneratedValue.Type.IDENTITY)
    private Long id;
    
    /**
     * 事件编码，唯一标识
     */
    private String eventCode;
    
    /**
     * 事件名称
     */
    private String eventName;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 事件分类
     */
    private String category;
    
    /**
     * 事件状态：ACTIVE-启用，INACTIVE-禁用
     */
    private String status = "ACTIVE";
    
    /**
     * 事件版本号
     */
    private Integer version = 1;
    
    /**
     * 创建时间
     */
    @DateCreated
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @DateUpdated
    private LocalDateTime updatedAt;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 事件字段列表
     */
    @Relation(value = Relation.Kind.ONE_TO_MANY)
    private List<BusinessEventField> eventFields;
    
    // Constructors
    public BusinessEvent() {}
    
    public BusinessEvent(String eventCode, String eventName) {
        this.eventCode = eventCode;
        this.eventName = eventName;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getEventCode() {
        return eventCode;
    }
    
    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }
    
    public String getEventName() {
        return eventName;
    }
    
    public void setEventName(String eventName) {
        this.eventName = eventName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getVersion() {
        return version;
    }
    
    public void setVersion(Integer version) {
        this.version = version;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    public List<BusinessEventField> getEventFields() {
        return eventFields;
    }
    
    public void setEventFields(List<BusinessEventField> eventFields) {
        this.eventFields = eventFields;
    }
}