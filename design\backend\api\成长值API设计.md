# 成长值系统API设计文档

## 1. 概述

### 1.1 文档说明

本文档定义了成长值系统的RESTful API接口设计，采用标准的HTTP协议，支持JSON格式的数据交换。文档包含对外服务API和Web管理端API两大类接口。

### 1.2 接口分类

- **对外服务API**：面向业务系统和客户端应用，提供用户成长值管理、成长值获取、查询统计等核心功能
- **Web管理端API**：面向运营管理人员，提供成长值规则配置、数据统计分析、系统配置等管理功能

### 1.3 接口列表

#### 1.3.1 对外服务API接口汇总

| 功能模块       | 接口名称 | 请求方式 | 接口路径                                         | 接口描述                           |
|------------| -------- | -------- |----------------------------------------------|--------------------------------|
| **用户成长值管理** | 查询用户成长值账户 | GET | `/api/v1/growth/account`                     | 查询指定用户的成长值账户信息，包括总成长值、各维度成长值等 |
|            | 查询用户成长值明细 | GET | `/api/v1/growth/record`                      | 分页查询指定用户的成长值明细记录，支持多维度筛选        |
| **成长值获取处理** | 获取成长值 | POST | `/api/v1/growth/earn`                        | 用户通过业务行为获取成长值，支持多种业务类型          |
|            | 调整成长值 | POST | `/api/v1/growth/adjust`                      | 手动调整用户成长值，用于补偿或纠正场景          |
|            | 成长值事务查询 | GET | `/api/v1/growth/transaction`                 | 查询成长值事务记录，支持按多种条件筛选和分页查询        |
|            | 成长值事务详情查询 | GET | `/api/v1/growth/transaction/{transactionId}` | 根据事务ID查询成长值事务的详细信息，包含执行日志和规则信息 |
| **成长值规则**   | 获取成长值规则 | GET | `/api/v1/growth/rule`                        | 获取账户的成长值规则列表                    |
|            | 计算成长值 | POST | `/api/v1/growth/calculate`                   | 根据业务场景计算应得成长值                   |
| **成长值统计**   | 成长值统计查询 | GET | `/api/v1/growth/statistics`                  | 查询用户成长值统计数据，支持多维度统计                   |
|            | 成长值排行查询 | GET | `/api/v1/growth/ranking`                     | 查询成长值排行榜数据                   |

#### 1.3.2 Web管理端API接口汇总

| 功能模块 | 接口名称 | 请求方式 | 接口路径 | 接口描述 |
| -------- | -------- | -------- | -------- | -------- |
| **成长值规则管理** | 创建成长值规则 | POST | `/admin/api/v1/growth/rule` | 创建新的成长值规则 |
| | 更新成长值规则 | PUT | `/admin/api/v1/growth/rule/{id}` | 更新现有成长值规则 |
| | 删除成长值规则 | DELETE | `/admin/api/v1/growth/rule/{id}` | 删除成长值规则 |
| | 查询成长值规则列表 | GET | `/admin/api/v1/growth/rule` | 查询成长值规则列表 |
| | 测试成长值规则 | POST | `/admin/api/v1/growth/rule/test` | 测试成长值规则的执行效果，验证规则表达式的正确性和计算结果 |
| **成长值统计分析** | 成长值获取统计 | GET | `/admin/api/v1/growth/stat/earn` | 查询成长值获取统计数据 |
| | 成长值分布统计 | GET | `/admin/api/v1/growth/stat/distribution` | 查询成长值分布统计数据 |
| | 用户成长值排行 | GET | `/admin/api/v1/growth/stat/ranking` | 查询用户成长值排行 |
| **成长值账户管理** | 查询成长值账户列表 | GET | `/admin/api/v1/growth/accounts` | 查询品牌下所有成长值账户配置列表 |
| | 查询成长值账户详情 | GET | `/admin/api/v1/growth/account/{accountCode}` | 查询指定成长值账户的详细配置信息 |
| | 创建成长值账户 | POST | `/admin/api/v1/growth/account` | 创建新的成长值账户配置 |
| | 更新成长值账户 | PUT | `/admin/api/v1/growth/account/{accountCode}` | 更新成长值账户的配置信息 |
| | 删除成长值账户 | DELETE | `/admin/api/v1/growth/account/{accountCode}` | 删除成长值账户配置（软删除） |

#### 1.3.3 接口特性说明

**对外服务API特性：**
- 支持高并发访问，提供完善的限流策略
- 所有事务类接口支持幂等性操作
- 提供实时成长值查询和明细查询能力
- 支持多种成长值操作：获取、调整、查询
- 提供成长值规则查询和计算服务

**Web管理端API特性：**
- 提供完整的成长值规则CRUD操作
- 支持属性元数据的动态配置和测试
- 提供丰富的统计分析功能
- 支持系统配置的在线管理
- 所有管理接口支持分页查询

**通用特性：**
- 统一的响应格式和错误码体系
- 完善的参数校验和异常处理
- 支持链路追踪和日志记录
- 提供详细的接口文档和示例

### 1.4 技术规范

- **协议**：HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **API版本**：v1
- **认证方式**：Bearer Token / API Key
- **限流策略**：基于用户/IP的令牌桶算法

### 1.5 通用响应格式

所有API接口均采用统一的响应格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | Integer | 响应码，0表示成功，非0表示失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| timestamp | Long | 响应时间戳 |
| traceId | String | 链路追踪ID |

### 1.6 分页响应格式

对于分页查询接口，data字段结构如下：

```json
{
  "total": 100,
  "pageNum": 1,
  "pageSize": 10,
  "pages": 10,
  "list": []
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Integer | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页记录数 |
| pages | Integer | 总页数 |
| list | Array | 数据列表 |

## 2. 对外服务API

### 2.1 用户成长值管理

#### 2.1.1 查询用户成长值账户

**基本信息**
- **接口路径**：`GET /api/v1/growth/account`
- **接口描述**：查询指定用户的成长值账户信息，包括总成长值、各维度成长值等
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述             | 示例 |
| ------ | ---- | ---- | ---- |----------------| ---- |
| userId | String | Query | 是 | 用户唯一标识         | "user_123456" |
| accountCode | String | Query | 是 | 账户代码           | "MALL_APP" |
| transactionId | String | Query | 否 | 交易ID，用于关联上下游交易 | "tx_789012" |

**响应参数**

| 参数名            | 类型 | 描述     | 示例                    |
|----------------| ---- |--------|-----------------------|
| userId         | String | 用户ID   | "user_123456"         |
| accountCode    | String | 账户代码   | "MALL_APP"            |
| brandCode      | String | 品牌代码   | "LN"                  |
| totalGrowth    | Long | 总成长值   | 5000                  |
| consumeGrowth  | Long | 消费成长值  | 2000                  |
| activityGrowth | Long | 活跃成长值  | 1500                  |
| socialGrowth   | Long | 社交成长值  | 800                   |
| taskGrowth     | Long | 任务成长值  | 700                   |
| createTime     | String | 创建时间   | "2024-01-01 10:00:00" |
| lastUpdateTime | String | 最后更新时间 | "2024-01-15 10:30:00" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "totalGrowth": 5000,
    "consumeGrowth": 2000,
    "activityGrowth": 1500,
    "socialGrowth": 800,
    "taskGrowth": 700,
    "createTime": "2024-01-01 10:00:00",
    "lastUpdateTime": "2024-01-15 10:30:00"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述     | 解决方案       |
| ------ |--------|------------|
| 1001 | 参数错误   | 检查必填参数是否完整 |
| 1002 | 用户不存在  | 确认用户ID是否正确 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |

#### 2.1.2 查询用户成长值明细

**基本信息**
- **接口路径**：`GET /api/v1/growth/record`
- **接口描述**：分页查询指定用户的成长值明细记录，支持多维度筛选
- **访问权限**：需要API认证
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Query | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| pageNum | Integer | Query | 否 | 页码     | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数  | 20 | 默认20，范围1-100 |
| startTime | String | Query | 否 | 开始时间   | "2024-01-01 00:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | Query | 否 | 结束时间   | "2024-01-31 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| changeType | String | Query | 否 | 变动类型   | "EARN" | EARN/ADJUST/CORRECTION |
| businessType | String | Query | 否 | 业务类型   | "PURCHASE" | PURCHASE/ACTIVITY/SOCIAL/TASK |
| source | String | Query | 否 | 成长值来源  | "SYSTEM" | 长度1-32 |
| minGrowth | Long | Query | 否 | 最小成长值  | 10 | 大于0 |
| maxGrowth | Long | Query | 否 | 最大成长值  | 1000 | 大于minGrowth |
| transactionId | String | Query | 否 | 交易ID   | "tx_789012" | 长度1-64 |

**响应参数**

| 参数名 | 类型 | 描述                | 示例 |
| ------ | ---- |-------------------| ---- |
| total | Integer | 总记录数              | 150 |
| pageNum | Integer | 当前页码              | 1 |
| pageSize | Integer | 每页记录数             | 20 |
| pages | Integer | 总页数               | 8 |
| list | Array | 成长值明细列表           | - |
| list[].id | String | 记录唯一标识            | "detail_001" |
| list[].userId | String | 用户ID              | "user_123456" |
| list[].accountCode | String | 账户代码              | "MALL_APP" |
| list[].changeType | String | 变动类型              | "EARN" |
| list[].growthValue | Long | 成长值数量             | 100 |
| list[].businessType | String | 业务类型              | "PURCHASE" |
| list[].businessId | String | 业务ID              | "order_20240115001" |
| list[].source | String | 成长值来源             | "SYSTEM" |
| list[].reason | String | 原因描述              | "购买商品获得成长值" |
| list[].transactionId | String | 交易ID              | "tx_789012" |
| list[].ruleCode | String | 规则代码              | "PURCHASE_RULE" |
| list[].baseGrowth | Long | 基础成长值             | 80 |
| list[].multiplier | BigDecimal | 倍率                | 1.25 |
| list[].createTime | String | 创建时间              | "2024-01-15 08:00:00" |
| list[].extraData | String | 扩展数据              | "{\"orderId\":\"123\"}" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 150,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 8,
    "list": [
      {
        "id": "detail_001",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "changeType": "EARN",
        "growthValue": 100,
        "businessType": "PURCHASE",
        "businessId": "order_20240115001",
        "source": "SYSTEM",
        "reason": "购买商品获得成长值",
        "transactionId": "tx_789012",
        "ruleCode": "PURCHASE_RULE",
        "baseGrowth": 80,
        "multiplier": 1.25,
        "createTime": "2024-01-15 08:00:00",
        "extraData": "{\"orderId\":\"order_20240115001\",\"amount\":500}"
      },
      {
        "id": "detail_002",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "changeType": "EARN",
        "growthValue": 50,
        "businessType": "ACTIVITY",
        "businessId": "activity_signin",
        "source": "SYSTEM",
        "reason": "每日签到获得成长值",
        "transactionId": "tx_789013",
        "ruleCode": "SIGNIN_RULE",
        "baseGrowth": 50,
        "multiplier": 1.0,
        "createTime": "2024-01-15 10:30:00",
        "extraData": "{\"signDays\":7}"
      }
    ]
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述     | 解决方案         |
| ------ |--------|--------------|
| 1001 | 参数错误   | 检查参数格式和取值范围  |
| 1002 | 用户不存在  | 确认用户ID是否正确   |
| 1003 | 账户代码无效 | 确认账户代码是否正确   |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数       |

### 2.2 成长值获取处理

#### 2.2.1 获取成长值

**基本信息**
- **接口路径**：`POST /api/v1/growth/earn`
- **接口描述**：用户通过业务行为获取成长值，支持多种业务类型
- **访问权限**：需要API认证
- **限流规则**：200次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| businessType | String | Body | 是 | 业务类型   | "PURCHASE" | PURCHASE/ACTIVITY/SOCIAL/TASK |
| businessId | String | Body | 是 | 业务ID   | "order_20240115001" | 长度1-128 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789012" | 长度1-64 |
| businessData | Object | Body | 是 | 业务数据   | {} | JSON对象 |
| source | String | Body | 否 | 成长值来源  | "SYSTEM" | 默认SYSTEM |
| reason | String | Body | 否 | 获取原因   | "购买商品获得成长值" | 长度1-256 |
| notifyUrl | String | Body | 否 | 回调通知地址 | "https://api.example.com/notify" | 有效URL |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "businessType": "PURCHASE",
  "businessId": "order_20240115001",
  "businessData": {
    "orderId": "order_20240115001",
    "amount": 500,
    "productCount": 3,
    "categoryId": "electronics"
  },
  "source": "SYSTEM",
  "reason": "购买商品获得成长值",
  "businessId": "biz_20240115001",
  "transactionId": "tx_789012",
  "notifyUrl": "https://api.example.com/notify"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| transactionId | String | 系统生成的交易ID | "sys_tx_001" |
| detailId | String | 成长值明细记录ID | "detail_001" |
| growthValue | Long | 实际获得的成长值 | 100 |
| beforeTotalGrowth | Long | 获取前总成长值 | 1000 |
| afterTotalGrowth | Long | 获取后总成长值 | 1100 |
| ruleCode | String | 应用的规则代码 | "PURCHASE_RULE" |
| baseGrowth | Long | 基础成长值 | 80 |
| multiplier | BigDecimal | 倍率 | 1.25 |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "成长值获取成功",
  "data": {
    "success": true,
    "transactionId": "sys_tx_001",
    "detailId": "detail_001",
    "growthValue": 100,
    "beforeTotalGrowth": 1000,
    "afterTotalGrowth": 1100,
    "ruleCode": "PURCHASE_RULE",
    "baseGrowth": 80,
    "multiplier": 1.25,
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2001 | 业务数据无效 | 检查业务数据格式和内容 |
| 2002 | 用户成长值账户异常 | 检查用户账户状态 |
| 2003 | 业务单号重复 | 使用新的业务单号 |
| 2004 | 业务类型无效 | 确认业务类型是否正确 |
| 2005 | 规则匹配失败 | 检查是否有适用的规则 |
| 2006 | 成长值计算失败 | 检查规则配置和业务数据 |

#### 2.2.2 调整成长值

**基本信息**
- **接口路径**：`POST /api/v1/growth/adjust`
- **接口描述**：手动调整用户成长值，用于补偿或纠正场景
- **访问权限**：需要API认证
- **限流规则**：100次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述       | 示例 | 约束 |
| ------ | ---- | ---- | ---- |----------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识   | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码     | "MALL_APP" | 长度1-32 |
| adjustType | String | Body | 是 | 调整类型     | "INCREASE" | INCREASE/DECREASE |
| growthValue | Long | Body | 是 | 调整成长值数量  | 50 | 正整数 |
| businessType | String | Body | 否 | 业务类型     | "PURCHASE" | PURCHASE/ACTIVITY/SOCIAL/TASK |
| reason | String | Body | 是 | 调整原因     | "系统补偿" | 长度1-256 |
| businessId | String | Body | 是 | 业务ID   | "order_20240115001" | 长度1-128 |
| transactionId | String | Body | 否 | 交易ID     | "tx_789014" | 长度1-64 |
| operator | String | Body | 否 | 操作人      | "admin" | 长度1-64 |
| notifyUrl | String | Body | 否 | 回调通知地址   | "https://api.example.com/notify" | 有效URL |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "adjustType": "INCREASE",
  "growthValue": 50,
  "businessType": "PURCHASE",
  "reason": "系统补偿",
  "businessId": "adjust_20240115001",
  "transactionId": "tx_789014",
  "operator": "admin",
  "notifyUrl": "https://api.example.com/notify"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| transactionId | String | 系统生成的交易ID | "sys_tx_002" |
| detailId | String | 成长值明细记录ID | "detail_002" |
| adjustType | String | 调整类型 | "INCREASE" |
| growthValue | Long | 实际调整的成长值 | 50 |
| beforeTotalGrowth | Long | 调整前总成长值 | 1100 |
| afterTotalGrowth | Long | 调整后总成长值 | 1150 |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "成长值调整成功",
  "data": {
    "success": true,
    "transactionId": "sys_tx_002",
    "detailId": "detail_002",
    "adjustType": "INCREASE",
    "growthValue": 50,
    "beforeTotalGrowth": 1100,
    "afterTotalGrowth": 1150,
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2101 | 调整数量无效 | 检查调整数量是否为正数 |
| 2102 | 用户成长值账户异常 | 检查用户账户状态 |
| 2103 | 业务单号重复 | 使用新的业务单号 |
| 2104 | 调整类型无效 | 确认调整类型是否正确 |
| 2105 | 权限不足 | 确认是否有调整权限 |

#### 2.2.3 成长值事务查询

**基本信息**
- **接口路径**：`GET /api/v1/growth/transaction`
- **接口描述**：查询成长值事务记录，支持按多种条件筛选和分页查询
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Query | 否 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码 | "MALL_APP" | 长度1-32 |
| transactionId | String | Query | 否 | 事务ID | "GT20240115001" | 长度1-64 |
| businessId | String | Query | 否 | 业务单号 | "biz_20240115001" | 长度1-64 |
| transactionType | String | Query | 否 | 事务类型 | "GROWTH_EARN" | 见事务类型枚举 |
| status | String | Query | 否 | 事务状态 | "SUCCESS" | PENDING/PROCESSING/SUCCESS/FAILED/CANCELLED |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01 00:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| total | Integer | 总记录数 | 150 |
| pageNum | Integer | 当前页码 | 1 |
| pageSize | Integer | 每页记录数 | 20 |
| pages | Integer | 总页数 | 8 |
| list | Array | 事务记录列表 | - |
| list[].transactionId | String | 事务ID | "GT20240115001" |
| list[].userId | String | 用户ID | "user_123456" |
| list[].accountCode | String | 账户代码 | "MALL_APP" |
| list[].businessId | String | 业务单号 | "biz_20240115001" |
| list[].businessType | String | 业务类型 | "PURCHASE" |
| list[].transactionType | String | 事务类型 | "GROWTH_EARN" |
| list[].growthChange | Long | 成长值变化量 | 100 |
| list[].beforeGrowth | Long | 变化前成长值 | 1000 |
| list[].afterGrowth | Long | 变化后成长值 | 1100 |
| list[].status | String | 事务状态 | "SUCCESS" |
| list[].description | String | 事务描述 | "购买商品获得成长值" |
| list[].source | String | 成长值来源 | "SYSTEM" |
| list[].processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| list[].errorMessage | String | 错误信息 | null |
| list[].retryCount | Integer | 重试次数 | 0 |
| list[].createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| list[].updateTime | String | 更新时间 | "2024-01-15 10:00:01" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 150,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 8,
    "list": [
      {
        "transactionId": "GT20240115001",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "businessId": "biz_20240115001",
        "businessType": "PURCHASE",
        "transactionType": "GROWTH_EARN",
        "growthChange": 100,
        "beforeGrowth": 1000,
        "afterGrowth": 1100,
        "status": "SUCCESS",
        "description": "购买商品获得成长值",
        "source": "SYSTEM",
        "processedTime": "2024-01-15 10:00:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 10:00:00",
        "updateTime": "2024-01-15 10:00:01"
      },
      {
        "transactionId": "GT20240115002",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "businessId": "biz_20240115002",
        "businessType": "ACTIVITY",
        "transactionType": "GROWTH_ADJUST",
        "growthChange": 50,
        "beforeGrowth": 1100,
        "afterGrowth": 1150,
        "status": "SUCCESS",
        "description": "系统补偿成长值",
        "source": "ADMIN",
        "processedTime": "2024-01-15 14:30:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 14:30:00",
        "updateTime": "2024-01-15 14:30:01"
      }
    ]
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**事务类型枚举**

| 类型 | 描述 |
| ---- | ---- |
| GROWTH_EARN | 成长值获取 |
| GROWTH_ADJUST | 成长值调整 |
| GROWTH_CORRECTION | 成长值纠正 |
| GROWTH_COMPENSATION | 成长值补偿 |
| GROWTH_MANUAL | 成长值手动操作 |

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查参数格式和取值范围 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数 |

#### 2.2.4 成长值事务详情查询

**基本信息**
- **接口路径**：`GET /api/v1/growth/transaction/{transactionId}`
- **接口描述**：根据事务ID查询成长值事务的详细信息
- **访问权限**：需要API认证
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| transactionId | String | Path | 是 | 事务ID | "GT20240115001" | 长度1-64 |
| includeDetail | Boolean | Query | 否 | 是否包含详细信息 | true | 默认false |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| transactionId | String | 事务ID | "GT20240115001" |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "MALL_APP" |
| businessId | String | 业务单号 | "biz_20240115001" |
| businessType | String | 业务类型 | "PURCHASE" |
| transactionType | String | 事务类型 | "GROWTH_EARN" |
| growthChange | Long | 成长值变化量 | 100 |
| beforeGrowth | Long | 变化前成长值 | 1000 |
| afterGrowth | Long | 变化后成长值 | 1100 |
| status | String | 事务状态 | "SUCCESS" |
| description | String | 事务描述 | "购买商品获得成长值" |
| source | String | 成长值来源 | "SYSTEM" |
| processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| errorMessage | String | 错误信息 | null |
| retryCount | Integer | 重试次数 | 0 |
| maxRetryCount | Integer | 最大重试次数 | 3 |
| operatorId | String | 操作人ID | "system" |
| extraData | String | 扩展数据 | "{\"remark\":\"系统自动处理\"}" |
| createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| updateTime | String | 更新时间 | "2024-01-15 10:00:01" |
| version | Integer | 版本号 | 1 |
| detail | Object | 详细信息（当includeDetail=true时返回） | - |
| detail.ruleInfo | Object | 规则信息 | - |
| detail.ruleInfo.ruleId | Long | 规则ID | 1001 |
| detail.ruleInfo.ruleName | String | 规则名称 | "购买商品成长值规则" |
| detail.ruleInfo.ruleExpression | String | 规则表达式 | "order.amount > 100 ? order.amount * 0.1 : 0" |
| detail.executionLog | Array | 执行日志 | - |
| detail.executionLog[].step | String | 执行步骤 | "RULE_EVALUATION" |
| detail.executionLog[].timestamp | String | 时间戳 | "2024-01-15 10:00:00.123" |
| detail.executionLog[].message | String | 日志信息 | "规则评估完成，计算成长值：100" |
| detail.executionLog[].data | Object | 相关数据 | {} |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "transactionId": "GT20240115001",
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "businessId": "biz_20240115001",
    "businessType": "PURCHASE",
    "transactionType": "GROWTH_EARN",
    "growthChange": 100,
    "beforeGrowth": 1000,
    "afterGrowth": 1100,
    "status": "SUCCESS",
    "description": "购买商品获得成长值",
    "source": "SYSTEM",
    "processedTime": "2024-01-15 10:00:01",
    "errorMessage": null,
    "retryCount": 0,
    "maxRetryCount": 3,
    "operatorId": "system",
    "extraData": "{\"remark\":\"系统自动处理\"}",
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:01",
    "version": 1,
    "detail": {
      "ruleInfo": {
        "ruleId": 1001,
        "ruleName": "购买商品成长值规则",
        "ruleExpression": "order.amount > 100 ? order.amount * 0.1 : 0"
      },
      "executionLog": [
        {
          "step": "TRANSACTION_CREATE",
          "timestamp": "2024-01-15 10:00:00.001",
          "message": "创建成长值事务",
          "data": {}
        },
        {
          "step": "RULE_EVALUATION",
          "timestamp": "2024-01-15 10:00:00.123",
          "message": "规则评估完成，计算成长值：100",
          "data": {
            "ruleId": 1001,
            "calculatedGrowth": 100
          }
        },
        {
          "step": "GROWTH_UPDATE",
          "timestamp": "2024-01-15 10:00:00.456",
          "message": "更新用户成长值",
          "data": {
            "beforeGrowth": 1000,
            "afterGrowth": 1100
          }
        },
        {
          "step": "TRANSACTION_COMPLETE",
          "timestamp": "2024-01-15 10:00:01.000",
          "message": "事务处理完成",
          "data": {}
        }
      ]
    }
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查事务ID格式 |
| 2201 | 事务不存在 | 确认事务ID是否正确 |
| 2202 | 事务已过期 | 事务记录已超过保存期限 |

### 2.3 成长值规则

#### 2.3.1 获取成长值规则

**基本信息**
- **接口路径**：`GET /api/v1/growth/rule`
- **接口描述**：获取账户的成长值规则列表
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                        | 示例 |
| ------ | ---- | ---- | ---- |---------------------------|-----|
| accountCode | String | Query | 是 | 账户代码                      | "MALL_APP" |
| ruleCode | String | Query | 否 | 规则代码                      | "PURCHASE_RULE" |
| ruleName | String | Query | 否 | 规则名称，支持模糊查询               | "购买" |
| businessType | String | Query | 否 | 业务类型                      | "PURCHASE" |
| status | String | Query | 否 | 状态：ACTIVE-生效，INACTIVE-未生效 | "ACTIVE" |
| pageNum | Integer | Query | 否 | 页码，默认1                    | 1 |
| pageSize | Integer | Query | 否 | 每页记录数，默认10                | 10 |

**响应参数**

| 参数名 | 类型 | 描述    | 示例 |
| ------ | ---- |-------|-----|
| total | Integer | 总记录数  | 100 |
| list | Array | 规则列表  | - |
| list[].id | Long | 规则ID  | 1001 |
| list[].accountCode | String | 账户代码  | "MALL_APP" |
| list[].ruleCode | String | 规则代码  | "PURCHASE_RULE" |
| list[].ruleName | String | 规则名称  | "购买商品成长值规则" |
| list[].businessType | String | 业务类型  | "PURCHASE" |
| list[].description | String | 规则描述  | "用户购买商品获得成长值" |
| list[].baseGrowth | Long | 基础成长值 | 100 |
| list[].multiplier | BigDecimal | 倍率    | 1.5 |
| list[].dailyLimit | Long | 每日限制  | 1000 |
| list[].monthlyLimit | Long | 每月限制  | 10000 |
| list[].ruleExpression | String | 规则表达式 | "order.amount > 100" |
| list[].priority | Integer | 优先级   | 10 |
| list[].status | String | 状态    | "ACTIVE" |
| list[].startTime | String | 开始时间  | "2024-01-01 00:00:00" |
| list[].endTime | String | 结束时间  | "2024-12-31 23:59:59" |
| list[].createBy | String | 创建人   | "admin" |
| list[].createTime | String | 创建时间  | "2024-01-01 10:00:00" |
| list[].updateTime | String | 更新时间  | "2024-01-01 10:00:00" |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1001,
        "accountCode": "MALL_APP",
        "ruleCode": "PURCHASE_RULE",
        "ruleName": "购买商品成长值规则",
        "businessType": "PURCHASE",
        "description": "用户购买商品获得成长值",
        "baseGrowth": 100,
        "multiplier": 1.5,
        "dailyLimit": 1000,
        "monthlyLimit": 10000,
        "ruleExpression": "order.amount > 100",
        "priority": 10,
        "status": "ACTIVE",
        "startTime": "2024-01-01 00:00:00",
        "endTime": "2024-12-31 23:59:59",
        "createBy": "admin",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

#### 2.3.2 计算成长值

**基本信息**
- **接口路径**：`POST /api/v1/growth/calculate`
- **接口描述**：根据业务场景计算应得成长值
- **访问权限**：需要API认证
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述               | 示例 |
| ------ | ---- | ---- | ---- |------------------|-----|
| userId | String | Body | 是 | 用户ID             | "user_123456" |
| accountCode | String | Body | 是 | 账户代码             | "MALL_APP" |
| businessType | String | Body | 是 | 业务类型             | "PURCHASE" |
| businessData | Object | Body | 是 | 业务数据，根据不同场景有不同结构 | {} |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "businessType": "PURCHASE",
  "businessData": {
    "orderId": "order_20240115001",
    "amount": 500,
    "productCount": 3,
    "categoryId": "electronics"
  }
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| growthValue | Long | 计算得出的成长值 | 150 |
| rule | Array | 适用的规则列表 | - |
| rule[].ruleCode | String | 规则代码 | "PURCHASE_RULE" |
| rule[].ruleName | String | 规则名称 | "购买商品成长值规则" |
| rule[].baseGrowth | Long | 基础成长值 | 100 |
| rule[].multiplier | BigDecimal | 倍率 | 1.5 |
| rule[].growthValue | Long | 该规则贡献的成长值 | 150 |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "growthValue": 150,
    "rule": [
      {
        "ruleCode": "PURCHASE_RULE",
        "ruleName": "购买商品成长值规则",
        "baseGrowth": 100,
        "multiplier": 1.5,
        "growthValue": 150
      }
    ]
  }
}
```

### 2.4 成长值统计

#### 2.4.1 成长值统计查询

**基本信息**
- **接口路径**：`GET /api/v1/growth/statistics`
- **接口描述**：查询用户成长值统计数据，支持多维度统计
- **访问权限**：需要API认证
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                          | 示例 |
| ------ | ---- | ---- | ---- |-----------------------------|-----|
| userId | String | Query | 是 | 用户ID                        | "user_123456" |
| accountCode | String | Query | 是 | 账户代码                        | "MALL_APP" |
| startDate | String | Query | 是 | 开始日期，格式：yyyy-MM-dd          | "2024-01-01" |
| endDate | String | Query | 是 | 结束日期，格式：yyyy-MM-dd          | "2024-01-31" |
| groupBy | String | Query | 否 | 分组方式：DAY, WEEK, MONTH，默认DAY | "DAY" |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| totalGrowth | Long | 总成长值 | 5000 |
| detail | Array | 统计明细 | - |
| detail[].date | String | 日期 | "2024-01-01" |
| detail[].totalGrowth | Long | 当日总成长值 | 100 |
| detail[].consumeGrowth | Long | 当日消费成长值 | 60 |
| detail[].activityGrowth | Long | 当日活跃成长值 | 20 |
| detail[].socialGrowth | Long | 当日社交成长值 | 10 |
| detail[].taskGrowth | Long | 当日任务成长值 | 10 |
| typeDistribution | Array | 类型分布 | - |
| typeDistribution[].businessType | String | 业务类型 | "PURCHASE" |
| typeDistribution[].growthValue | Long | 成长值数量 | 3000 |
| typeDistribution[].percentage | Number | 百分比 | 60.0 |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalGrowth": 5000,
    "detail": [
      {
        "date": "2024-01-01",
        "totalGrowth": 100,
        "consumeGrowth": 60,
        "activityGrowth": 20,
        "socialGrowth": 10,
        "taskGrowth": 10
      },
      {
        "date": "2024-01-02",
        "totalGrowth": 120,
        "consumeGrowth": 80,
        "activityGrowth": 20,
        "socialGrowth": 10,
        "taskGrowth": 10
      }
    ],
    "typeDistribution": [
      {
        "businessType": "PURCHASE",
        "growthValue": 3000,
        "percentage": 60.0
      },
      {
        "businessType": "ACTIVITY",
        "growthValue": 1000,
        "percentage": 20.0
      },
      {
        "businessType": "SOCIAL",
        "growthValue": 500,
        "percentage": 10.0
      },
      {
        "businessType": "TASK",
        "growthValue": 500,
        "percentage": 10.0
      }
    ]
  }
}
```

#### 2.4.2 成长值排行查询

**基本信息**
- **接口路径**：`GET /api/v1/growth/ranking`
- **接口描述**：查询成长值排行榜数据
- **访问权限**：需要API认证
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                                                   | 示例 |
| ------ | ---- | ---- | ---- |------------------------------------------------------|-----|
| accountCode | String | Query | 是 | 账户代码                                                 | "MALL_APP" |
| type | String | Query | 否 | 排行类型：TOTAL-总成长值，CONSUME-消费成长值，ACTIVITY-活跃成长值，默认TOTAL | "TOTAL" |
| period | String | Query | 否 | 统计周期：ALL-全部，MONTH-本月，WEEK-本周，默认ALL | "ALL" |
| limit | Integer | Query | 否 | 返回记录数，默认100，最大1000                                   | 100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| list | Array | 排行列表 | - |
| list[].rank | Integer | 排名 | 1 |
| list[].userId | String | 用户ID | "user_123456" |
| list[].userName | String | 用户名称 | "张三" |
| list[].growthValue | Long | 成长值数量 | 10000 |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "rank": 1,
        "userId": "user_123456",
        "userName": "张三",
        "growthValue": 10000
      },
      {
        "rank": 2,
        "userId": "user_123457",
        "userName": "李四",
        "growthValue": 9500
      },
      {
        "rank": 3,
        "userId": "user_123458",
        "userName": "王五",
        "growthValue": 9000
      }
    ]
  }
}
```

## 3. Web管理端API

### 3.1 成长值规则管理

#### 3.1.1 创建成长值规则

**基本信息**
- **接口路径**：`POST /admin/api/v1/growth/rule`
- **接口描述**：创建新的成长值规则
- **访问权限**：需要管理员权限
- **限流规则**：20次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                                 | 示例 |
| ------ | ---- | ---- | ---- |------------------------------------|-----|
| accountCode | String | Body | 是 | 账户代码                               | "MALL_APP" |
| ruleCode | String | Body | 是 | 规则代码，唯一标识                          | "NEW_PURCHASE_RULE" |
| ruleName | String | Body | 是 | 规则名称                               | "新用户购买奖励" |
| businessType | String | Body | 是 | 业务类型                               | "PURCHASE" |
| description | String | Body | 否 | 规则描述                               | "新用户首次购买获得额外成长值" |
| baseGrowth | Long | Body | 是 | 基础成长值                              | 100 |
| multiplier | BigDecimal | Body | 否 | 倍率，默认1.0                           | 1.5 |
| dailyLimit | Long | Body | 否 | 每日限制                               | 1000 |
| monthlyLimit | Long | Body | 否 | 每月限制                               | 10000 |
| ruleExpression | String | Body | 否 | 规则表达式                              | "user.isNewUser == true" |
| priority | Integer | Body | 否 | 优先级，数字越大优先级越高                      | 10 |
| status | String | Body | 否 | 状态：ACTIVE-生效，INACTIVE-未生效，默认ACTIVE | "ACTIVE" |
| startTime | String | Body | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss        | "2024-01-01 00:00:00" |
| endTime | String | Body | 否 | 结束时间，格式：yyyy-MM-dd HH:mm:ss        | "2024-12-31 23:59:59" |

**请求示例**

```json
{
  "accountCode": "MALL_APP",
  "ruleCode": "NEW_PURCHASE_RULE",
  "ruleName": "新用户购买奖励",
  "businessType": "PURCHASE",
  "description": "新用户首次购买获得额外成长值",
  "baseGrowth": 100,
  "multiplier": 1.5,
  "dailyLimit": 1000,
  "monthlyLimit": 10000,
  "ruleExpression": "user.isNewUser == true && order.amount > 100",
  "priority": 10,
  "status": "ACTIVE",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| id | Long | 规则ID | 1001 |
| ruleCode | String | 规则代码 | "NEW_PURCHASE_RULE" |
| ruleName | String | 规则名称 | "新用户购买奖励" |
| createTime | String | 创建时间 | "2024-01-01 10:00:00" |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1001,
    "ruleCode": "NEW_PURCHASE_RULE",
    "ruleName": "新用户购买奖励",
    "createTime": "2024-01-01 10:00:00"
  }
}
```

#### 3.1.2 更新成长值规则

**基本信息**
- **接口路径**：`PUT /admin/api/v1/growth/rule/{id}`
- **接口描述**：更新现有成长值规则
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 |
| ------ | ---- | ---- | ---- | ---- | ---- |
| id | Long | Path | 是 | 规则ID | 1001 |
| ruleName | String | Body | 否 | 规则名称 | "新用户购买奖励V2" |
| description | String | Body | 否 | 规则描述 | "新用户首次购买获得额外成长值，升级版" |
| baseGrowth | Long | Body | 否 | 基础成长值 | 120 |
| multiplier | BigDecimal | Body | 否 | 倍率 | 2.0 |
| dailyLimit | Long | Body | 否 | 每日限制 | 1200 |
| monthlyLimit | Long | Body | 否 | 每月限制 | 12000 |
| ruleExpression | String | Body | 否 | 规则表达式 | "user.isNewUser == true && order.amount > 200" |
| priority | Integer | Body | 否 | 优先级 | 15 |
| status | String | Body | 否 | 状态：ACTIVE-生效，INACTIVE-未生效 | "ACTIVE" |
| startTime | String | Body | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss | "2024-02-01 00:00:00" |
| endTime | String | Body | 否 | 结束时间，格式：yyyy-MM-dd HH:mm:ss | "2024-12-31 23:59:59" |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| updateTime | String | 更新时间 | "2024-01-02 15:30:00" |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "updateTime": "2024-01-02 15:30:00"
  }
}
```

#### 3.1.3 删除成长值规则

**基本信息**
- **接口路径**：`DELETE /admin/api/v1/growth/rule/{id}`
- **接口描述**：删除成长值规则
- **访问权限**：需要管理员权限
- **限流规则**：10次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 |
| ------ | ---- | ---- | ---- | ---- | ---- |
| id | Long | Path | 是 | 规则ID | 1001 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true
  }
}
```

#### 3.1.4 查询成长值规则列表

**基本信息**
- **接口路径**：`GET /admin/api/v1/growth/rule`
- **接口描述**：查询成长值规则列表
- **访问权限**：需要管理员权限
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                        | 示例 |
| ------ | ---- | ---- | ---- |---------------------------|-----|
| accountCode | String | Query | 是 | 账户代码                      | "MALL_APP" |
| ruleCode | String | Query | 否 | 规则代码                      | "PURCHASE_RULE" |
| ruleName | String | Query | 否 | 规则名称，支持模糊查询               | "购买" |
| businessType | String | Query | 否 | 业务类型                      | "PURCHASE" |
| status | String | Query | 否 | 状态：ACTIVE-生效，INACTIVE-未生效 | "ACTIVE" |
| pageNum | Integer | Query | 否 | 页码，默认1                    | 1 |
| pageSize | Integer | Query | 否 | 每页记录数，默认10                | 10 |

**响应参数**

| 参数名 | 类型 | 描述    | 示例 |
| ------ | ---- |-------|-----|
| total | Integer | 总记录数  | 100 |
| list | Array | 规则列表  | - |
| list[].id | Long | 规则ID  | 1001 |
| list[].accountCode | String | 账户代码  | "MALL_APP" |
| list[].ruleCode | String | 规则代码  | "PURCHASE_RULE" |
| list[].ruleName | String | 规则名称  | "购买商品成长值规则" |
| list[].businessType | String | 业务类型  | "PURCHASE" |
| list[].description | String | 规则描述  | "用户购买商品获得成长值" |
| list[].baseGrowth | Long | 基础成长值 | 100 |
| list[].multiplier | BigDecimal | 倍率    | 1.5 |
| list[].dailyLimit | Long | 每日限制  | 1000 |
| list[].monthlyLimit | Long | 每月限制  | 10000 |
| list[].ruleExpression | String | 规则表达式 | "order.amount > 100" |
| list[].priority | Integer | 优先级   | 10 |
| list[].status | String | 状态    | "ACTIVE" |
| list[].startTime | String | 开始时间  | "2024-01-01 00:00:00" |
| list[].endTime | String | 结束时间  | "2024-12-31 23:59:59" |
| list[].createBy | String | 创建人   | "admin" |
| list[].createTime | String | 创建时间  | "2024-01-01 10:00:00" |
| list[].updateTime | String | 更新时间  | "2024-01-01 10:00:00" |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1001,
        "accountCode": "MALL_APP",
        "ruleCode": "PURCHASE_RULE",
        "ruleName": "购买商品成长值规则",
        "businessType": "PURCHASE",
        "description": "用户购买商品获得成长值",
        "baseGrowth": 100,
        "multiplier": 1.5,
        "dailyLimit": 1000,
        "monthlyLimit": 10000,
        "ruleExpression": "order.amount > 100",
        "priority": 10,
        "status": "ACTIVE",
        "startTime": "2024-01-01 00:00:00",
        "endTime": "2024-12-31 23:59:59",
        "createBy": "admin",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

#### 3.1.5 测试成长值规则

**基本信息**
- **接口路径**：`POST /admin/api/v1/growth/rule/test`
- **接口描述**：测试成长值规则的执行效果，验证规则表达式的正确性和计算结果
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名                 | 类型 | 位置 | 必填 | 描述                | 示例 |
|---------------------| ---- | ---- | ---- |-------------------|-----|
| ruleId              | Long | Body | 否 | 规则ID，测试已存在规则时使用   | 1001 |
| ruleExpression      | String | Body | 否 | 规则表达式，测试新规则时使用    | "user.orderCount >= 5 && order.amount > 100" |
| params              | Object | Body | 是 | 测试参数，模拟业务场景数据     | {} |
| params.userId       | String | Body | 否 | 用户ID              | "user_123456" |
| params.accountCode  | String | Body | 是 | 账户代码              | "MALL_APP" |
| params.businessData | Object | Body | 否 | 业务数据，根据规则类型提供不同数据 | {} |

**请求示例**

```json
{
  "ruleId": 1001,
  "ruleExpression": "user.orderCount >= 5 && order.amount > 100 ? order.amount * 0.1 : 0",
  "params": {
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "businessData": {
      "order": {
        "amount": 500,
        "productCount": 3
      },
      "user": {
        "orderCount": 8,
        "memberLevel": "GOLD",
        "isNewUser": false
      }
    }
  }
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 测试是否成功 | true |
| result | Object | 测试结果 | - |
| result.growthValue | Long | 计算得出的成长值 | 50 |
| result.executionTime | Long | 执行时间（毫秒） | 12 |
| result.expressionResult | String | 表达式执行结果详情 | "user.orderCount(8) >= 5 && order.amount(500) > 100 ? order.amount(500) * 0.1 : 0 = 50" |
| result.variables | Object | 变量解析结果 | {} |
| result.errorMessage | String | 错误信息（如果有） | null |
| ruleInfo | Object | 规则信息 | - |
| ruleInfo.ruleCode | String | 规则代码 | "ORDER_REWARD" |
| ruleInfo.ruleName | String | 规则名称 | "订单奖励成长值" |
| ruleInfo.ruleExpression | String | 规则表达式 | "user.orderCount >= 5 && order.amount > 100 ? order.amount * 0.1 : 0" |

**响应示例**

```json
{
  "code": 0,
  "message": "测试成功",
  "data": {
    "success": true,
    "result": {
      "growthValue": 50,
      "executionTime": 12,
      "expressionResult": "user.orderCount(8) >= 5 && order.amount(500) > 100 ? order.amount(500) * 0.1 : 0 = 50",
      "variables": {
        "user.orderCount": 8,
        "user.memberLevel": "GOLD",
        "user.isNewUser": false,
        "order.amount": 500,
        "order.productCount": 3
      },
      "errorMessage": null
    },
    "ruleInfo": {
      "ruleCode": "ORDER_REWARD",
      "ruleName": "订单奖励成长值",
      "ruleExpression": "user.orderCount >= 5 && order.amount > 100 ? order.amount * 0.1 : 0"
    }
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 3101 | 规则不存在 | 确认规则ID是否正确 |
| 3102 | 规则表达式语法错误 | 检查表达式语法 |
| 3103 | 测试参数不完整 | 补充必要的测试参数 |
| 3104 | 变量解析失败 | 检查变量名称和数据结构 |
| 3105 | 表达式执行异常 | 检查表达式逻辑和数据类型 |

### 3.2 成长值统计分析

#### 3.2.1 成长值获取统计

**基本信息**
- **接口路径**：`GET /admin/api/v1/growth/stat/earn`
- **接口描述**：查询成长值获取统计数据
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                          | 示例 |
| ------ | ---- | ---- | ---- |-----------------------------|-----|
| accountCode | String | Query | 是 | 账户代码                        | "MALL_APP" |
| startDate | String | Query | 是 | 开始日期，格式：yyyy-MM-dd          | "2024-01-01" |
| endDate | String | Query | 是 | 结束日期，格式：yyyy-MM-dd          | "2024-01-31" |
| groupBy | String | Query | 否 | 分组方式：DAY, WEEK, MONTH，默认DAY | "DAY" |
| businessType | String | Query | 否 | 业务类型                        | "PURCHASE" |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| totalGrowth | Long | 总成长值获取量 | 100000 |
| userCount | Integer | 涉及用户数 | 5000 |
| detail | Array | 统计明细 | - |
| detail[].date | String | 日期 | "2024-01-01" |
| detail[].growthValue | Long | 成长值数量 | 10000 |
| detail[].userCount | Integer | 用户数量 | 1000 |
| businessTypeDistribution | Array | 业务类型分布 | - |
| businessTypeDistribution[].businessType | String | 业务类型 | "PURCHASE" |
| businessTypeDistribution[].growthValue | Long | 成长值数量 | 60000 |
| businessTypeDistribution[].percentage | Number | 百分比 | 60.0 |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalGrowth": 100000,
    "userCount": 5000,
    "detail": [
      {
        "date": "2024-01-01",
        "growthValue": 10000,
        "userCount": 1000
      },
      {
        "date": "2024-01-02",
        "growthValue": 12000,
        "userCount": 1200
      }
    ],
    "businessTypeDistribution": [
      {
        "businessType": "PURCHASE",
        "growthValue": 60000,
        "percentage": 60.0
      },
      {
        "businessType": "ACTIVITY",
        "growthValue": 20000,
        "percentage": 20.0
      },
      {
        "businessType": "SOCIAL",
        "growthValue": 10000,
        "percentage": 10.0
      },
      {
        "businessType": "TASK",
        "growthValue": 10000,
        "percentage": 10.0
      }
    ]
  }
}
```

#### 3.2.2 成长值分布统计

**基本信息**
- **接口路径**：`GET /admin/api/v1/growth/stat/distribution`
- **接口描述**：查询成长值分布统计数据
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                          | 示例 |
| ------ | ---- | ---- | ---- |-----------------------------|-----|
| accountCode | String | Query | 是 | 账户代码                        | "MALL_APP" |
| distributionType | String | Query | 否 | 分布类型：LEVEL-等级分布，RANGE-区间分布，默认LEVEL | "LEVEL" |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| totalUsers | Integer | 总用户数 | 10000 |
| distribution | Array | 分布明细 | - |
| distribution[].range | String | 范围 | "0-1000" |
| distribution[].userCount | Integer | 用户数量 | 3000 |
| distribution[].percentage | Number | 百分比 | 30.0 |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "totalUsers": 10000,
    "distribution": [
      {
        "range": "0-1000",
        "userCount": 3000,
        "percentage": 30.0
      },
      {
        "range": "1001-5000",
        "userCount": 4000,
        "percentage": 40.0
      },
      {
        "range": "5001-10000",
        "userCount": 2000,
        "percentage": 20.0
      },
      {
        "range": "10000+",
        "userCount": 1000,
        "percentage": 10.0
      }
    ]
  }
}
```

#### 3.2.3 用户成长值排行

**基本信息**
- **接口路径**：`GET /admin/api/v1/growth/stat/ranking`
- **接口描述**：查询用户成长值排行
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述                                                   | 示例 |
| ------ | ---- | ---- | ---- |------------------------------------------------------|-----|
| accountCode | String | Query | 是 | 账户代码                                                 | "MALL_APP" |
| type | String | Query | 否 | 排行类型：TOTAL-总成长值，CONSUME-消费成长值，ACTIVITY-活跃成长值，默认TOTAL | "TOTAL" |
| period | String | Query | 否 | 统计周期：ALL-全部，MONTH-本月，WEEK-本周，默认ALL | "ALL" |
| limit | Integer | Query | 否 | 返回记录数，默认100，最大1000                                   | 100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| list | Array | 排行列表 | - |
| list[].rank | Integer | 排名 | 1 |
| list[].userId | String | 用户ID | "user_123456" |
| list[].userName | String | 用户名称 | "张三" |
| list[].growthValue | Long | 成长值数量 | 10000 |

**响应示例**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "rank": 1,
        "userId": "user_123456",
        "userName": "张三",
        "growthValue": 10000
      },
      {
        "rank": 2,
        "userId": "user_123457",
        "userName": "李四",
        "growthValue": 9500
      },
      {
        "rank": 3,
        "userId": "user_123458",
        "userName": "王五",
        "growthValue": 9000
      }
    ]
  }
}
```

### 3.3 成长值账户管理

#### 3.3.1 查询成长值账户列表

**基本信息**
- **接口路径**：`GET /admin/api/v1/growth/accounts`
- **接口描述**：查询品牌下所有成长值账户配置列表
- **访问权限**：需要管理员权限
- **限流规则**：100次/分钟

**请求参数**

| 参数名         | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|-------------|------|------|------|------|------|------|
| brandCode   | String | Query | 是 | 品牌ID | "brand123" | 长度1-64 |
| accountCode | String | Query | 否 | 账户名称 | "MALL_APP" | 长度1-100，模糊匹配 |
| status      | String | Query | 否 | 账户状态 | "ACTIVE" | ACTIVE/INACTIVE |
| pageNum     | Integer | Query | 否 | 页码 | 1 | >=1 |
| pageSize    | Integer | Query | 否 | 每页大小 | 20 | 1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Integer | 总数量 | 50 |
| data.list | Array | 账户列表 | - |
| data.list[].accountCode | String | 账户ID | "acc123" |
| data.list[].accountName | String | 账户名称 | "MALL_APP" |
| data.list[].accountType | String | 账户类型 | "BUSINESS" |
| data.list[].status | String | 账户状态 | "ACTIVE" |
| data.list[].description | String | 账户描述 | "商城成长值账户" |
| data.list[].totalUsers | Integer | 用户总数 | 10000 |
| data.list[].totalGrowth | Long | 成长值总量 | 5000000 |
| data.list[].createTime | String | 创建时间 | "2024-01-01 10:00:00" |
| data.list[].updateTime | String | 更新时间 | "2024-01-15 15:30:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.2 查询成长值账户详情

**基本信息**
- **接口路径**：`GET /admin/api/v1/growth/account/{accountCode}`
- **接口描述**：查询指定成长值账户的详细配置信息
- **访问权限**：需要管理员权限
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Path | 是 | 账户ID | "acc123" | 长度1-64 |
| statistics | Boolean | Query | 否 | 是否统计数据 | true | - |

**响应参数**

| 参数名                             | 类型      | 描述      | 示例 |
|---------------------------------|---------|---------|------|
| code                            | Integer | 响应码     | 200 |
| message                         | String  | 响应消息    | "success" |
| data                            | Object  | 响应数据    | - |
| data.accountCode                | String  | 账户ID    | "acc123" |
| data.accountName                | String  | 账户名称    | "MALL_APP" |
| data.accountType                | String  | 账户类型    | "BUSINESS" |
| data.brandCode                  | String  | 品牌ID    | "brand123" |
| data.status                     | String  | 账户状态    | "ACTIVE" |
| data.description                | String  | 账户描述    | "商城成长值账户" |
| data.basicConfig                | JSON    | 账户基础配置  | - |
| data.riskControlConfig          | JSON    | 账户风险控制配置 | - |
| data.extensionConfig            | JSON    | 账户基础配置  | - |
| data.statistics                 | Object  | 统计信息    | - |
| data.statistics.totalUsers      | Integer | 用户总数    | 10000 |
| data.statistics.totalGrowth     | Long    | 成长值总量   | 5000000 |
| data.statistics.consumeGrowth   | Long    | 消费成长值   | 2000000 |
| data.statistics.activityGrowth  | Long    | 活跃成长值   | 1500000 |
| data.statistics.socialGrowth    | Long    | 社交成长值   | 800000 |
| data.statistics.taskGrowth      | Long    | 任务成长值   | 700000 |
| data.createTime                 | String  | 创建时间    | "2024-01-01 10:00:00" |
| data.updateTime                 | String  | 更新时间    | "2024-01-15 15:30:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式 |
| 404001 | 成长值账户不存在 | 检查账户ID是否正确 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.3 创建成长值账户

**基本信息**
- **接口路径**：`POST /admin/api/v1/growth/account`
- **接口描述**：创建新的成长值账户配置
- **访问权限**：需要管理员权限
- **限流规则**：20次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名               | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|-------------------|------|------|------|------|------|------|
| accountCode       | String | Body | 是 | 账户代码 | "MALL_APP" | 长度1-100，同品牌下唯一 |
| accountName       | String | Body | 是 | 账户名称 | "成长值商城" | 长度1-100，同品牌下唯一 |
| accountType       | String | Body | 是 | 账户类型 | "BUSINESS" | BUSINESS/PERSONAL |
| brandCode         | String | Body | 是 | 品牌ID | "brand123" | 长度1-64 |
| description       | String | Body | 否 | 账户描述 | "商城成长值账户" | 长度0-500 |
| basicConfig       | JSON | Body | 否 | 账户基础配置 | - | - |
| riskControlConfig | JSON | Body | 否 | 风险控制配置 | - | - |
| extensionConfig   | JSON | Body | 否 | 扩展配置 | - | - |
| businessId        | String | Body | 否 | 业务单号 | "biz20240101001" | 长度1-64，幂等标识 |
| operator          | String | Body | 是 | 操作人 | "admin" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.accountCode | String | 账户ID | "acc123" |
| data.accountName | String | 账户名称 | "MALL_APP" |
| data.accountType | String | 账户类型 | "BUSINESS" |
| data.brandCode | String | 品牌ID | "brand123" |
| data.status | String | 账户状态 | "ACTIVE" |
| data.createTime | String | 创建时间 | "2024-01-01 10:00:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 400002 | 账户名称已存在 | 使用不同的账户名称 |
| 400003 | 业务单号重复 | 使用新的业务单号 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.4 更新成长值账户

**基本信息**
- **接口路径**：`PUT /admin/api/v1/growth/account/{accountCode}`
- **接口描述**：更新成长值账户的配置信息
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Path | 是 | 账户ID | "acc123" | 长度1-64 |
| accountName | String | Body | 否 | 账户名称 | "MALL_APP_V2" | 长度1-100，同品牌下唯一 |
| status | String | Body | 否 | 账户状态 | "ACTIVE" | ACTIVE/INACTIVE |
| description | String | Body | 否 | 账户描述 | "商城成长值账户升级版" | 长度0-500 |
| basicConfig | JSON | Body | 否 | 账户基础配置 | - | - |
| riskControlConfig | JSON | Body | 否 | 风险控制配置 | - | - |
| extensionConfig | JSON | Body | 否 | 扩展配置 | - | - |
| businessId | String | Body | 否 | 业务单号 | "biz20240101002" | 长度1-64，幂等标识 |
| operator | String | Body | 是 | 操作人 | "admin" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.accountCode | String | 账户ID | "acc123" |
| data.accountName | String | 账户名称 | "MALL_APP_V2" |
| data.accountType | String | 账户类型 | "BUSINESS" |
| data.status | String | 账户状态 | "ACTIVE" |
| data.updateTime | String | 更新时间 | "2024-01-15 15:30:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 400002 | 账户名称已存在 | 使用不同的账户名称 |
| 400003 | 业务单号重复 | 使用新的业务单号 |
| 404001 | 成长值账户不存在 | 检查账户ID是否正确 |
| 400004 | 账户状态不允许修改 | 检查当前账户状态 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.5 删除成长值账户

**基本信息**
- **接口路径**：`DELETE /admin/api/v1/growth/account/{accountCode}`
- **接口描述**：删除成长值账户配置（软删除）
- **访问权限**：需要管理员权限
- **限流规则**：10次/分钟
- **幂等性**：支持

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Path | 是 | 账户ID | "acc123" | 长度1-64 |
| operator | String | Body | 是 | 操作人 | "admin" | 长度1-32 |
| reason | String | Body | 否 | 删除原因 | "业务调整" | 长度0-200 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.accountCode | String | 账户ID | "acc123" |
| data.deleteTime | String | 删除时间 | "2024-01-15 16:00:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 404001 | 成长值账户不存在 | 检查账户ID是否正确 |
| 400005 | 账户正在使用中 | 存在用户使用该账户，无法删除 |
| 400006 | 系统默认账户无法删除 | 系统默认账户不允许删除 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

## 4. 错误码定义

| 错误码 | 描述 | 处理建议 |
| ------ | ---- | -------- |
| 0 | 成功 | - |
| 1001 | 参数错误 | 检查请求参数是否符合要求 |
| 1002 | 用户不存在 | 确认用户ID是否正确 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数 |
| 2001 | 业务数据无效 | 检查业务数据格式和内容 |
| 2002 | 用户成长值账户异常 | 检查用户账户状态 |
| 2003 | 业务单号重复 | 使用新的业务单号 |
| 2004 | 业务类型无效 | 确认业务类型是否正确 |
| 2005 | 规则匹配失败 | 检查是否有适用的规则 |
| 2006 | 成长值计算失败 | 检查规则配置和业务数据 |
| 2101 | 调整数量无效 | 检查调整数量是否为正数 |
| 2102 | 用户成长值账户异常 | 检查用户账户状态 |
| 2103 | 业务单号重复 | 使用新的业务单号 |
| 2104 | 调整类型无效 | 确认调整类型是否正确 |
| 2105 | 权限不足 | 确认是否有调整权限 |
| 2201 | 事务不存在 | 确认事务ID是否正确 |
| 2202 | 参数不完整 | 至少提供一个查询条件 |
| 3001 | 规则不存在 | 检查规则代码是否正确 |
| 3002 | 规则已失效 | 检查规则状态和有效期 |
| 3101 | 规则不存在 | 确认规则ID是否正确 |
| 3102 | 规则表达式语法错误 | 检查表达式语法 |
| 3103 | 测试参数不完整 | 补充必要的测试参数 |
| 3104 | 变量解析失败 | 检查变量名称和数据结构 |
| 3105 | 表达式执行异常 | 检查表达式逻辑和数据类型 |
| 4001 | 业务单号重复 | 检查业务单号是否已使用 |
| 5001 | 系统内部错误 | 联系系统管理员 |
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 400002 | 账户名称已存在 | 使用不同的账户名称 |
| 400003 | 业务单号重复 | 使用新的业务单号 |
| 400004 | 账户状态不允许修改 | 检查当前账户状态 |
| 400005 | 账户正在使用中 | 存在用户使用该账户，无法删除 |
| 400006 | 系统默认账户无法删除 | 系统默认账户不允许删除 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 404001 | 成长值账户不存在 | 检查账户ID是否正确 |
| 500001 | 系统内部错误 | 联系技术支持 |