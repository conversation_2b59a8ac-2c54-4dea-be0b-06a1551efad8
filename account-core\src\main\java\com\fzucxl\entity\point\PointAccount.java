package com.fzucxl.entity.point;

import com.fzucxl.open.base.AccountStatus;
import com.fzucxl.open.base.AccountType;
import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 积分账户实体
 * 
 * <AUTHOR>
 */
@MappedEntity("point_account")
@Data
public class PointAccount {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private AccountType accountType;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 账户状态
     */
    private AccountStatus status = AccountStatus.ACTIVE;

    /**
     * 账户描述
     */
    private String description;


    /**
     * 基础配置
     */
    private String basicConfig;

    /**
     * 风控配置
     */
    private String riskControlConfig;

    /**
     * 扩展配置
     */
    private String extensionConfig;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 版本号
     */
    private Integer version = 0;
}