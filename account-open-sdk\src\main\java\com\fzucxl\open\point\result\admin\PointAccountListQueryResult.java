﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分账户列表结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccountListQueryResult extends Extensible {    
    /**
     * 总记录数
     */
    private Integer total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer pages;    
    /**
     * 积分账户列表
     */
    private java.util.List<PointAccount> list;}
