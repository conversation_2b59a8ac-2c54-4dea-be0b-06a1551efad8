package com.fzucxl.point.rule.dto;

import com.fzucxl.point.rule.model.FilterCondition;
import com.fzucxl.point.rule.model.TimeRangeConfig;
import io.micronaut.core.annotation.Introspected;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 更新属性表达式请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Data
public class UpdateAttributeRequest {
    
    private String attributeName;
    private String description;
    private String dataSource;
    private String targetTable;
    private String aggregateFunction;
    private String aggregateField;
    private List<FilterCondition> filterConditions;
    private TimeRangeConfig timeRangeConfig;
    private Integer cacheTtl;
    private BigDecimal defaultValue;
    private String updatedBy;

}