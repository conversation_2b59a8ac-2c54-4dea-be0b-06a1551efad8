package com.fzucxl.event.repository;

import com.fzucxl.entity.event.BusinessEventField;
import io.micronaut.data.annotation.Repository;
import io.micronaut.data.jdbc.annotation.JdbcRepository;
import io.micronaut.data.model.query.builder.sql.Dialect;
import io.micronaut.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;

/**
 * 业务事件字段Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@JdbcRepository(dialect = Dialect.MYSQL)
public interface BusinessEventFieldRepository extends CrudRepository<BusinessEventField, Long> {
    
    /**
     * 根据事件ID查找字段列表
     */
    List<BusinessEventField> findByEventIdAndStatusOrderBySortOrder(Long eventId, String status);
    
    /**
     * 根据事件ID查找所有字段
     */
    List<BusinessEventField> findByEventIdOrderBySortOrder(Long eventId);
    
    /**
     * 根据事件ID和字段编码查找字段
     */
    Optional<BusinessEventField> findByEventIdAndFieldCode(Long eventId, String fieldCode);
    
    /**
     * 根据字段编码查找字段列表
     */
    List<BusinessEventField> findByFieldCode(String fieldCode);
    
    /**
     * 根据字段类型查找字段列表
     */
    List<BusinessEventField> findByFieldTypeAndStatus(String fieldType, String status);
    
    /**
     * 检查字段编码在指定事件中是否存在
     */
    boolean existsByEventIdAndFieldCode(Long eventId, String fieldCode);
    
    /**
     * 根据事件ID删除字段
     */
    void deleteByEventId(Long eventId);
    
    /**
     * 统计事件的字段数量
     */
    long countByEventIdAndStatus(Long eventId, String status);
}