package com.fzucxl.entity.level;

import com.fzucxl.open.base.TransactionStatus;
import com.fzucxl.open.base.level.LevelTransactionType;
import io.micronaut.data.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("level_transaction")
@Data
public class LevelTransaction {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 交易类型
     */
    private LevelTransactionType transactionType;

    /**
     * 来源等级
     */
    private Integer fromLevel;

    /**
     * 目标等级
     */

    private Integer toLevel;

    /**
     * 交易状态
     */
    private TransactionStatus status = TransactionStatus.PENDING;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 输入数据
     */
    private String inputData;

    /**
     * 版本号
     */
    private Integer version = 0;
}
