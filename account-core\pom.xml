<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>account-core</artifactId>
    <version>0.1.0</version>
    <packaging>${packaging}</packaging>

    <parent>
        <groupId>com.fzucxl</groupId>
        <artifactId>fast-account</artifactId>
        <version>0.1.0</version>
    </parent>

    <properties>
        <packaging>jar</packaging>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <micronaut.version>4.9.2</micronaut.version>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>com.fzucxl.aot.generated</micronaut.aot.packageName>
        <micronaut.runtime>netty</micronaut.runtime>
        <micronaut.test.resources.enabled>false</micronaut.test.resources.enabled>
        <exec.mainClass>com.fzucxl.Application</exec.mainClass>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.fzucxl</groupId>
            <artifactId>account-open-sdk</artifactId>
            <version>0.1.0</version>
        </dependency>

        <!-- Micronaut Core -->
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-inject</artifactId>
        </dependency>

        <!-- Micronaut HTTP -->
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-server-netty</artifactId>
        </dependency>

        <!-- Micronaut Validation -->
        <dependency>
            <groupId>io.micronaut.validation</groupId>
            <artifactId>micronaut-validation</artifactId>
        </dependency>

        <!-- Jakarta Validation -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <!-- Jakarta Inject -->
        <dependency>
            <groupId>jakarta.inject</groupId>
            <artifactId>jakarta.inject-api</artifactId>
            <version>2.0.1</version>
        </dependency>

        <!-- Jakarta Transaction -->
        <dependency>
            <groupId>jakarta.transaction</groupId>
            <artifactId>jakarta.transaction-api</artifactId>
        </dependency>

        <!-- MySQL Driver -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <!-- Jackson for JSON -->
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-jackson-databind</artifactId>
        </dependency>

        <!-- Cache -->
        <dependency>
            <groupId>io.micronaut.cache</groupId>
            <artifactId>micronaut-cache-caffeine</artifactId>
        </dependency>

        <!-- SLF4J Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- Logback -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

    </dependencies>

</project>
