package com.fzucxl.point.rule.model;

/**
 * 过滤条件模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FilterCondition {
    
    private String field;
    private String operator;
    private Object value;
    private String logicOperator; // AND, OR
    
    public FilterCondition() {
        this.logicOperator = "AND";
    }
    
    public FilterCondition(String field, String operator, Object value) {
        this.field = field;
        this.operator = operator;
        this.value = value;
        this.logicOperator = "AND";
    }
    
    // Getter and Setter methods
    public String getField() {
        return field;
    }
    
    public void setField(String field) {
        this.field = field;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
    
    public Object getValue() {
        return value;
    }
    
    public void setValue(Object value) {
        this.value = value;
    }
    
    public String getLogicOperator() {
        return logicOperator;
    }
    
    public void setLogicOperator(String logicOperator) {
        this.logicOperator = logicOperator;
    }
    
    @Override
    public String toString() {
        return "FilterCondition{" +
                "field='" + field + '\'' +
                ", operator='" + operator + '\'' +
                ", value=" + value +
                ", logicOperator='" + logicOperator + '\'' +
                '}';
    }
}