package com.fzucxl.entity.growth;

import com.fzucxl.open.base.AccountStatus;
import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.event.PrePersist;
import io.micronaut.data.annotation.event.PreUpdate;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("user_growth")
@Data
public class UserGrowth {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户代码
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;

    /**
     * 总成长值
     */
    private Long totalGrowth;

    /**
     * 消费成长值
     */
    private Long consumeGrowth;

    /**
     * 活跃成长值
     */
    private Long activityGrowth;

    /**
     * 社交成长值
     */
    private Long socialGrowth;

    /**
     * 任务成长值
     */
    private Long taskGrowth;
    /**
     * 账户状态：ACTIVE-正常，INACTIVE-停用，FROZEN-冻结，DELETED-已删除
     */
    private AccountStatus status;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private Integer version = 0;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;

        // 初始化成长值为0
        if (this.totalGrowth == null) this.totalGrowth = 0L;
        if (this.consumeGrowth == null) this.consumeGrowth = 0L;
        if (this.activityGrowth == null) this.activityGrowth = 0L;
        if (this.socialGrowth == null) this.socialGrowth = 0L;
        if (this.taskGrowth == null) this.taskGrowth = 0L;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 增加成长值
     */
    public void addGrowth(String businessType, Long growthValue) {
        this.totalGrowth += growthValue;

        switch (businessType.toUpperCase()) {
            case "PURCHASE":
            case "CONSUME":
                this.consumeGrowth += growthValue;
                break;
            case "ACTIVITY":
                this.activityGrowth += growthValue;
                break;
            case "SOCIAL":
                this.socialGrowth += growthValue;
                break;
            case "TASK":
                this.taskGrowth += growthValue;
                break;
        }
    }
}
