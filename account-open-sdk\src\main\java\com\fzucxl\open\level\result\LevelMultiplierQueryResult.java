﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级倍率结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelMultiplierQueryResult extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 等级
     */
    private Integer level;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 倍率列表
     */
    private java.util.List<LevelMultiplier> multiplierList;}
