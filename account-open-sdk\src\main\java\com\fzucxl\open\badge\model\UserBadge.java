﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 用户勋章模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserBadge extends Extensible {    
    /**
     * 用户勋章ID
     */
    private Long userBadgeId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 勋章类型
     */
    private String badgeType;    
    /**
     * 勋章等级
     */
    private Integer badgeLevel;    
    /**
     * 勋章图标URL
     */
    private String iconUrl;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 展示状态
     */
    private String displayStatus;    
    /**
     * 获得时间
     */
    private String obtainTime;    
    /**
     * 过期时间
     */
    private String expireTime;    
    /**
     * 稀有度
     */
    private String rarity;    
    /**
     * 特权描述
     */
    private String privilege;}
