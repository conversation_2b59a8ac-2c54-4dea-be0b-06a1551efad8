﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级计算参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelCalculateParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 当前计算值（积分或成长值）
     */
    private Long currentValue;    
    /**
     * 计算方式：POINT(积分), GROWTH(成长值)
     */
    private String calculationType;    
    /**
     * 交易ID
     */
    private String transactionId;}
