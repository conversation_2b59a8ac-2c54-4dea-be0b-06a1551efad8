﻿package com.fzucxl.open.growth.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建成长值规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleCreateResult extends Extensible {    
    /**
     * 规则ID
     */
    private Long id;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 创建时间
     */
    private String createTime;}
