{"ast": null, "code": "import { getArrowOffsetToken } from '../style/placementArrow';\nexport function getOverflowOptions(placement, arrowOffset, arrowWidth, autoAdjustOverflow) {\n  if (autoAdjustOverflow === false) {\n    return {\n      adjustX: false,\n      adjustY: false\n    };\n  }\n  const overflow = autoAdjustOverflow && typeof autoAdjustOverflow === 'object' ? autoAdjustOverflow : {};\n  const baseOverflow = {};\n  switch (placement) {\n    case 'top':\n    case 'bottom':\n      baseOverflow.shiftX = arrowOffset.arrowOffsetHorizontal * 2 + arrowWidth;\n      baseOverflow.shiftY = true;\n      baseOverflow.adjustY = true;\n      break;\n    case 'left':\n    case 'right':\n      baseOverflow.shiftY = arrowOffset.arrowOffsetVertical * 2 + arrowWidth;\n      baseOverflow.shiftX = true;\n      baseOverflow.adjustX = true;\n      break;\n  }\n  const mergedOverflow = Object.assign(Object.assign({}, baseOverflow), overflow);\n  // Support auto shift\n  if (!mergedOverflow.shiftX) {\n    mergedOverflow.adjustX = true;\n  }\n  if (!mergedOverflow.shiftY) {\n    mergedOverflow.adjustY = true;\n  }\n  return mergedOverflow;\n}\nconst PlacementAlignMap = {\n  left: {\n    points: ['cr', 'cl']\n  },\n  right: {\n    points: ['cl', 'cr']\n  },\n  top: {\n    points: ['bc', 'tc']\n  },\n  bottom: {\n    points: ['tc', 'bc']\n  },\n  topLeft: {\n    points: ['bl', 'tl']\n  },\n  leftTop: {\n    points: ['tr', 'tl']\n  },\n  topRight: {\n    points: ['br', 'tr']\n  },\n  rightTop: {\n    points: ['tl', 'tr']\n  },\n  bottomRight: {\n    points: ['tr', 'br']\n  },\n  rightBottom: {\n    points: ['bl', 'br']\n  },\n  bottomLeft: {\n    points: ['tl', 'bl']\n  },\n  leftBottom: {\n    points: ['br', 'bl']\n  }\n};\nconst ArrowCenterPlacementAlignMap = {\n  topLeft: {\n    points: ['bl', 'tc']\n  },\n  leftTop: {\n    points: ['tr', 'cl']\n  },\n  topRight: {\n    points: ['br', 'tc']\n  },\n  rightTop: {\n    points: ['tl', 'cr']\n  },\n  bottomRight: {\n    points: ['tr', 'bc']\n  },\n  rightBottom: {\n    points: ['bl', 'cr']\n  },\n  bottomLeft: {\n    points: ['tl', 'bc']\n  },\n  leftBottom: {\n    points: ['br', 'cl']\n  }\n};\nconst DisableAutoArrowList = new Set(['topLeft', 'topRight', 'bottomLeft', 'bottomRight', 'leftTop', 'leftBottom', 'rightTop', 'rightBottom']);\nexport default function getPlacements(config) {\n  const {\n    arrowWidth,\n    autoAdjustOverflow,\n    arrowPointAtCenter,\n    offset,\n    borderRadius,\n    visibleFirst\n  } = config;\n  const halfArrowWidth = arrowWidth / 2;\n  const placementMap = {};\n  // Dynamic offset\n  const arrowOffset = getArrowOffsetToken({\n    contentRadius: borderRadius,\n    limitVerticalRadius: true\n  });\n  Object.keys(PlacementAlignMap).forEach(key => {\n    const template = arrowPointAtCenter && ArrowCenterPlacementAlignMap[key] || PlacementAlignMap[key];\n    const placementInfo = Object.assign(Object.assign({}, template), {\n      offset: [0, 0],\n      dynamicInset: true\n    });\n    placementMap[key] = placementInfo;\n    // Disable autoArrow since design is fixed position\n    if (DisableAutoArrowList.has(key)) {\n      placementInfo.autoArrow = false;\n    }\n    // Static offset\n    switch (key) {\n      case 'top':\n      case 'topLeft':\n      case 'topRight':\n        placementInfo.offset[1] = -halfArrowWidth - offset;\n        break;\n      case 'bottom':\n      case 'bottomLeft':\n      case 'bottomRight':\n        placementInfo.offset[1] = halfArrowWidth + offset;\n        break;\n      case 'left':\n      case 'leftTop':\n      case 'leftBottom':\n        placementInfo.offset[0] = -halfArrowWidth - offset;\n        break;\n      case 'right':\n      case 'rightTop':\n      case 'rightBottom':\n        placementInfo.offset[0] = halfArrowWidth + offset;\n        break;\n    }\n    if (arrowPointAtCenter) {\n      switch (key) {\n        case 'topLeft':\n        case 'bottomLeft':\n          placementInfo.offset[0] = -arrowOffset.arrowOffsetHorizontal - halfArrowWidth;\n          break;\n        case 'topRight':\n        case 'bottomRight':\n          placementInfo.offset[0] = arrowOffset.arrowOffsetHorizontal + halfArrowWidth;\n          break;\n        case 'leftTop':\n        case 'rightTop':\n          placementInfo.offset[1] = -arrowOffset.arrowOffsetHorizontal * 2 + halfArrowWidth;\n          break;\n        case 'leftBottom':\n        case 'rightBottom':\n          placementInfo.offset[1] = arrowOffset.arrowOffsetHorizontal * 2 - halfArrowWidth;\n          break;\n      }\n    }\n    // Overflow\n    placementInfo.overflow = getOverflowOptions(key, arrowOffset, arrowWidth, autoAdjustOverflow);\n    // VisibleFirst\n    if (visibleFirst) {\n      placementInfo.htmlRegion = 'visibleFirst';\n    }\n  });\n  return placementMap;\n}", "map": {"version": 3, "names": ["getArrowOffsetToken", "getOverflowOptions", "placement", "arrowOffset", "arrow<PERSON>idth", "autoAdjustOverflow", "adjustX", "adjustY", "overflow", "baseOverflow", "shiftX", "arrowOffsetHorizontal", "shiftY", "arrowOffsetVertical", "mergedOverflow", "Object", "assign", "PlacementAlignMap", "left", "points", "right", "top", "bottom", "topLeft", "leftTop", "topRight", "rightTop", "bottomRight", "rightBottom", "bottomLeft", "leftBottom", "ArrowCenterPlacementAlignMap", "DisableAutoArrowList", "Set", "getPlacements", "config", "arrowPointAtCenter", "offset", "borderRadius", "visibleFirst", "halfArrowWidth", "placementMap", "contentRadius", "limitVerticalRadius", "keys", "for<PERSON>ach", "key", "template", "placementInfo", "dynamicInset", "has", "autoArrow", "htmlRegion"], "sources": ["D:/code/private/fast-account/loyalty-system-admin/node_modules/antd/es/_util/placements.js"], "sourcesContent": ["import { getArrowOffsetToken } from '../style/placementArrow';\nexport function getOverflowOptions(placement, arrowOffset, arrowWidth, autoAdjustOverflow) {\n  if (autoAdjustOverflow === false) {\n    return {\n      adjustX: false,\n      adjustY: false\n    };\n  }\n  const overflow = autoAdjustOverflow && typeof autoAdjustOverflow === 'object' ? autoAdjustOverflow : {};\n  const baseOverflow = {};\n  switch (placement) {\n    case 'top':\n    case 'bottom':\n      baseOverflow.shiftX = arrowOffset.arrowOffsetHorizontal * 2 + arrowWidth;\n      baseOverflow.shiftY = true;\n      baseOverflow.adjustY = true;\n      break;\n    case 'left':\n    case 'right':\n      baseOverflow.shiftY = arrowOffset.arrowOffsetVertical * 2 + arrowWidth;\n      baseOverflow.shiftX = true;\n      baseOverflow.adjustX = true;\n      break;\n  }\n  const mergedOverflow = Object.assign(Object.assign({}, baseOverflow), overflow);\n  // Support auto shift\n  if (!mergedOverflow.shiftX) {\n    mergedOverflow.adjustX = true;\n  }\n  if (!mergedOverflow.shiftY) {\n    mergedOverflow.adjustY = true;\n  }\n  return mergedOverflow;\n}\nconst PlacementAlignMap = {\n  left: {\n    points: ['cr', 'cl']\n  },\n  right: {\n    points: ['cl', 'cr']\n  },\n  top: {\n    points: ['bc', 'tc']\n  },\n  bottom: {\n    points: ['tc', 'bc']\n  },\n  topLeft: {\n    points: ['bl', 'tl']\n  },\n  leftTop: {\n    points: ['tr', 'tl']\n  },\n  topRight: {\n    points: ['br', 'tr']\n  },\n  rightTop: {\n    points: ['tl', 'tr']\n  },\n  bottomRight: {\n    points: ['tr', 'br']\n  },\n  rightBottom: {\n    points: ['bl', 'br']\n  },\n  bottomLeft: {\n    points: ['tl', 'bl']\n  },\n  leftBottom: {\n    points: ['br', 'bl']\n  }\n};\nconst ArrowCenterPlacementAlignMap = {\n  topLeft: {\n    points: ['bl', 'tc']\n  },\n  leftTop: {\n    points: ['tr', 'cl']\n  },\n  topRight: {\n    points: ['br', 'tc']\n  },\n  rightTop: {\n    points: ['tl', 'cr']\n  },\n  bottomRight: {\n    points: ['tr', 'bc']\n  },\n  rightBottom: {\n    points: ['bl', 'cr']\n  },\n  bottomLeft: {\n    points: ['tl', 'bc']\n  },\n  leftBottom: {\n    points: ['br', 'cl']\n  }\n};\nconst DisableAutoArrowList = new Set(['topLeft', 'topRight', 'bottomLeft', 'bottomRight', 'leftTop', 'leftBottom', 'rightTop', 'rightBottom']);\nexport default function getPlacements(config) {\n  const {\n    arrowWidth,\n    autoAdjustOverflow,\n    arrowPointAtCenter,\n    offset,\n    borderRadius,\n    visibleFirst\n  } = config;\n  const halfArrowWidth = arrowWidth / 2;\n  const placementMap = {};\n  // Dynamic offset\n  const arrowOffset = getArrowOffsetToken({\n    contentRadius: borderRadius,\n    limitVerticalRadius: true\n  });\n  Object.keys(PlacementAlignMap).forEach(key => {\n    const template = arrowPointAtCenter && ArrowCenterPlacementAlignMap[key] || PlacementAlignMap[key];\n    const placementInfo = Object.assign(Object.assign({}, template), {\n      offset: [0, 0],\n      dynamicInset: true\n    });\n    placementMap[key] = placementInfo;\n    // Disable autoArrow since design is fixed position\n    if (DisableAutoArrowList.has(key)) {\n      placementInfo.autoArrow = false;\n    }\n    // Static offset\n    switch (key) {\n      case 'top':\n      case 'topLeft':\n      case 'topRight':\n        placementInfo.offset[1] = -halfArrowWidth - offset;\n        break;\n      case 'bottom':\n      case 'bottomLeft':\n      case 'bottomRight':\n        placementInfo.offset[1] = halfArrowWidth + offset;\n        break;\n      case 'left':\n      case 'leftTop':\n      case 'leftBottom':\n        placementInfo.offset[0] = -halfArrowWidth - offset;\n        break;\n      case 'right':\n      case 'rightTop':\n      case 'rightBottom':\n        placementInfo.offset[0] = halfArrowWidth + offset;\n        break;\n    }\n    if (arrowPointAtCenter) {\n      switch (key) {\n        case 'topLeft':\n        case 'bottomLeft':\n          placementInfo.offset[0] = -arrowOffset.arrowOffsetHorizontal - halfArrowWidth;\n          break;\n        case 'topRight':\n        case 'bottomRight':\n          placementInfo.offset[0] = arrowOffset.arrowOffsetHorizontal + halfArrowWidth;\n          break;\n        case 'leftTop':\n        case 'rightTop':\n          placementInfo.offset[1] = -arrowOffset.arrowOffsetHorizontal * 2 + halfArrowWidth;\n          break;\n        case 'leftBottom':\n        case 'rightBottom':\n          placementInfo.offset[1] = arrowOffset.arrowOffsetHorizontal * 2 - halfArrowWidth;\n          break;\n      }\n    }\n    // Overflow\n    placementInfo.overflow = getOverflowOptions(key, arrowOffset, arrowWidth, autoAdjustOverflow);\n    // VisibleFirst\n    if (visibleFirst) {\n      placementInfo.htmlRegion = 'visibleFirst';\n    }\n  });\n  return placementMap;\n}"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAO,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,kBAAkB,EAAE;EACzF,IAAIA,kBAAkB,KAAK,KAAK,EAAE;IAChC,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH;EACA,MAAMC,QAAQ,GAAGH,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAG,CAAC,CAAC;EACvG,MAAMI,YAAY,GAAG,CAAC,CAAC;EACvB,QAAQP,SAAS;IACf,KAAK,KAAK;IACV,KAAK,QAAQ;MACXO,YAAY,CAACC,MAAM,GAAGP,WAAW,CAACQ,qBAAqB,GAAG,CAAC,GAAGP,UAAU;MACxEK,YAAY,CAACG,MAAM,GAAG,IAAI;MAC1BH,YAAY,CAACF,OAAO,GAAG,IAAI;MAC3B;IACF,KAAK,MAAM;IACX,KAAK,OAAO;MACVE,YAAY,CAACG,MAAM,GAAGT,WAAW,CAACU,mBAAmB,GAAG,CAAC,GAAGT,UAAU;MACtEK,YAAY,CAACC,MAAM,GAAG,IAAI;MAC1BD,YAAY,CAACH,OAAO,GAAG,IAAI;MAC3B;EACJ;EACA,MAAMQ,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,YAAY,CAAC,EAAED,QAAQ,CAAC;EAC/E;EACA,IAAI,CAACM,cAAc,CAACJ,MAAM,EAAE;IAC1BI,cAAc,CAACR,OAAO,GAAG,IAAI;EAC/B;EACA,IAAI,CAACQ,cAAc,CAACF,MAAM,EAAE;IAC1BE,cAAc,CAACP,OAAO,GAAG,IAAI;EAC/B;EACA,OAAOO,cAAc;AACvB;AACA,MAAMG,iBAAiB,GAAG;EACxBC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDC,KAAK,EAAE;IACLD,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDE,GAAG,EAAE;IACHF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDG,MAAM,EAAE;IACNH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDI,OAAO,EAAE;IACPJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDK,OAAO,EAAE;IACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDM,QAAQ,EAAE;IACRN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDO,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDQ,WAAW,EAAE;IACXR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDS,WAAW,EAAE;IACXT,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDU,UAAU,EAAE;IACVV,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDW,UAAU,EAAE;IACVX,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB;AACF,CAAC;AACD,MAAMY,4BAA4B,GAAG;EACnCR,OAAO,EAAE;IACPJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDK,OAAO,EAAE;IACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDM,QAAQ,EAAE;IACRN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDO,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDQ,WAAW,EAAE;IACXR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDS,WAAW,EAAE;IACXT,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDU,UAAU,EAAE;IACVV,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB,CAAC;EACDW,UAAU,EAAE;IACVX,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;EACrB;AACF,CAAC;AACD,MAAMa,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;AAC9I,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC5C,MAAM;IACJ/B,UAAU;IACVC,kBAAkB;IAClB+B,kBAAkB;IAClBC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGJ,MAAM;EACV,MAAMK,cAAc,GAAGpC,UAAU,GAAG,CAAC;EACrC,MAAMqC,YAAY,GAAG,CAAC,CAAC;EACvB;EACA,MAAMtC,WAAW,GAAGH,mBAAmB,CAAC;IACtC0C,aAAa,EAAEJ,YAAY;IAC3BK,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF5B,MAAM,CAAC6B,IAAI,CAAC3B,iBAAiB,CAAC,CAAC4B,OAAO,CAACC,GAAG,IAAI;IAC5C,MAAMC,QAAQ,GAAGX,kBAAkB,IAAIL,4BAA4B,CAACe,GAAG,CAAC,IAAI7B,iBAAiB,CAAC6B,GAAG,CAAC;IAClG,MAAME,aAAa,GAAGjC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+B,QAAQ,CAAC,EAAE;MAC/DV,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdY,YAAY,EAAE;IAChB,CAAC,CAAC;IACFR,YAAY,CAACK,GAAG,CAAC,GAAGE,aAAa;IACjC;IACA,IAAIhB,oBAAoB,CAACkB,GAAG,CAACJ,GAAG,CAAC,EAAE;MACjCE,aAAa,CAACG,SAAS,GAAG,KAAK;IACjC;IACA;IACA,QAAQL,GAAG;MACT,KAAK,KAAK;MACV,KAAK,SAAS;MACd,KAAK,UAAU;QACbE,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAG,CAACG,cAAc,GAAGH,MAAM;QAClD;MACF,KAAK,QAAQ;MACb,KAAK,YAAY;MACjB,KAAK,aAAa;QAChBW,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAGG,cAAc,GAAGH,MAAM;QACjD;MACF,KAAK,MAAM;MACX,KAAK,SAAS;MACd,KAAK,YAAY;QACfW,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAG,CAACG,cAAc,GAAGH,MAAM;QAClD;MACF,KAAK,OAAO;MACZ,KAAK,UAAU;MACf,KAAK,aAAa;QAChBW,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAGG,cAAc,GAAGH,MAAM;QACjD;IACJ;IACA,IAAID,kBAAkB,EAAE;MACtB,QAAQU,GAAG;QACT,KAAK,SAAS;QACd,KAAK,YAAY;UACfE,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAG,CAAClC,WAAW,CAACQ,qBAAqB,GAAG6B,cAAc;UAC7E;QACF,KAAK,UAAU;QACf,KAAK,aAAa;UAChBQ,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAGlC,WAAW,CAACQ,qBAAqB,GAAG6B,cAAc;UAC5E;QACF,KAAK,SAAS;QACd,KAAK,UAAU;UACbQ,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAG,CAAClC,WAAW,CAACQ,qBAAqB,GAAG,CAAC,GAAG6B,cAAc;UACjF;QACF,KAAK,YAAY;QACjB,KAAK,aAAa;UAChBQ,aAAa,CAACX,MAAM,CAAC,CAAC,CAAC,GAAGlC,WAAW,CAACQ,qBAAqB,GAAG,CAAC,GAAG6B,cAAc;UAChF;MACJ;IACF;IACA;IACAQ,aAAa,CAACxC,QAAQ,GAAGP,kBAAkB,CAAC6C,GAAG,EAAE3C,WAAW,EAAEC,UAAU,EAAEC,kBAAkB,CAAC;IAC7F;IACA,IAAIkC,YAAY,EAAE;MAChBS,aAAa,CAACI,UAAU,GAAG,cAAc;IAC3C;EACF,CAAC,CAAC;EACF,OAAOX,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}