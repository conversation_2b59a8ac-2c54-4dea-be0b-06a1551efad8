﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新成长值规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleUpdateParam extends Extensible {    
    /**
     * 规则ID
     */
    private Long id;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 基础成长值
     */
    private Long baseGrowth;    
    /**
     * 倍率
     */
    private java.math.BigDecimal multiplier;    
    /**
     * 每日限制
     */
    private Long dailyLimit;    
    /**
     * 每月限制
     */
    private Long monthlyLimit;    
    /**
     * 规则表达式
     */
    private String ruleExpression;    
    /**
     * 优先级
     */
    private Integer priority;    
    /**
     * 状态：ACTIVE-生效，INACTIVE-未生效
     */
    private String status;    
    /**
     * 开始时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String startTime;    
    /**
     * 结束时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String endTime;}
