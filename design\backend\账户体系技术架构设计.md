# 账户体系技术架构设计文档

## 1. 架构概述

### 1.1 整体架构
账户体系采用微服务架构，将积分、等级、成长值、勋章四大核心模块进行服务拆分，通过统一的API网关对外提供服务。

```
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   积分服务   │  │   等级服务   │  │  成长值服务  │          │
│  │             │  │             │  │             │          │
│  │ • 积分计算   │  │ • 等级计算   │  │ • 成长值计算 │          │
│  │ • 积分管理   │  │ • 权益管理   │  │ • 数据统计   │          │
│  │ • 规则引擎   │  │ • 升级通知   │  │ • 趋势分析   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   勋章服务   │  │   通知服务   │  │   统计服务   │          │
│  │             │  │             │  │             │          │
│  │ • 成就检测   │  │ • 消息推送   │  │ • 数据分析   │          │
│  │ • 勋章发放   │  │ • 模板管理   │  │ • 报表生成   │          │
│  │ • 展示管理   │  │ • 渠道管理   │  │ • 实时监控   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                        数据存储层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │    MySQL    │  │    Redis    │  │  Elasticsearch │        │
│  │             │  │             │  │             │          │
│  │ • 核心数据   │  │ • 缓存数据   │  │ • 日志数据   │          │
│  │ • 事务处理   │  │ • 计数器    │  │ • 搜索分析   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择
- **开发语言**: Java 17 + Micronaut 4.0
- **数据库**: MySQL 8.0 (主库) + Redis 7.0 (缓存)
- **消息队列**: Apache Kafka 3.0
- **搜索引擎**: Elasticsearch 8.0
- **服务注册**: Nacos 2.0
- **API网关**: Micronaut Gateway
- **监控**: Prometheus + Grafana
- **链路追踪**: SkyWalking

### 1.3 部署架构
- **容器化**: Docker + Kubernetes
- **负载均衡**: Nginx + Kubernetes Service
- **数据库**: MySQL主从复制 + Redis集群
- **文件存储**: MinIO对象存储
- **CDN**: 阿里云CDN加速

## 2. 服务拆分设计

### 2.1 积分服务 (Point Service)
**职责范围**:
- 积分账户管理
- 积分获取和消费
- 积分规则引擎
- 积分明细记录

**核心接口**:
```java
@RestController
@RequestMapping("/api/v1/point")
public class PointController {
    
    @PostMapping("/earn")
    public Result<PointTransaction> earnPoint(@RequestBody EarnPointRequest request);
    
    @PostMapping("/consume")
    public Result<PointTransaction> consumePoint(@RequestBody ConsumePointRequest request);
    
    @GetMapping("/balance/{userId}")
    public Result<PointBalance> getBalance(@PathVariable Long userId);
    
    @GetMapping("/transactions/{userId}")
    public Result<PageResult<PointTransaction>> getTransactions(
        @PathVariable Long userId, @RequestParam PageRequest pageRequest);
}
```

### 2.2 等级服务 (Level Service)
**职责范围**:
- 用户等级计算
- 等级权益管理
- 升级检测和通知
- 等级配置管理

**核心接口**:
```java
@RestController
@RequestMapping("/api/v1/level")
public class LevelController {
    
    @GetMapping("/info/{userId}")
    public Result<UserLevel> getLevelInfo(@PathVariable Long userId);
    
    @PostMapping("/calculate")
    public Result<LevelCalculateResult> calculateLevel(@RequestBody LevelCalculateRequest request);
    
    @GetMapping("/privileges/{userId}")
    public Result<List<LevelPrivilege>> getPrivileges(@PathVariable Long userId);
    
    @GetMapping("/config")
    public Result<List<LevelRule>> getLevelRules();
}
```

### 2.3 成长值服务 (Growth Service)
**职责范围**:
- 成长值计算和累积
- 多维度成长值管理
- 成长轨迹记录
- 成长值统计分析

**核心接口**:
```java
@RestController
@RequestMapping("/api/v1/growth")
public class GrowthController {
    
    @PostMapping("/earn")
    public Result<GrowthTransaction> earnGrowth(@RequestBody EarnGrowthRequest request);
    
    @GetMapping("/account/{userId}")
    public Result<GrowthAccount> getGrowthAccount(@PathVariable Long userId);
    
    @GetMapping("/statistics/{userId}")
    public Result<GrowthStatistics> getGrowthStatistics(@PathVariable Long userId);
    
    @GetMapping("/trend/{userId}")
    public Result<List<GrowthTrend>> getGrowthTrend(@PathVariable Long userId);
}
```

### 2.4 勋章服务 (Badge Service)
**职责范围**:
- 勋章配置管理
- 成就条件检测
- 勋章发放和回收
- 勋章展示管理

**核心接口**:
```java
@RestController
@RequestMapping("/api/v1/badge")
public class BadgeController {
    
    @PostMapping("/award")
    public Result<BadgeAwardResult> awardBadge(@RequestBody AwardBadgeRequest request);
    
    @GetMapping("/user/{userId}")
    public Result<List<UserBadge>> getUserBadges(@PathVariable Long userId);
    
    @GetMapping("/progress/{userId}")
    public Result<List<BadgeProgress>> getBadgeProgress(@PathVariable Long userId);
    
    @GetMapping("/config")
    public Result<List<BadgeConfig>> getBadgeConfigs();
}
```

## 3. 数据库设计

### 3.1 数据库分库分表策略
```sql
-- 按用户ID进行分库分表
-- 积分相关表按用户ID hash分表
-- 明细表按时间+用户ID分表
-- 配置表不分表，使用主库

-- 分库策略
database_0: user_id % 4 = 0
database_1: user_id % 4 = 1  
database_2: user_id % 4 = 2
database_3: user_id % 4 = 3

-- 分表策略  
user_point_0: user_id % 8 = 0
user_point_1: user_id % 8 = 1
...
point_detail_202501: 2025年1月数据
point_detail_202502: 2025年2月数据
```

### 3.2 核心表结构设计

#### 3.2.1 积分账户表
```sql
CREATE TABLE user_point (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_point BIGINT DEFAULT 0 COMMENT '总积分',
    available_point BIGINT DEFAULT 0 COMMENT '可用积分',
    frozen_point BIGINT DEFAULT 0 COMMENT '冻结积分',
    expired_point BIGINT DEFAULT 0 COMMENT '已过期积分',
    version INT DEFAULT 0 COMMENT '乐观锁版本号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分账户表';
```

#### 3.2.2 积分明细表
```sql
CREATE TABLE point_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    transaction_id VARCHAR(64) NOT NULL COMMENT '交易ID',
    point_type ENUM('EARN', 'CONSUME', 'EXPIRE', 'FREEZE', 'UNFREEZE') NOT NULL COMMENT '积分类型',
    point BIGINT NOT NULL COMMENT '积分数量',
    balance_after BIGINT NOT NULL COMMENT '操作后余额',
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    business_id VARCHAR(64) COMMENT '业务ID',
    source VARCHAR(64) NOT NULL COMMENT '来源',
    description VARCHAR(255) COMMENT '描述',
    expire_time DATETIME COMMENT '过期时间',
    status ENUM('VALID', 'USED', 'EXPIRED') DEFAULT 'VALID' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id_create_time (user_id, create_time),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_business (business_type, business_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分明细表';
```

#### 3.2.3 用户等级表
```sql
CREATE TABLE user_level (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    current_level INT DEFAULT 1 COMMENT '当前等级',
    current_growth BIGINT DEFAULT 0 COMMENT '当前成长值',
    next_level_growth BIGINT DEFAULT 0 COMMENT '下一等级所需成长值',
    total_growth BIGINT DEFAULT 0 COMMENT '总成长值',
    level_up_time DATETIME COMMENT '最近升级时间',
    version INT DEFAULT 0 COMMENT '乐观锁版本号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_level (current_level),
    INDEX idx_growth (total_growth)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级表';
```

#### 3.2.4 成长值账户表
```sql
CREATE TABLE growth_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_growth BIGINT DEFAULT 0 COMMENT '总成长值',
    consume_growth BIGINT DEFAULT 0 COMMENT '消费成长值',
    activity_growth BIGINT DEFAULT 0 COMMENT '活跃成长值',
    social_growth BIGINT DEFAULT 0 COMMENT '社交成长值',
    task_growth BIGINT DEFAULT 0 COMMENT '任务成长值',
    last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_total_growth (total_growth)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值账户表';
```

#### 3.2.5 用户勋章表
```sql
CREATE TABLE user_badge (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    badge_id BIGINT NOT NULL COMMENT '勋章ID',
    earn_time DATETIME NOT NULL COMMENT '获得时间',
    expire_time DATETIME COMMENT '过期时间',
    status ENUM('VALID', 'EXPIRED', 'REVOKED') DEFAULT 'VALID' COMMENT '状态',
    is_displayed BOOLEAN DEFAULT FALSE COMMENT '是否展示',
    display_order INT DEFAULT 0 COMMENT '展示顺序',
    source VARCHAR(64) NOT NULL COMMENT '获得来源',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_badge (user_id, badge_id),
    INDEX idx_user_id_status (user_id, status),
    INDEX idx_badge_id (badge_id),
    INDEX idx_earn_time (earn_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户勋章表';
```

### 3.3 缓存设计

#### 3.3.1 Redis缓存策略
```java
// 缓存Key设计
public class CacheKeys {
    // 积分账户缓存，TTL: 1小时
    public static final String POINTS_ACCOUNT = "point:account:%d";
    
    // 用户等级缓存，TTL: 30分钟  
    public static final String USER_LEVEL = "level:user:%d";
    
    // 成长值账户缓存，TTL: 30分钟
    public static final String GROWTH_ACCOUNT = "growth:account:%d";
    
    // 勋章配置缓存，TTL: 24小时
    public static final String BADGE_CONFIG = "badge:config:%d";
    
    // 等级配置缓存，TTL: 24小时
    public static final String LEVEL_CONFIG = "level:config";
    
    // 积分规则缓存，TTL: 1小时
    public static final String POINTS_RULES = "point:rule:%s";
}
```

#### 3.3.2 缓存更新策略
```java
@Service
public class CacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 缓存更新策略：先更新数据库，再删除缓存
    public void updateUserPoint(Long userId, UserPoint account) {
        // 1. 更新数据库
        pointAccountMapper.updateByUserId(account);
        
        // 2. 删除缓存
        String cacheKey = String.format(CacheKeys.POINTS_ACCOUNT, userId);
        redisTemplate.delete(cacheKey);
    }
    
    // 缓存预热
    @PostConstruct
    public void warmUpCache() {
        // 预热热点数据
        loadLevelRules();
        loadBadgeConfigs();
        loadPointRule();
    }
}
```

## 4. 核心算法设计

### 4.1 积分计算引擎
```java
@Component
public class PointCalculationEngine {
    
    public PointCalculationResult calculate(PointCalculationRequest request) {
        // 1. 获取适用规则
        List<PointRule> rule = getApplicableRule(request);
        
        // 2. 计算基础积分
        long basePoint = calculateBasePoint(request, rule);
        
        // 3. 应用等级加成
        long levelBonus = applyLevelBonus(request.getUserId(), basePoint);
        
        // 4. 应用活动加成
        long activityBonus = applyActivityBonus(request, basePoint);
        
        // 5. 检查每日上限
        long finalPoint = checkDailyLimit(request.getUserId(), 
            basePoint + levelBonus + activityBonus);
        
        return PointCalculationResult.builder()
            .basePoint(basePoint)
            .levelBonus(levelBonus)
            .activityBonus(activityBonus)
            .finalPoint(finalPoint)
            .build();
    }
}
```

### 4.2 等级计算算法
```java
@Component
public class LevelCalculationEngine {
    
    public LevelCalculationResult calculateLevel(Long userId, long totalGrowth) {
        // 1. 获取等级配置
        List<LevelRule> levelConfigs = getLevelRules();
        
        // 2. 计算当前等级
        int currentLevel = 1;
        long currentLevelGrowth = 0;
        long nextLevelGrowth = 0;
        
        for (LevelRule config : levelConfigs) {
            if (totalGrowth >= config.getRequiredGrowth()) {
                currentLevel = config.getLevel();
                currentLevelGrowth = config.getRequiredGrowth();
                
                // 查找下一等级所需成长值
                LevelRule nextConfig = getNextLevelRule(config.getLevel());
                if (nextConfig != null) {
                    nextLevelGrowth = nextConfig.getRequiredGrowth();
                }
            } else {
                break;
            }
        }
        
        return LevelCalculationResult.builder()
            .currentLevel(currentLevel)
            .currentLevelGrowth(currentLevelGrowth)
            .nextLevelGrowth(nextLevelGrowth)
            .progress((double)(totalGrowth - currentLevelGrowth) / 
                     (nextLevelGrowth - currentLevelGrowth))
            .build();
    }
}
```

### 4.3 勋章检测算法
```java
@Component
public class BadgeDetectionEngine {
    
    public List<BadgeDetectionResult> detectBadges(Long userId, String eventType, 
                                                  Map<String, Object> eventData) {
        List<BadgeDetectionResult> results = new ArrayList<>();
        
        // 1. 获取相关勋章配置
        List<BadgeConfig> badgeConfigs = getBadgeConfigsByEventType(eventType);
        
        for (BadgeConfig config : badgeConfigs) {
            // 2. 检查用户是否已获得该勋章
            if (hasUserEarnedBadge(userId, config.getId())) {
                continue;
            }
            
            // 3. 检查获得条件
            boolean conditionMet = checkBadgeCondition(userId, config, eventData);
            
            if (conditionMet) {
                results.add(BadgeDetectionResult.builder()
                    .badgeId(config.getId())
                    .userId(userId)
                    .earnReason(eventType)
                    .eventData(eventData)
                    .build());
            }
        }
        
        return results;
    }
}
```

## 5. 分布式事务设计

### 5.1 Saga分布式事务模式
```java
@Component
public class AccountTransactionSaga {
    
    @SagaOrchestrationStart
    public void processUserAction(UserActionEvent event) {
        // 1. 发放积分
        sagaManager.choreography()
            .step("earnPoint")
            .localTransaction(this::earnPoint)
            .compensationTransaction(this::compensatePoint)
            
            // 2. 增加成长值
            .step("earnGrowth")
            .localTransaction(this::earnGrowth)
            .compensationTransaction(this::compensateGrowth)
            
            // 3. 检测等级升级
            .step("checkLevelUp")
            .localTransaction(this::checkLevelUp)
            .compensationTransaction(this::compensateLevelUp)
            
            // 4. 检测勋章获得
            .step("checkBadges")
            .localTransaction(this::checkBadges)
            .compensationTransaction(this::compensateBadges)
            
            // 5. 发送通知
            .step("sendNotification")
            .localTransaction(this::sendNotification)
            .compensationTransaction(this::compensateNotification)
            
            .execute(event);
    }
}
```

### 5.2 本地事务管理
```java
@Service
@Transactional
public class PointTransactionService {
    
    public PointTransaction earnPoint(EarnPointRequest request) {
        // 1. 参数校验
        validateEarnPointRequest(request);
        
        // 2. 获取用户积分账户（加锁）
        UserPoint account = pointAccountMapper.selectByUserIdForUpdate(request.getUserId());
        
        // 3. 计算积分
        long earnedPoint = pointCalculationEngine.calculate(request).getFinalPoint();
        
        // 4. 更新账户余额
        account.setAvailablePoint(account.getAvailablePoint() + earnedPoint);
        account.setTotalPoint(account.getTotalPoint() + earnedPoint);
        pointAccountMapper.updateByUserId(account);
        
        // 5. 记录明细
        PointDetail detail = buildPointDetail(request, earnedPoint, account.getAvailablePoint());
        pointDetailMapper.insert(detail);
        
        // 6. 发布事件
        eventPublisher.publishEvent(new PointEarnedEvent(request.getUserId(), earnedPoint));
        
        return buildPointTransaction(detail);
    }
}
```

## 6. 性能优化方案

### 6.1 数据库优化
```sql
-- 1. 索引优化
-- 积分明细表复合索引
CREATE INDEX idx_user_time_type ON point_detail(user_id, create_time, point_type);

-- 成长值明细表分区
CREATE TABLE growth_detail (
    -- 字段定义
) PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027)
);

-- 2. 读写分离配置
-- 主库：写操作
-- 从库：读操作，延迟容忍度1秒内
```

### 6.2 缓存优化
```java
@Service
public class PointService {
    
    // 多级缓存：本地缓存 + Redis缓存
    @Cacheable(value = "pointAccount", key = "#userId", 
               cacheManager = "multiLevelCacheManager")
    public UserPoint getUserPoint(Long userId) {
        return pointAccountMapper.selectByUserId(userId);
    }
    
    // 缓存预热
    @Scheduled(fixedRate = 300000) // 5分钟
    public void warmUpHotData() {
        // 预热活跃用户数据
        List<Long> activeUsers = getActiveUsers();
        activeUsers.parallelStream().forEach(userId -> {
            getUserPoint(userId);
            getUserLevel(userId);
        });
    }
}
```

### 6.3 异步处理优化
```java
@Component
public class AsyncTaskProcessor {
    
    @Async("accountTaskExecutor")
    public CompletableFuture<Void> processGrowthCalculation(Long userId, String eventType) {
        // 异步计算成长值
        growthService.calculateAndUpdateGrowth(userId, eventType);
        return CompletableFuture.completedFuture(null);
    }
    
    @Async("badgeTaskExecutor")  
    public CompletableFuture<Void> processBadgeDetection(Long userId, String eventType) {
        // 异步检测勋章
        badgeService.detectAndAwardBadges(userId, eventType);
        return CompletableFuture.completedFuture(null);
    }
}
```

## 7. 监控和运维

### 7.1 监控指标设计
```java
@Component
public class AccountMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // 业务指标
    private final Counter pointEarnedCounter;
    private final Counter pointConsumedCounter;
    private final Counter levelUpCounter;
    private final Counter badgeAwardedCounter;
    
    // 性能指标
    private final Timer pointCalculationTimer;
    private final Timer levelCalculationTimer;
    private final Gauge activeUsersGauge;
    
    public AccountMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.pointEarnedCounter = Counter.builder("point.earned.total")
            .description("Total point earned")
            .register(meterRegistry);
        // ... 其他指标初始化
    }
}
```

### 7.2 日志设计
```java
@Slf4j
@Component
public class AccountLogger {
    
    // 业务日志
    public void logPointTransaction(PointTransaction transaction) {
        log.info("Point transaction: userId={}, type={}, amount={}, balance={}, transactionId={}", 
            transaction.getUserId(), 
            transaction.getPointType(),
            transaction.getPointAmount(),
            transaction.getBalanceAfter(),
            transaction.getTransactionId());
    }
    
    // 性能日志
    public void logSlowQuery(String operation, long duration, Object... params) {
        if (duration > 1000) { // 超过1秒记录慢查询
            log.warn("Slow operation: operation={}, duration={}ms, params={}", 
                operation, duration, Arrays.toString(params));
        }
    }
    
    // 错误日志
    public void logError(String operation, Exception e, Object... params) {
        log.error("Operation failed: operation={}, params={}, error={}", 
            operation, Arrays.toString(params), e.getMessage(), e);
    }
}
```

### 7.3 健康检查
```java
@Component
public class AccountHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            checkDatabaseConnection();
            
            // 检查Redis连接
            checkRedisConnection();
            
            // 检查关键业务指标
            checkBusinessMetrics();
            
            return Health.up()
                .withDetail("database", "UP")
                .withDetail("redis", "UP")
                .withDetail("business", "HEALTHY")
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

## 8. 安全设计

### 8.1 接口安全
```java
@RestController
@RequestMapping("/api/v1/point")
@Validated
public class PointController {
    
    @PostMapping("/earn")
    @PreAuthorize("hasRole('USER')")
    @RateLimiter(name = "pointEarn", fallbackMethod = "earnPointFallback")
    public Result<PointTransaction> earnPoint(
        @Valid @RequestBody EarnPointRequest request,
        @RequestHeader("X-User-Id") Long userId) {
        
        // 验证用户身份
        if (!request.getUserId().equals(userId)) {
            throw new SecurityException("User ID mismatch");
        }
        
        // 防重复提交
        String idempotencyKey = request.getIdempotencyKey();
        if (StringUtils.isNotBlank(idempotencyKey)) {
            if (idempotencyService.isDuplicate(idempotencyKey)) {
                return idempotencyService.getResult(idempotencyKey);
            }
        }
        
        return pointService.earnPoint(request);
    }
}
```

### 8.2 数据安全
```java
@Component
public class DataSecurityService {
    
    // 敏感数据加密
    public String encryptSensitiveData(String data) {
        return AESUtil.encrypt(data, getEncryptionKey());
    }
    
    // 数据脱敏
    public String maskSensitiveData(String data, DataType dataType) {
        switch (dataType) {
            case PHONE:
                return data.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
            case EMAIL:
                return data.replaceAll("(\\w{2})\\w+(\\w{2}@\\w+\\.\\w+)", "$1***$2");
            default:
                return data;
        }
    }
    
    // 操作审计
    @EventListener
    public void auditOperation(AccountOperationEvent event) {
        AuditLog auditLog = AuditLog.builder()
            .userId(event.getUserId())
            .operation(event.getOperation())
            .operationTime(event.getOperationTime())
            .ipAddress(event.getIpAddress())
            .userAgent(event.getUserAgent())
            .build();
        
        auditLogService.save(auditLog);
    }
}
```

## 9. 部署和运维

### 9.1 Docker化部署
```dockerfile
# Dockerfile
FROM openjdk:17