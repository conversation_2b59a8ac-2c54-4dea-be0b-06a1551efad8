﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值调整结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAdjustResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 调整类型
     */
    private String adjustType;    
    /**
     * 调整数量
     */
    private Long amount;    
    /**
     * 调整前成长值
     */
    private Long beforeGrowth;    
    /**
     * 调整后成长值
     */
    private Long afterGrowth;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 调整时间
     */
    private String adjustTime;    
    /**
     * 处理状态
     */
    private String status;}
