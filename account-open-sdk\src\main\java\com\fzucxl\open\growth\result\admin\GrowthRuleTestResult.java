﻿package com.fzucxl.open.growth.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 测试成长值规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleTestResult extends Extensible {    
    /**
     * 是否匹配规则
     */
    private Boolean matched;    
    /**
     * 计算得出的成长值
     */
    private Long growthValue;    
    /**
     * 规则表达式
     */
    private String ruleExpression;    
    /**
     * 执行日志
     */
    private String executionLog;}
