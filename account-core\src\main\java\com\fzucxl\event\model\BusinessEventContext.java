package com.fzucxl.event.model;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.serde.annotation.Serdeable;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务事件上下文对象
 * 用于规则引擎执行时的上下文数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Serdeable
public class BusinessEventContext {
    
    /**
     * 事件编码
     */
    private String eventCode;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 事件发生时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 事件来源
     */
    private String source;
    
    /**
     * 事件数据映射，用于规则引擎执行
     */
    private Map<String, Object> context;
    
    /**
     * 系统内置字段
     */
    private Map<String, Object> systemFields;
    
    // Constructors
    public BusinessEventContext() {
        this.context = new HashMap<>();
        this.systemFields = new HashMap<>();
        this.eventTime = LocalDateTime.now();
    }
    
    public BusinessEventContext(String eventCode, String userId) {
        this();
        this.eventCode = eventCode;
        this.userId = userId;
        initSystemFields();
    }
    
    /**
     * 初始化系统内置字段
     */
    private void initSystemFields() {
        systemFields.put("eventCode", eventCode);
        systemFields.put("userId", userId);
        systemFields.put("eventTime", eventTime);
        systemFields.put("source", source);
        systemFields.put("currentTime", LocalDateTime.now());
    }
    
    /**
     * 添加事件字段值
     */
    public BusinessEventContext addField(String fieldCode, Object value) {
        context.put(fieldCode, value);
        return this;
    }
    
    /**
     * 批量添加事件字段值
     */
    public BusinessEventContext addFields(Map<String, Object> fields) {
        if (fields != null) {
            context.putAll(fields);
        }
        return this;
    }
    
    /**
     * 获取字段值
     */
    public Object getField(String fieldCode) {
        return context.get(fieldCode);
    }
    
    /**
     * 获取字段值（带类型转换）
     */
    @SuppressWarnings("unchecked")
    public <T> T getField(String fieldCode, Class<T> type) {
        Object value = context.get(fieldCode);
        if (value == null) {
            return null;
        }
        
        if (type.isInstance(value)) {
            return (T) value;
        }
        
        // 简单类型转换
        if (type == String.class) {
            return (T) value.toString();
        } else if (type == Integer.class && value instanceof Number) {
            return (T) Integer.valueOf(((Number) value).intValue());
        } else if (type == Long.class && value instanceof Number) {
            return (T) Long.valueOf(((Number) value).longValue());
        } else if (type == Double.class && value instanceof Number) {
            return (T) Double.valueOf(((Number) value).doubleValue());
        } else if (type == Boolean.class) {
            return (T) Boolean.valueOf(value.toString());
        }
        
        return null;
    }
    
    /**
     * 获取完整的上下文映射（包含系统字段和事件字段）
     * 用于规则引擎执行
     */
    public Map<String, Object> getFullContext() {
        Map<String, Object> fullContext = new HashMap<>();
        fullContext.putAll(systemFields);
        fullContext.putAll(context);
        return fullContext;
    }
    
    /**
     * 检查字段是否存在
     */
    public boolean hasField(String fieldCode) {
        return context.containsKey(fieldCode);
    }
    
    /**
     * 移除字段
     */
    public BusinessEventContext removeField(String fieldCode) {
        context.remove(fieldCode);
        return this;
    }
    
    /**
     * 清空所有事件字段
     */
    public BusinessEventContext clearFields() {
        context.clear();
        return this;
    }
    
    /**
     * 获取事件字段数量
     */
    public int getFieldCount() {
        return context.size();
    }
    
    // Getters and Setters
    public String getEventCode() {
        return eventCode;
    }
    
    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
        systemFields.put("eventCode", eventCode);
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
        systemFields.put("userId", userId);
    }
    
    public LocalDateTime getEventTime() {
        return eventTime;
    }
    
    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
        systemFields.put("eventTime", eventTime);
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
        systemFields.put("source", source);
    }
    
    public Map<String, Object> getContext() {
        return context;
    }
    
    public void setContext(Map<String, Object> context) {
        this.context = context != null ? context : new HashMap<>();
    }
    
    public Map<String, Object> getSystemFields() {
        return systemFields;
    }
    
    @Override
    public String toString() {
        return "BusinessEventContext{" +
                "eventCode='" + eventCode + '\'' +
                ", userId='" + userId + '\'' +
                ", eventTime=" + eventTime +
                ", source='" + source + '\'' +
                ", fieldCount=" + getFieldCount() +
                '}';
    }
}