﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级流转
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelFlow extends Extensible {    
    /**
     * 起始等级
     */
    private Integer fromLevel;    
    /**
     * 目标等级
     */
    private Integer toLevel;    
    /**
     * 流转次数
     */
    private Long flowCount;    
    /**
     * 流转类型：UPGRADE(升级), DOWNGRADE(降级)
     */
    private String flowType;}
