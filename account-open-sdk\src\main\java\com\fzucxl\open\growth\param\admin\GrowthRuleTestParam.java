﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 测试成长值规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleTestParam extends Extensible {    
    /**
     * 规则ID
     */
    private Long ruleId;    
    /**
     * 测试用户ID
     */
    private Long userId;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 业务数据
     */
    private java.util.Map<String, Object> businessData;}
