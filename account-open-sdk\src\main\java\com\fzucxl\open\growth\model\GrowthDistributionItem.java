﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值分布项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthDistributionItem extends Extensible {    
    /**
     * 分类
     */
    private String category;    
    /**
     * 成长值
     */
    private Long growthValue;    
    /**
     * 用户数量
     */
    private Long userCount;    
    /**
     * 占比
     */
    private java.math.BigDecimal percentage;}
