﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新勋章规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRuleUpdateParam extends Extensible {    
    /**
     * 规则ID
     */
    private Long ruleId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 规则类型
     */
    private String ruleType;    
    /**
     * 触发条件
     */
    private String triggerCondition;    
    /**
     * 条件表达式
     */
    private String conditionExpression;    
    /**
     * 优先级
     */
    private Integer priority;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 操作人ID
     */
    private Long operatorId;}
