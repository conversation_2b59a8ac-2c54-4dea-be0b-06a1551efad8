package com.fzucxl.event.dto;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.serde.annotation.Serdeable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.Map;

/**
 * 触发事件请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Serdeable
public class TriggerEventRequest {
    
    @NotBlank(message = "事件编码不能为空")
    private String eventCode;
    
    @NotBlank(message = "用户ID不能为空")
    private Integer userId;
    
    @NotNull(message = "事件数据不能为空")
    private Map<String, Object> eventData;
    
    private String source;
    
    // Constructors
    public TriggerEventRequest() {}
    
    public TriggerEventRequest(String eventCode, String userId, Map<String, Object> eventData) {
        this.eventCode = eventCode;
        this.userId = userId;
        this.eventData = eventData;
    }
    
    // Getters and Setters
    public String getEventCode() {
        return eventCode;
    }
    
    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public Map<String, Object> getEventData() {
        return eventData;
    }
    
    public void setEventData(Map<String, Object> eventData) {
        this.eventData = eventData;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
}