{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { Plot } from '../../base';\nimport { adaptor } from './adaptor';\nvar Violin = /** @class */function (_super) {\n  __extends(Violin, _super);\n  function Violin() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    /** 图表类型 */\n    _this.type = 'violin';\n    return _this;\n  }\n  /**\n   * 获取 折线图 默认配置项\n   * 供外部使用\n   */\n  Violin.getDefaultOptions = function () {\n    return {\n      type: 'view',\n      children: [{\n        type: 'density',\n        sizeField: 'size',\n        tooltip: false\n      }, {\n        type: 'boxplot',\n        shapeField: 'violin',\n        style: {\n          opacity: 0.5,\n          point: false\n        }\n      }],\n      animate: {\n        enter: {\n          type: 'fadeIn'\n        }\n      }\n    };\n  };\n  /**\n   * 获取 折线图 默认配置\n   */\n  Violin.prototype.getDefaultOptions = function () {\n    return Violin.getDefaultOptions();\n  };\n  /**\n   * 折线图适配器\n   */\n  Violin.prototype.getSchemaAdaptor = function () {\n    return adaptor;\n  };\n  return Violin;\n}(Plot);\nexport { Violin };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "Plot", "adaptor", "Violin", "_super", "_this", "apply", "arguments", "type", "getDefaultOptions", "children", "sizeField", "tooltip", "shapeField", "style", "opacity", "point", "animate", "enter", "getSchemaAdaptor"], "sources": ["D:/code/private/fast-account/loyalty-system-admin/node_modules/@ant-design/plots/es/core/plots/violin/index.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { Plot } from '../../base';\nimport { adaptor } from './adaptor';\nvar Violin = /** @class */ (function (_super) {\n    __extends(Violin, _super);\n    function Violin() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        /** 图表类型 */\n        _this.type = 'violin';\n        return _this;\n    }\n    /**\n     * 获取 折线图 默认配置项\n     * 供外部使用\n     */\n    Violin.getDefaultOptions = function () {\n        return {\n            type: 'view',\n            children: [\n                {\n                    type: 'density',\n                    sizeField: 'size',\n                    tooltip: false,\n                },\n                {\n                    type: 'boxplot',\n                    shapeField: 'violin',\n                    style: {\n                        opacity: 0.5,\n                        point: false,\n                    },\n                },\n            ],\n            animate: { enter: { type: 'fadeIn' } },\n        };\n    };\n    /**\n     * 获取 折线图 默认配置\n     */\n    Violin.prototype.getDefaultOptions = function () {\n        return Violin.getDefaultOptions();\n    };\n    /**\n     * 折线图适配器\n     */\n    Violin.prototype.getSchemaAdaptor = function () {\n        return adaptor;\n    };\n    return Violin;\n}(Plot));\nexport { Violin };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,SAASG,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,WAAW;AACnC,IAAIC,MAAM,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC1CpB,SAAS,CAACmB,MAAM,EAAEC,MAAM,CAAC;EACzB,SAASD,MAAMA,CAAA,EAAG;IACd,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpE;IACAF,KAAK,CAACG,IAAI,GAAG,QAAQ;IACrB,OAAOH,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIF,MAAM,CAACM,iBAAiB,GAAG,YAAY;IACnC,OAAO;MACHD,IAAI,EAAE,MAAM;MACZE,QAAQ,EAAE,CACN;QACIF,IAAI,EAAE,SAAS;QACfG,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,SAAS;QACfK,UAAU,EAAE,QAAQ;QACpBC,KAAK,EAAE;UACHC,OAAO,EAAE,GAAG;UACZC,KAAK,EAAE;QACX;MACJ,CAAC,CACJ;MACDC,OAAO,EAAE;QAAEC,KAAK,EAAE;UAAEV,IAAI,EAAE;QAAS;MAAE;IACzC,CAAC;EACL,CAAC;EACD;AACJ;AACA;EACIL,MAAM,CAACV,SAAS,CAACgB,iBAAiB,GAAG,YAAY;IAC7C,OAAON,MAAM,CAACM,iBAAiB,CAAC,CAAC;EACrC,CAAC;EACD;AACJ;AACA;EACIN,MAAM,CAACV,SAAS,CAAC0B,gBAAgB,GAAG,YAAY;IAC5C,OAAOjB,OAAO;EAClB,CAAC;EACD,OAAOC,MAAM;AACjB,CAAC,CAACF,IAAI,CAAE;AACR,SAASE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}