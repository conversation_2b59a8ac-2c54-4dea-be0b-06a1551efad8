package com.fzucxl.point.rule.repository;

import com.fzucxl.entity.attribute.AttributeMeta;
import io.micronaut.data.jdbc.annotation.JdbcRepository;
import io.micronaut.data.model.query.builder.sql.Dialect;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 属性元数据Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@JdbcRepository(dialect = Dialect.MYSQL)
public interface AttributeMetaRepository extends CrudRepository<AttributeMeta, Long> {
    
    /**
     * 分页查找所有属性元数据
     */
    Page<AttributeMeta> findAll(Pageable pageable);
    
    /**
     * 根据属性编码查找
     */
    Optional<AttributeMeta> findByAttributeCode(String attributeCode);
    
    /**
     * 根据属性编码和状态查找
     */
    Optional<AttributeMeta> findByAttributeCodeAndStatus(String attributeCode, String status);
    
    /**
     * 检查属性编码是否存在
     */
    boolean existsByAttributeCode(String attributeCode);
    
    /**
     * 根据数据源查找
     */
    List<AttributeMeta> findByDataSource(String dataSource);
    
    /**
     * 根据数据源和状态查找
     */
    List<AttributeMeta> findByDataSourceAndStatus(String dataSource, String status);
    
    /**
     * 根据数据源和状态分页查找
     */
    Page<AttributeMeta> findByDataSourceAndStatus(String dataSource, String status, Pageable pageable);
    
    /**
     * 根据状态查找
     */
    List<AttributeMeta> findByStatus(String status);
    
    /**
     * 根据状态分页查找
     */
    Page<AttributeMeta> findByStatus(String status, Pageable pageable);
    
    /**
     * 根据目标表查找
     */
    List<AttributeMeta> findByTargetTable(String targetTable);
    
    /**
     * 根据聚合函数查找
     */
    List<AttributeMeta> findByAggregateFunction(String aggregateFunction);

    List<AttributeMeta> findByAttributeCodeIn(List<String> attributeCodes);
}