﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值交易记录结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthTransactionQueryResult extends Extensible {    
    /**
     * 成长值交易记录列表
     */
    private java.util.List<GrowthTransaction> transactionList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
