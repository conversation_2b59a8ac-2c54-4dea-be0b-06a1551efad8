﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeStatQueryParam extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 分组维度：DAY(按天), WEEK(按周), MONTH(按月)
     */
    private String groupBy;}
