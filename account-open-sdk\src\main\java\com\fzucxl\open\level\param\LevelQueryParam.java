﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户等级参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易ID，用于关联上下游交易
     */
    private String transactionId;}
