﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章排行榜参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRankingQueryParam extends Extensible {    
    /**
     * 排行类型：COUNT(数量排行), RARITY(稀有度排行), LEVEL(等级排行)
     */
    private String rankingType;    
    /**
     * 时间范围：WEEK(本周), MONTH(本月), YEAR(本年), ALL(全部)
     */
    private String timeRange;    
    /**
     * 返回数量限制
     */
    private Integer limit;}
