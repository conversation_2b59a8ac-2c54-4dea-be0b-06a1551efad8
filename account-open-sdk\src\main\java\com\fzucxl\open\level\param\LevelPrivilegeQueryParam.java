﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级特权参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelPrivilegeQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 等级
     */
    private Integer level;    
    /**
     * 特权类型
     */
    private String privilegeType;    
    /**
     * 交易ID
     */
    private String transactionId;}
