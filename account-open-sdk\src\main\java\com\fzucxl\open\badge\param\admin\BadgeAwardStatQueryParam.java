﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章发放统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAwardStatQueryParam extends Extensible {    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 勋章类型
     */
    private String badgeType;    
    /**
     * 分组维度
     */
    private String groupBy;}
