package com.fzucxl.open.base.point;

/**
 * 事务类型枚举
 */
public enum PointTransactionType {
    EARN("积分发放"),
    CONSUME("积分消费"),
    EXPIRE("积分过期"),
    FREEZE("积分冻结"),
    UNFREEZE("积分解冻"),
    TRANSFER_OUT("积分转出"),
    TRANSFER_IN("积分转入"),
    REFUND("积分退款"),
    ADJUST("积分调整"),
    CANCEL("积分撤销");

    private final String description;

    PointTransactionType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为增加积分的事务类型
     */
    public boolean isIncreaseType() {
        return this == EARN || this == TRANSFER_IN || this == REFUND ||
                this == UNFREEZE || (this == ADJUST);
    }

    /**
     * 判断是否为减少积分的事务类型
     */
    public boolean isDecreaseType() {
        return this == CONSUME || this == EXPIRE || this == TRANSFER_OUT ||
                this == FREEZE || this == CANCEL;
    }
}
