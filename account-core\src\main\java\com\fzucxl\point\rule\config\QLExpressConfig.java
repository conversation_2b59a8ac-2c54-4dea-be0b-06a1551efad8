package com.fzucxl.point.rule.config;

import com.alibaba.qlexpress4.Express4Runner;
import com.alibaba.qlexpress4.InitOptions;
import com.alibaba.qlexpress4.QLOptions;
import com.alibaba.qlexpress4.QLResult;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;

/**
 * QLExpress配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Factory
public class QLExpressConfig {

    @Bean
    public Express4Runner expressRunner() {
        return new Express4Runner(InitOptions.DEFAULT_OPTIONS);
    }

    public static void main(String[]  args){
        Express4Runner runner = new Express4Runner(InitOptions.DEFAULT_OPTIONS);
        // 使用Lambda表达式定义today函数
        runner.addVarArgsFunction("today", (params) -> {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(new Date());
        });

        String express = "'2025-08-15' == today()";

        QLOptions.Builder optionsBuilder = QLOptions.builder()
                .traceExpression(false)
                .precise(true)
                .shortCircuitDisable(false);


        QLResult result = runner.execute(express, Collections.emptyMap(), optionsBuilder.build());
        System.out.println(result.getResult());
    }
}
