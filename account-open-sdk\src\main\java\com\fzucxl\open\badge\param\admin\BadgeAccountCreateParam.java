﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建勋章账户参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccountCreateParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 账户类型
     */
    private String accountType;    
    /**
     * 状态：ACTIVE(激活), INACTIVE(未激活), FROZEN(冻结)
     */
    private String status;    
    /**
     * 操作人ID
     */
    private Long operatorId;}
