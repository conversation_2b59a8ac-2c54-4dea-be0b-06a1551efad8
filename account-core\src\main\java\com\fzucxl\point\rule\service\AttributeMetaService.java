package com.fzucxl.point.rule.service;

import com.fzucxl.point.rule.dto.CreateAttributeRequest;
import com.fzucxl.point.rule.dto.UpdateAttributeRequest;
import com.fzucxl.point.rule.dto.AttributeTestResult;
import com.fzucxl.entity.attribute.AttributeMeta;
import com.fzucxl.point.rule.engine.AttributeExecutor;
import com.fzucxl.point.rule.repository.AttributeMetaRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 属性元数据service
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class AttributeMetaService {
    
    private static final Logger log = LoggerFactory.getLogger(AttributeMetaService.class);
    
    @Inject
    private AttributeMetaRepository metaRepository;
    
    @Inject
    private AttributeExecutor attributeExecutor;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 创建属性表达式
     */
    public AttributeMeta createAttribute(CreateAttributeRequest request) {
        try {
            // 检查表达式代码是否已存在
            if (metaRepository.existsByAttributeCode(request.getAttributeCode())) {
                throw new RuntimeException("表达式代码已存在: " + request.getAttributeCode());
            }
            
            // 创建实体
            AttributeMeta meta = new AttributeMeta();
            meta.setAttributeCode(request.getAttributeCode());
            meta.setAttributeName(request.getAttributeName());
            meta.setDescription(request.getDescription());
            meta.setDataSource(request.getDataSource());
            meta.setTargetTable(request.getTargetTable());
            meta.setAggregateFunction(request.getAggregateFunction());
            meta.setAggregateField(request.getAggregateField());
            meta.setCacheTtl(request.getCacheTtl());
            meta.setDefaultValue(request.getDefaultValue());
            meta.setCreatedBy(request.getCreatedBy());
            meta.setUpdatedBy(request.getCreatedBy());
            
            // 转换过滤条件为JSON
            if (request.getFilterConditions() != null) {
                String filterJson = objectMapper.writeValueAsString(request.getFilterConditions());
                meta.setFilterConditions(filterJson);
            }
            
            // 转换时间范围配置为JSON
            if (request.getTimeRangeConfig() != null) {
                String timeRangeJson = objectMapper.writeValueAsString(request.getTimeRangeConfig());
                meta.setTimeRangeConfig(timeRangeJson);
            }
            
            // 保存
            AttributeMeta saved = metaRepository.save(meta);
            log.info("创建属性成功: {}", saved.getAttributeCode());
            
            return saved;
            
        } catch (Exception e) {
            log.error("创建属性表达式失败", e);
            throw new RuntimeException("创建属性表达式失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 更新属性表达式
     */
    public AttributeMeta updateAttribute(Long id, UpdateAttributeRequest request) {
        try {
            AttributeMeta meta = metaRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("属性表达式不存在: " + id));
            
            // 更新字段
            if (request.getAttributeName() != null) {
                meta.setAttributeName(request.getAttributeName());
            }
            if (request.getDescription() != null) {
                meta.setDescription(request.getDescription());
            }
            if (request.getDataSource() != null) {
                meta.setDataSource(request.getDataSource());
            }
            if (request.getTargetTable() != null) {
                meta.setTargetTable(request.getTargetTable());
            }
            if (request.getAggregateFunction() != null) {
                meta.setAggregateFunction(request.getAggregateFunction());
            }
            if (request.getAggregateField() != null) {
                meta.setAggregateField(request.getAggregateField());
            }
            if (request.getCacheTtl() != null) {
                meta.setCacheTtl(request.getCacheTtl());
            }
            if (request.getDefaultValue() != null) {
                meta.setDefaultValue(request.getDefaultValue());
            }
            if (request.getUpdatedBy() != null) {
                meta.setUpdatedBy(request.getUpdatedBy());
            }
            
            // 更新过滤条件
            if (request.getFilterConditions() != null) {
                String filterJson = objectMapper.writeValueAsString(request.getFilterConditions());
                meta.setFilterConditions(filterJson);
            }
            
            // 更新时间范围配置
            if (request.getTimeRangeConfig() != null) {
                String timeRangeJson = objectMapper.writeValueAsString(request.getTimeRangeConfig());
                meta.setTimeRangeConfig(timeRangeJson);
            }
            
            meta.setUpdatedTime(LocalDateTime.now());
            
            // 保存
            AttributeMeta saved = metaRepository.save(meta);
            log.info("更新属性成功: {}", saved.getAttributeCode());
            
            return saved;
            
        } catch (Exception e) {
            log.error("更新属性表达式失败", e);
            throw new RuntimeException("更新属性表达式失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 测试属性表达式
     */
    public AttributeTestResult testAttribute(String attributeCode, Map<String, Object> testParams) {
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行属性表达式
            Object result = attributeExecutor.executeAttribute(attributeCode, testParams);
            
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 构建测试结果
            AttributeTestResult testResult = new AttributeTestResult();
            testResult.setAttributeCode(attributeCode);
            testResult.setTestParams(testParams);
            testResult.setResult(result);
            testResult.setExecutionTime(executionTime);
            testResult.setSuccess(true);
            testResult.setMessage("测试成功");
            
            log.info("测试属性成功: attributeCode={}, result={}, executionTime={}ms",
                    attributeCode, result, executionTime);
            
            return testResult;
            
        } catch (Exception e) {
            log.error("测试属性失败: attributeCode={}", attributeCode, e);
            
            AttributeTestResult testResult = new AttributeTestResult();
            testResult.setAttributeCode(attributeCode);
            testResult.setTestParams(testParams);
            testResult.setResult(null);
            testResult.setExecutionTime(0L);
            testResult.setSuccess(false);
            testResult.setMessage("测试失败: " + e.getMessage());
            
            return testResult;
        }
    }
    
    /**
     * 删除属性表达式（软删除）
     */
    public void deleteAttribute(Long id) {
        try {
            AttributeMeta meta = metaRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("属性表达式不存在: " + id));
            
            meta.setStatus("INACTIVE");
            meta.setUpdatedTime(LocalDateTime.now());
            
            metaRepository.save(meta);
            log.info("删除属性成功: {}", meta.getAttributeCode());
            
        } catch (Exception e) {
            log.error("删除属性表达式失败", e);
            throw new RuntimeException("删除属性表达式失败: " + e.getMessage(), e);
        }
    }
}