﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新勋章展示状态参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeDisplayUpdateParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 展示状态：SHOW(展示), HIDE(隐藏)
     */
    private String displayStatus;    
    /**
     * 交易ID
     */
    private String transactionId;}
