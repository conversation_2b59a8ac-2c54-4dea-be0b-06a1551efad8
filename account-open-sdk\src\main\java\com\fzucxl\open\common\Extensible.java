package com.fzucxl.open.common;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;

@Data
public class Extensible {
    @Schema(title = "扩展字段")
    private Map<String, Object> extension = new HashMap<String, Object>();
    @JsonAnySetter
    public void setExtension(String key, Object value) {
        this.extension.put(key, value);
    }
    //enabled = true开启后本方法将卡券自定义属性动态扩展到本对象最外层,如果想统一放到extData对象则请设置为false
    @JsonAnyGetter(enabled = true)
    public Map<String, Object> getExtension() {
        return this.extension;
    }

    public Object getExtension(String key) {
        return extension.get(key);
    }
}
