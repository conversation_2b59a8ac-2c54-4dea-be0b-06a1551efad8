package com.fzucxl.point.rule.dto;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.serde.annotation.Serdeable;
import lombok.Data;

import java.util.Map;

/**
 * 属性表达式测试结果DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Serdeable
@Data
public class AttributeTestResult {
    
    private String attributeCode;
    private String testUserId;
    private Map<String, Object> testParams;
    private Object result;
    private Long executionTime;
    private boolean success;
    private String message;
}