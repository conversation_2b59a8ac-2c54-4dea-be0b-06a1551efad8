package com.fzucxl.entity.level;

import com.fzucxl.open.base.AccountStatus;
import io.micronaut.data.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("user_level")
@Data
public class UserLevel {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 当前等级
     */
    private Integer level = 1;

    /**
     * 等级名称
     */
    private Integer levelName = 1;
    /**
     * 等级升级时间
     */
    private LocalDateTime levelUpTime;
    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;
    /**
     * 失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 账户状态：ACTIVE-正常，INACTIVE-停用，FROZEN-冻结，DELETED-已删除
     */
    private AccountStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
