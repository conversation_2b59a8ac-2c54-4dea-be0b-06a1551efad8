package com.fzucxl.point.rule.engine;

import com.fzucxl.common.utils.JsonUtils;
import com.fzucxl.entity.attribute.AttributeMeta;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.StringJoiner;

/**
 * 动态SQL生成器
 * 根据AttributeExpressionMeta配置生成查询SQL
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class DynamicSqlGenerator {
    
    private static final Logger log = LoggerFactory.getLogger(DynamicSqlGenerator.class);
    
    /**
     * 生成查询SQL
     * 
     * @param meta 属性元数据
     * @param params 查询参数
     * @return 生成的SQL
     */
    public String generateQuery(AttributeMeta meta, Map<String, Object> params) {
        StringBuilder sql = new StringBuilder();
        
        // SELECT子句
        sql.append("SELECT ");
        if (meta.getAggregateFunction() != null && !meta.getAggregateFunction().isEmpty()) {
            sql.append(meta.getAggregateFunction().toUpperCase()).append("(");
            if (meta.getAggregateField() != null && !meta.getAggregateField().isEmpty()) {
                sql.append(meta.getAggregateField());
            } else {
                sql.append("*");
            }
            sql.append(")");
        } else {
            sql.append("*");
        }
        
        // FROM子句
        sql.append(" FROM ").append(meta.getTargetTable());
        
        // WHERE子句
        String whereClause = buildWhereClause(meta, params);
        if (!whereClause.isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
            sql.append(" AND user_id = ?");
        }
        
        String finalSql = sql.toString();
        log.debug("生成动态SQL: {}", finalSql);
        
        return finalSql;
    }
    
    /**
     * 构建WHERE子句
     */
    private String buildWhereClause(AttributeMeta meta, Map<String, Object> params) {
        StringJoiner conditions = new StringJoiner(" AND ");
        
        // 添加过滤条件
        if (meta.getFilterConditions() != null) {
            String filterCondition = buildFilterConditions(meta.getFilterConditions(), params);
            if (!filterCondition.isEmpty()) {
                conditions.add(filterCondition);
            }
        }
        
        // 添加时间范围条件
        if (meta.getTimeRangeConfig() != null) {
            String timeCondition = buildTimeRangeCondition(meta.getTimeRangeConfig());
            if (!timeCondition.isEmpty()) {
                conditions.add(timeCondition);
            }
        }
        
        return conditions.toString();
    }
    
    /**
     * 构建过滤条件
     */
    private String buildFilterConditions(String filterConditionsJson, Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> conditions = JsonUtils.parseMap(filterConditionsJson);
            
            StringJoiner conditionJoiner = new StringJoiner(" AND ");
            
            for (Map.Entry<String, Object> entry : conditions.entrySet()) {
                String field = entry.getKey();
                Object conditionValue = entry.getValue();
                
                if (conditionValue instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> conditionMap = (Map<String, Object>) conditionValue;
                    String condition = buildSingleCondition(field, conditionMap, params);
                    if (!condition.isEmpty()) {
                        conditionJoiner.add(condition);
                    }
                } else {
                    // 简单等值条件
                    conditionJoiner.add(field + " = ?");
                    params.put(field, conditionValue);
                }
            }
            
            return conditionJoiner.toString();
            
        } catch (Exception e) {
            log.error("解析过滤条件失败: {}", filterConditionsJson, e);
            return "";
        }
    }
    
    /**
     * 构建单个条件
     */
    private String buildSingleCondition(String field, Map<String, Object> conditionMap, Map<String, Object> params) {
        String operator = (String) conditionMap.get("operator");
        Object value = conditionMap.get("value");
        
        if (operator == null) {
            operator = "=";
        }
        
        switch (operator.toUpperCase()) {
            case "=":
            case "EQ":
                params.put(field, value);
                return field + " = ?";
                
            case "!=":
            case "NE":
                params.put(field, value);
                return field + " != ?";
                
            case ">":
            case "GT":
                params.put(field, value);
                return field + " > ?";
                
            case ">=":
            case "GTE":
                params.put(field, value);
                return field + " >= ?";
                
            case "<":
            case "LT":
                params.put(field, value);
                return field + " < ?";
                
            case "<=":
            case "LTE":
                params.put(field, value);
                return field + " <= ?";
                
            case "LIKE":
                params.put(field, "%" + value + "%");
                return field + " LIKE ?";
                
            case "IN":
                if (value instanceof String) {
                    String[] values = ((String) value).split(",");
                    StringJoiner inClause = new StringJoiner(",", "(", ")");
                    for (String v : values) {
                        inClause.add("?");
                        params.put(field + "_" + v.trim(), v.trim());
                    }
                    return field + " IN " + inClause.toString();
                }
                break;
                
            case "IS_NULL":
                return field + " IS NULL";
                
            case "IS_NOT_NULL":
                return field + " IS NOT NULL";
                
            default:
                log.warn("不支持的操作符: {}", operator);
                return "";
        }
        
        return "";
    }
    
    /**
     * 构建时间范围条件
     */
    private String buildTimeRangeCondition(String timeRangeConfigJson) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> config = JsonUtils.parseMap(timeRangeConfigJson);
            
            String type = (String) config.get("type");
            Object value = config.get("value");
            String unit = (String) config.get("unit");
            String field = (String) config.getOrDefault("field", "created_time");
            
            if ("RELATIVE".equals(type) && value instanceof Number) {
                int amount = ((Number) value).intValue();
                LocalDateTime endTime = LocalDateTime.now();
                LocalDateTime startTime;
                
                switch (unit.toUpperCase()) {
                    case "DAYS":
                        startTime = endTime.minusDays(amount);
                        break;
                    case "HOURS":
                        startTime = endTime.minusHours(amount);
                        break;
                    case "MONTHS":
                        startTime = endTime.minusMonths(amount);
                        break;
                    case "YEARS":
                        startTime = endTime.minusYears(amount);
                        break;
                    default:
                        log.warn("不支持的时间单位: {}", unit);
                        return "";
                }
                
                return field + " >= '" + startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + 
                       "' AND " + field + " <= '" + endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + "'";
            }
            
        } catch (Exception e) {
            log.error("解析时间范围配置失败: {}", timeRangeConfigJson, e);
        }
        
        return "";
    }
}