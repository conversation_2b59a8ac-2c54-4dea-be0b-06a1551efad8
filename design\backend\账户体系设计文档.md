# 账户体系设计文档

## 1. 项目概述

### 1.1 项目背景
本系统是面向电商、零售、鞋服、耐消品、快消品、化妆品等行业的品牌方账户体系管理系统，专注于积分、等级、成长值、勋章等账户激励体系的管理，与会员管理服务解耦，通过统一的会员ID进行关联。

### 1.2 核心价值
- **账户激励体系**：构建完整的积分、等级、成长值、勋章激励机制
- **业务规则引擎**：灵活可配置的业务规则，支持多样化的激励策略
- **数据驱动运营**：提供丰富的数据分析，支持精准营销决策
- **系统解耦设计**：独立的账户体系服务，便于系统集成和扩展

### 1.3 系统定位
- **服务范围**：积分管理、等级体系、成长值计算、勋章系统
- **集成方式**：通过会员ID与会员管理系统关联
- **应用场景**：用户激励、营销活动、数据分析、业务增长

## 2. 业务需求分析

### 2.1 积分系统需求

#### 2.1.1 积分获取规则
```
积分获取场景及规则：
1. 购买奖励：消费1元获得1积分（可配置倍率）
2. 任务奖励：
   - 每日签到：5积分
   - 商品评价：10积分/次
   - 分享商品：5积分/次
   - 完成任务：根据任务类型给予相应积分
3. 推荐奖励：成功推荐新用户获得200积分
4. 活动奖励：参与营销活动获得额外积分
5. 管理员发放：运营人员手动发放积分
```

#### 2.1.2 积分使用场景
```
积分使用规则：
1. 积分抵扣：100积分=1元现金（可配置比例）
2. 积分兑换：
   - 优惠券兑换
   - 实物商品兑换
   - 服务权益兑换
3. 积分抽奖：参与积分抽奖活动
4. 积分转赠：用户间积分转赠（可选功能）
```

#### 2.1.3 积分管理规则
- **有效期管理**：积分有效期2年，按先进先出原则使用
- **冻结机制**：异常交易积分冻结，待核实后释放
- **回收机制**：退单时按比例回收积分
- **上限控制**：单日获取积分上限，防止刷分行为

### 2.2 等级体系需求

#### 2.2.1 等级划分标准
```
等级体系（基于成长值）：
- 青铜等级：0-999成长值
- 白银等级：1000-2999成长值  
- 黄金等级：3000-7999成长值
- 铂金等级：8000-19999成长值
- 钻石等级：20000+成长值
```

#### 2.2.2 等级权益设计
```
等级权益差异化：
1. 积分倍率：
   - 青铜：1倍积分
   - 白银：1.2倍积分
   - 黄金：1.5倍积分
   - 铂金：2倍积分
   - 钻石：3倍积分

2. 专属权益：
   - 白银+：生日专属优惠券
   - 黄金+：包邮门槛降低
   - 铂金+：专属客服通道
   - 钻石：新品优先购买权

3. 折扣权益：
   - 黄金+：会员日9.5折
   - 铂金+：会员日9折
   - 钻石：会员日8.5折
```

#### 2.2.3 等级升降规则
- **升级条件**：基于12个月滚动成长值计算
- **保级机制**：连续12个月无成长值增长则降级
- **升级奖励**：等级提升时赠送相应积分和优惠券

### 2.3 成长值机制需求

#### 2.3.1 成长值获取规则
```
成长值获取场景：
1. 消费获取：消费1元获得1成长值
2. 互动获取：
   - 商品评价：10成长值
   - 晒单分享：20成长值
   - 参与活动：50成长值
3. 推荐获取：成功推荐获得100成长值
4. 任务获取：完成指定任务获得成长值
5. 管理员发放：运营人员手动调整成长值
```

#### 2.3.2 成长值特性
- **不可消费**：成长值只用于等级计算，不可兑换商品
- **累计计算**：成长值累计计算，影响用户等级
- **时效性**：基于滚动12个月计算有效成长值

### 2.4 勋章系统需求

#### 2.4.1 勋章类型设计
```
勋章体系分类：
1. 消费类勋章：
   - 首单达人：完成首次购买
   - 消费新星：月消费达到1000元
   - 消费达人：年消费达到10000元
   - 消费王者：年消费达到50000元

2. 互动类勋章：
   - 评价专家：累计评价100次
   - 分享达人：累计分享50次
   - 社区活跃：参与社区互动100次

3. 忠诚类勋章：
   - 忠实粉丝：连续12个月有消费
   - 品牌挚友：注册满3年
   - 终身客户：累计消费达到100000元

4. 特殊类勋章：
   - 推荐大使：成功推荐10位新用户
   - 内测先锋：参与新功能内测
   - 生日之星：生日月份专属勋章
```

#### 2.4.2 勋章权益
- **展示价值**：勋章展示，提升用户荣誉感
- **实际权益**：部分勋章附带优惠券或积分奖励
- **社交功能**：勋章分享到社交平台，增加品牌曝光

## 3. 系统架构设计

### 3.1 整体架构

```
系统分层架构：
┌─────────────────────────────────────────┐
│              前端展示层                    │
├─────────────────────────────────────────┤
│              API网关层                    │
├─────────────────────────────────────────┤
│              业务服务层                    │
│  ┌─────────┬─────────┬─────────┬─────────┐ │
│  │积分服务  │等级服务  │成长值服务│勋章服务  │ │
│  ├─────────┼─────────┼─────────┼─────────┤ │
│  │规则引擎  │任务服务  │消息服务  │报表服务  │ │
│  └─────────┴─────────┴─────────┴─────────┘ │
├─────────────────────────────────────────┤
│              数据访问层                    │
├─────────────────────────────────────────┤
│              数据存储层                    │
│  ┌─────────┬─────────┬─────────┬─────────┐ │
│  │MySQL    │Redis    │MongoDB  │ES       │ │
│  └─────────┴─────────┴─────────┴─────────┘ │
└─────────────────────────────────────────┘
```

### 3.2 核心服务模块

#### 3.2.1 积分服务 (Point Service)
- **积分获取处理**
- **积分消费处理**
- **积分规则引擎**
- **积分流水管理**

#### 3.2.2 等级服务 (Level Service)
- **成长值计算**
- **等级升降处理**
- **等级权益管理**
- **等级变更通知**

#### 3.2.3 成长值服务 (Growth Service)
- **成长值获取处理**
- **成长值计算规则**
- **成长值历史记录**
- **成长值统计分析**

#### 3.2.4 勋章服务 (Badge Service)
- **勋章获得判断**
- **勋章发放处理**
- **勋章展示管理**
- **勋章权益处理**

#### 3.2.5 规则引擎 (Rule Engine)
- **业务规则配置**
- **规则执行引擎**
- **规则版本管理**
- **规则效果统计**

### 3.3 数据模型设计

#### 3.3.1 积分账户表 (user_point)
```sql
CREATE TABLE user_point (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id BIGINT NOT NULL COMMENT '会员ID',
    total_point BIGINT DEFAULT 0 COMMENT '累计获得积分',
    available_point BIGINT DEFAULT 0 COMMENT '可用积分',
    frozen_point BIGINT DEFAULT 0 COMMENT '冻结积分',
    used_point BIGINT DEFAULT 0 COMMENT '已使用积分',
    expire_point BIGINT DEFAULT 0 COMMENT '已过期积分',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_member_id (member_id)
);
```

#### 3.3.2 积分流水表 (point_transaction)
```sql
CREATE TABLE point_transaction (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    transaction_no VARCHAR(32) UNIQUE NOT NULL COMMENT '交易流水号',
    member_id BIGINT NOT NULL COMMENT '会员ID',
    point_change BIGINT NOT NULL COMMENT '积分变动数量',
    transaction_type TINYINT NOT NULL COMMENT '交易类型 1获得2消费3过期4冻结5解冻',
    business_type VARCHAR(20) COMMENT '业务类型',
    business_id VARCHAR(50) COMMENT '业务ID',
    description VARCHAR(200) COMMENT '交易描述',
    expire_time DATETIME COMMENT '过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_member_id (member_id),
    INDEX idx_transaction_no (transaction_no),
    INDEX idx_created_time (created_time)
);
```

#### 3.3.3 成长值账户表 (growth_account)
```sql
CREATE TABLE growth_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id BIGINT NOT NULL COMMENT '会员ID',
    total_growth_value BIGINT DEFAULT 0 COMMENT '累计成长值',
    current_growth_value BIGINT DEFAULT 0 COMMENT '当前有效成长值（12个月滚动）',
    current_level TINYINT DEFAULT 1 COMMENT '当前等级',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_member_id (member_id)
);
```

#### 3.3.4 成长值记录表 (growth_value_record)
```sql
CREATE TABLE growth_value_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id BIGINT NOT NULL COMMENT '会员ID',
    growth_value BIGINT NOT NULL COMMENT '成长值变动',
    business_type VARCHAR(20) COMMENT '业务类型',
    business_id VARCHAR(50) COMMENT '业务ID',
    description VARCHAR(200) COMMENT '描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_member_id (member_id),
    INDEX idx_created_time (created_time)
);
```

#### 3.3.5 等级配置表 (level_config)
```sql
CREATE TABLE level_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_code TINYINT UNIQUE NOT NULL COMMENT '等级代码',
    level_name VARCHAR(20) NOT NULL COMMENT '等级名称',
    min_growth_value BIGINT NOT NULL COMMENT '最小成长值',
    max_growth_value BIGINT COMMENT '最大成长值',
    point_ratio DECIMAL(3,2) DEFAULT 1.00 COMMENT '积分倍率',
    discount_ratio DECIMAL(3,2) COMMENT '折扣比例',
    free_shipping_threshold DECIMAL(10,2) COMMENT '包邮门槛',
    level_icon VARCHAR(200) COMMENT '等级图标',
    level_description TEXT COMMENT '等级描述',
    upgrade_reward_point BIGINT DEFAULT 0 COMMENT '升级奖励积分',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.3.6 等级变更记录表 (level_change_record)
```sql
CREATE TABLE level_change_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id BIGINT NOT NULL COMMENT '会员ID',
    old_level TINYINT COMMENT '原等级',
    new_level TINYINT NOT NULL COMMENT '新等级',
    change_type TINYINT NOT NULL COMMENT '变更类型 1升级2降级',
    growth_value BIGINT COMMENT '当时成长值',
    reward_point BIGINT DEFAULT 0 COMMENT '奖励积分',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_member_id (member_id),
    INDEX idx_created_time (created_time)
);
```

#### 3.3.7 勋章配置表 (badge_config)
```sql
CREATE TABLE badge_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    badge_code VARCHAR(20) UNIQUE NOT NULL COMMENT '勋章代码',
    badge_name VARCHAR(50) NOT NULL COMMENT '勋章名称',
    badge_type TINYINT NOT NULL COMMENT '勋章类型 1消费2互动3忠诚4特殊',
    badge_icon VARCHAR(200) COMMENT '勋章图标',
    badge_description TEXT COMMENT '勋章描述',
    obtain_condition TEXT COMMENT '获得条件JSON',
    reward_point BIGINT DEFAULT 0 COMMENT '奖励积分',
    is_repeatable TINYINT DEFAULT 0 COMMENT '是否可重复获得',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.3.8 用户勋章表 (user_badge)
```sql
CREATE TABLE user_badge (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    member_id BIGINT NOT NULL COMMENT '会员ID',
    badge_id INT NOT NULL COMMENT '勋章ID',
    obtain_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    obtain_count INT DEFAULT 1 COMMENT '获得次数',
    is_display TINYINT DEFAULT 1 COMMENT '是否展示',
    INDEX idx_member_id (member_id),
    UNIQUE KEY uk_member_badge (member_id, badge_id)
);
```

#### 3.3.9 业务规则配置表 (business_rule_config)
```sql
CREATE TABLE business_rule_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_code VARCHAR(50) UNIQUE NOT NULL COMMENT '规则代码',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(20) NOT NULL COMMENT '规则类型 POINTS/GROWTH/BADGE',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型',
    rule_content TEXT NOT NULL COMMENT '规则内容JSON',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    start_time DATETIME COMMENT '生效开始时间',
    end_time DATETIME COMMENT '生效结束时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 4. 核心功能设计

### 4.1 积分系统核心流程

#### 4.1.1 积分获取流程
```mermaid
sequenceDiagram
    participant External as 外部系统
    participant Point as 积分服务
    participant Rule as 规则引擎
    participant DB as 数据库
    participant MQ as 消息队列
    
    External->>Point: 发送积分获取事件
    Point->>Rule: 查询积分规则
    Rule-->>Point: 返回积分计算结果
    Point->>DB: 更新积分账户
    Point->>DB: 记录积分流水
    Point->>MQ: 发送积分变更消息
    Point-->>External: 返回处理结果
```

#### 4.1.2 积分消费流程
```mermaid
sequenceDiagram
    participant External as 外部系统
    participant Point as 积分服务
    participant DB as 数据库
    participant MQ as 消息队列
    
    External->>Point: 请求积分扣减
    Point->>DB: 检查可用积分
    alt 积分充足
        Point->>DB: 扣减积分
        Point->>DB: 记录消费流水
        Point->>MQ: 发送积分消费消息
        Point-->>External: 返回成功
    else 积分不足
        Point-->>External: 返回失败
    end
```

### 4.2 等级系统核心流程

#### 4.2.1 等级计算流程
```mermaid
flowchart TD
    A[触发等级计算] --> B[获取用户12个月成长值]
    B --> C[查询等级配置表]
    C --> D{成长值是否达到升级条件?}
    D -->|是| E[执行升级操作]
    D -->|否| F{成长值是否低于保级条件?}
    F -->|是| G[执行降级操作]
    F -->|否| H[维持当前等级]
    E --> I[发放升级奖励]
    G --> J[发送降级通知]
    I --> K[记录等级变更日志]
    J --> K
    H --> K
    K --> L[结束]
```

### 4.3 勋章系统核心流程

#### 4.3.1 勋章获得判断流程
```mermaid
flowchart TD
    A[用户行为触发] --> B[获取相关勋章规则]
    B --> C[遍历勋章条件]
    C --> D{是否满足获得条件?}
    D -->|是| E{是否已拥有该勋章?}
    D -->|否| F[继续下一个勋章]
    E -->|否| G[发放勋章]
    E -->|是| H{勋章是否可重复获得?}
    H -->|是| I[增加获得次数]
    H -->|否| F
    G --> J[记录勋章获得记录]
    I --> J
    J --> K[发放勋章奖励]
    K --> L[发送获得通知]
    F --> M{是否还有其他勋章?}
    M -->|是| C
    M -->|否| N[结束]
    L --> N
```

## 5. 技术实现方案

### 5.1 技术选型

#### 5.1.1 后端技术栈
- **开发框架**：Micronaut 4.x + Micronaut Cloud
- **数据库**：MySQL 8.0（主数据库）+ Redis（缓存）
- **消息队列**：RocketMQ / Kafka
- **搜索引擎**：Elasticsearch（数据分析）
- **任务调度**：XXL-Job
- **API网关**：Micronaut Gateway
- **服务注册**：Nacos
- **监控工具**：Prometheus + Grafana

#### 5.1.2 前端技术栈
- **管理后台**：Vue 3 + Element Plus
- **数据可视化**：ECharts

### 5.2 关键技术实现

#### 5.2.1 积分并发安全
```java
@Service
@Transactional
public class PointService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    public boolean addPoint(Long memberId, Long point, String businessType, String businessId) {
        String lockKey = "point:lock:" + memberId;
        String lockValue = UUID.randomUUID().toString();
        
        try {
            // 分布式锁防止并发问题
            Boolean locked = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));
            
            if (!locked) {
                throw new BusinessException("系统繁忙，请稍后重试");
            }
            
            // 更新积分账户
            pointAccountMapper.addPoint(memberId, point);
            
            // 记录积分流水
            PointTransaction transaction = new PointTransaction();
            transaction.setTransactionNo(generateTransactionNo());
            transaction.setMemberId(memberId);
            transaction.setPointChange(point);
            transaction.setTransactionType(1); // 获得
            transaction.setBusinessType(businessType);
            transaction.setBusinessId(businessId);
            pointTransactionMapper.insert(transaction);
            
            // 发送积分变更消息
            pointChangeEventPublisher.publishPointChangeEvent(memberId, point, businessType);
            
            return true;
        } finally {
            // 释放锁
            String currentValue = redisTemplate.opsForValue().get(lockKey);
            if (lockValue.equals(currentValue)) {
                redisTemplate.delete(lockKey);
            }
        }
    }
}
```

#### 5.2.2 规则引擎实现
```java
@Component
public class RuleEngine {
    
    public PointCalculateResult calculatePoint(String businessType, Long amount, Long memberId) {
        // 获取基础积分规则
        BusinessRuleConfig rule = businessRuleMapper.getActiveRule("POINTS", businessType);
        if (rule == null) {
            return PointCalculateResult.zero();
        }
        
        // 解析规则内容
        PointRule pointRule = JSON.parseObject(rule.getRuleContent(), PointRule.class);
        
        // 计算基础积分
        Long basePoint = (long) (amount * pointRule.getPointRatio());
        
        // 应用等级倍率
        GrowthAccount growthAccount = growthAccountMapper.getByMemberId(memberId);
        LevelRule levelConfig = levelConfigMapper.getByLevel(growthAccount.getCurrentLevel());
        Long finalPoint = (long) (basePoint * levelConfig.getPointRatio());
        
        return PointCalculateResult.builder()
                .basePoint(basePoint)
                .levelMultiplier(levelConfig.getPointRatio())
                .finalPoint(finalPoint)
                .build();
    }
    
    public List<BadgeConfig> checkBadgeConditions(Long memberId, String actionType, Map<String, Object> context) {
        List<BadgeConfig> availableBadges = new ArrayList<>();
        
        // 获取所有激活的勋章配置
        List<BadgeConfig> allBadges = badgeConfigMapper.getActiveBadges();
        
        for (BadgeConfig badge : allBadges) {
            if (evaluateBadgeCondition(memberId, badge, actionType, context)) {
                availableBadges.add(badge);
            }
        }
        
        return availableBadges;
    }
}
```

#### 5.2.3 等级升降级处理
```java
@Service
public class LevelService {
    
    public void checkLevelUpgrade(Long memberId) {
        // 获取用户当前成长值账户
        GrowthAccount growthAccount = growthAccountMapper.getByMemberId(memberId);
        if (growthAccount == null) {
            return;
        }
        
        // 计算12个月滚动成长值
        Long currentGrowthValue = calculateRollingGrowthValue(memberId);
        
        // 获取应该达到的等级
        LevelRule targetLevel = levelConfigMapper.getLevelByGrowthValue(currentGrowthValue);
        
        if (targetLevel.getLevelCode() != growthAccount.getCurrentLevel()) {
            // 执行等级变更
            updateUserLevel(memberId, growthAccount.getCurrentLevel(), targetLevel.getLevelCode(), currentGrowthValue);
        }
    }
    
    private void updateUserLevel(Long memberId, Integer oldLevel, Integer newLevel, Long growthValue) {
        // 更新用户等级
        growthAccountMapper.updateLevel(memberId, newLevel, growthValue);
        
        // 记录等级变更
        LevelChangeRecord record = new LevelChangeRecord();
        record.setMemberId(memberId);
        record.setOldLevel(oldLevel);
        record.setNewLevel(newLevel);
        record.setChangeType(newLevel > oldLevel ? 1 : 2); // 1升级 2降级
        record.setGrowthValue(growthValue);
        
        // 如果是升级，发放奖励
        if (newLevel > oldLevel) {
            LevelRule levelConfig = levelConfigMapper.getByLevel(newLevel);
            if (levelConfig.getUpgradeRewardPoint() > 0) {
                pointService.addPoint(memberId, levelConfig.getUpgradeRewardPoint(), "LEVEL_UPGRADE", String.valueOf(newLevel));
                record.setRewardPoint(levelConfig.getUpgradeRewardPoint());
            }
        }
        
        levelChangeRecordMapper.insert(record);
        
        // 发送等级变更消息
        levelChangeEventPublisher.publishLevelChangeEvent(memberId, oldLevel, newLevel);
    }
    
    private Long calculateRollingGrowthValue(Long memberId) {
        LocalDateTime twelveMonthsAgo = LocalDateTime.now().minusMonths(12);
        return growthValueRecordMapper.sumGrowthValueSince(memberId, twelveMonthsAgo);
    }
}
```

## 6. API接口设计

### 6.1 积分相关接口

#### 6.1.1 获取积分账户信息
```java
@GetMapping("/api/point/account/{memberId}")
public Result<UserPointVO> getUserPoint(@PathVariable Long memberId) {
    UserPoint account = pointService.getUserPoint(memberId);
    return Result.success(UserPointVO.from(account));
}
```

#### 6.1.2 积分获取接口
```java
@PostMapping("/api/point/add")
public Result<Boolean> addPoint(@RequestBody AddPointRequest request) {
    boolean success = pointService.addPoint(
        request.getMemberId(), 
        request.getPoint(), 
        request.getBusinessType(),
        request.getBusinessId()
    );
    return Result.success(success);
}
```

#### 6.1.3 积分消费接口
```java
@PostMapping("/api/point/consume")
public Result<Boolean> consumePoint(@RequestBody ConsumePointRequest request) {
    boolean success = pointService.consumePoint(
        request.getMemberId(),
        request.getPoint(),
        request.getBusinessType(),
        request.getBusinessId()
    );
    return Result.success(success);
}
```

### 6.2 等级相关接口

#### 6.2.1 获取用户等级信息
```java
@GetMapping("/api/level/info/{memberId}")
public Result<UserLevelVO> getUserLevel(@PathVariable Long memberId) {
    UserLevelVO levelInfo = levelService.getUserLevelInfo(memberId);
    return Result.success(levelInfo);
}
```

#### 6.2.2 获取等级配置列表
```java
@GetMapping("/api/level/config")
public Result<List<LevelRuleVO>> getLevelRules() {
    List<LevelRule> configs = levelService.getAllLevelRules();
    return Result.success(configs.stream()
        .map(LevelRuleVO::from)
        .collect(Collectors.toList()));
}
```

### 6.3 勋章相关接口

#### 6.3.1 获取用户勋章列表
```java
@GetMapping("/api/badge/user/{memberId}")
public Result<List<UserBadgeVO>> getUserBadges(@PathVariable Long memberId) {
    List<UserBadge> badges = badgeService.getUserBadges(memberId);
    return Result.success(badges.stream()
        .map(UserBadgeVO::from)
        .collect(Collectors.toList()));
}
```

#### 6.3.2 获取勋章配置列表
```java
@GetMapping("/api/badge/config")
public Result<List<BadgeConfigVO>> getBadgeConfigs() {
    List<BadgeConfig> configs = badgeService.getAllBadgeConfigs();
    return Result.success(configs.stream()
        .map(BadgeConfigVO::from)
        .collect(Collectors.toList()));
}
```

## 7. 运营管理功能

### 7.1 规则配置管理

#### 7.1.1 积分规则配置
- **获取规则配置**：不同业务场景的积分获取倍率
- **消费规则配置**：积分抵扣比例、使用限制
- **有效期配置**：积分有效期、过期提醒
- **活动规则配置**：双倍积分、特殊活动规则

#### 7.1.2 等级规则配置
- **等级门槛配置**：各等级成长值要求
- **权益配置**：各等级专属权益设置
- **升降级规则**：升级条件、保级机制
- **奖励配置**：升级奖励积分、优惠券

#### 7.1.3 勋章规则配置
- **勋章条件配置**：各类勋章获得条件
- **奖励配置**：勋章奖励积分设置
- **展示配置**：勋章图标、描述管理
- **权益配置**：勋章附带权益设置

### 7.2 营销活动支持

#### 7.2.1 积分营销活动
- **双倍积分活动**：指定时间段积分翻倍
- **任务积分活动**：完成特定任务获得积分
- **推荐有礼**：推荐新用户积分奖励
- **节日特权**：节日期间积分加倍

#### 7.2.2 等级营销活动
- **等级冲刺**：限时等级升级活动
- **等级专享**：不同等级专属优惠
- **保级奖励**：等级保持奖励机制

#### 7.2.3 勋章营销活动
- **勋章挑战**：限时勋章获得活动
- **勋章展示**：勋章炫耀功能
- **勋章兑换**：勋章兑换特殊权益

### 7.3 数据分析报表

#### 7.3.1 积分分析报表
- **积分概览**：积分发放总量、消费总量、库存
- **积分流向**：获取渠道分析、消费场景分析
- **积分效果**：积分对用户行为的影响分析
- **积分预警**：异常积分行为监控

#### 7.3.2 等级分析报表
- **等级分布**：各等级用户数量和占比
- **等级流转**：升级降级趋势分析
- **等级价值**：不同等级用户价值分析
- **权益使用**：等级权益使用情况统计

#### 7.3.3 勋章分析报表
- **勋章获得统计**：各类勋章获得情况
- **勋章活跃度**：勋章对用户活跃度的影响
- **勋章价值分析**：勋章系统的业务价值
- **勋章优化建议**：基于数据的优化建议

## 8. 系统集成方案

### 8.1 订单系统集成

#### 8.1.1 订单完成回调
```json
{
  "eventType": "ORDER_COMPLETED",
  "orderId": "ORD202501051001",
  "memberId": 123456,
  "orderAmount": 299.00,
  "payAmount": 279.00,
  "pointUsed": 2000,
  "orderTime": "2025-01-05 14:30:00",
  "products": [
    {
      "productId": "P001",
      "categoryId": "C001",
      "quantity": 2,
      "price": 149.50
    }
  ]
}
```

#### 8.1.2 退单处理回调
```json
{
  "eventType": "ORDER_REFUNDED",
  "orderId": "ORD202501051001",
  "refundId": "REF202501051002",
  "memberId": 123456,
  "refundAmount": 149.50,
  "pointToReclaim": 1000,
  "refundTime": "2025-01-06 10:15:00"
}
```

### 8.2 营销系统集成

#### 8.2.1 优惠券发放接口
```java
@RestController
@RequestMapping("/api/marketing")
public class MarketingController {
    
    @PostMapping("/coupon/grant")
    public Result grantCoupon(@RequestBody CouponGrantRequest request) {
        // 验证用户等级权限
        GrowthAccount growthAccount = growthAccountService.getByMemberId(request.getMemberId());
        LevelRule levelConfig = levelConfigService.getByLevel(growthAccount.getCurrentLevel());
        
        if (!levelService.hasPrivilege(levelConfig.getLevelCode(), "COUPON_GRANT")) {
            return Result.error("用户等级不足");
        }
        
        // 发放优惠券
        return couponService.grantCoupon(request);
    }
}
```

### 8.3 会员系统集成

#### 8.3.1 账户信息查询接口
```java
@RestController
@RequestMapping("/api/account")
public class AccountController {
    
    @GetMapping("/summary/{memberId}")
    public Result<AccountSummaryVO> getAccountSummary(@PathVariable Long memberId) {
        AccountSummaryVO summary = new AccountSummaryVO();
        
        // 积分信息
        UserPoint pointAccount = pointService.getUserPoint(memberId);
        summary.setPointInfo(PointInfoVO.from(pointAccount));
        
        // 等级信息
        UserLevelVO levelInfo = levelService.getUserLevelInfo(memberId);
        summary.setLevelInfo(levelInfo);
        
        // 勋章信息
        List<UserBadge> badges = badgeService.getUserBadges(memberId);
        summary.setBadges(badges.stream()
            .map(UserBadgeVO::from)
            .collect(Collectors.toList()));
        
        return Result.success(summary);
    }
}
```

## 9. 性能优化方案

### 9.1 数据库优化

#### 9.1.1 索引优化
```sql
-- 积分流水表索引优化
CREATE INDEX idx_member_created ON point_transaction(member_id, created_time);
CREATE INDEX idx_business_type ON point_transaction(business_type, created_time);

-- 成长值记录表索引优化
CREATE INDEX idx_member_growth ON growth_value_record(member_id, created_time);

-- 用户勋章表索引优化
CREATE INDEX idx_member_badge_time ON user_badge(member_id, obtain_time);
```

#### 9.1.2 分表策略
```sql
-- 积分流水按月分表
CREATE TABLE point_transaction_202501 LIKE point_transaction;
CREATE TABLE point_transaction_202502 LIKE point_transaction;

-- 成长值记录按年分表
CREATE TABLE growth_value_record_2025 LIKE growth_value_record;
```

### 9.2 缓存策略

#### 9.2.1 Redis缓存设计
```java
@Service
public class AccountCacheService {
    
    private static final String POINTS_ACCOUNT_KEY = "account:point:";
    private static final String GROWTH_ACCOUNT_KEY = "account:growth:";
    private static final String USER_LEVEL_KEY = "account:level:";
    
    public UserPoint getUserPoint(Long memberId) {
        String key = POINTS_ACCOUNT_KEY + memberId;
        UserPoint account = redisTemplate.opsForValue().get(key);
        
        if (account == null) {
            account = pointAccountMapper.getByMemberId(memberId);
            if (account != null) {
                redisTemplate.opsForValue().set(key, account, Duration.ofHours(1));
            }
        }
        
        return account;
    }
}
```

#### 9.2.2 缓存更新策略
- **写入时更新**：数据变更时同步更新缓存
- **定时刷新**：定时任务刷新热点数据
- **缓存预热**：系统启动时预加载常用数据

### 9.3 异步处理

#### 9.3.1 消息队列处理
```java
@Component
public class AccountEventProducer {
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    public void sendPointChangeEvent(Long memberId, Long pointChange, String businessType) {
        PointChangeEvent event = new PointChangeEvent();
        event.setMemberId(memberId);
        event.setPointChange(pointChange);
        event.setBusinessType(businessType);
        
        rocketMQTemplate.convertAndSend("POINTS_CHANGE_TOPIC", event);
    }
}

@RocketMQMessageListener(topic = "POINTS_CHANGE_TOPIC", consumerGroup = "account-service")
@Component
public class PointChangeEventConsumer implements RocketMQListener<PointChangeEvent> {
    
    @Override
    public void onMessage(PointChangeEvent event) {
        // 异步处理积分变更相关业务
        levelService.checkLevelUpgrade(event.getMemberId());
        badgeService.checkAndGrantBadges(event.getMemberId(), event.getBusinessType(), null);
        notificationService.sendPointChangeNotification(event);
    }
}
```

## 10. 安全设计

### 10.1 数据安全

#### 10.1.1 参数验证
```java
@PostMapping("/point/add")
public Result addPoint(@Valid @RequestBody AddPointRequest request) {
    // 参数验证通过后处理业务逻辑
    return pointService.addPoint(request);
}

public class AddPointRequest {
    @NotNull(message = "会员ID不能为空")
    private Long memberId;
    
    @Min(value = 1, message = "积分数量必须大于0")
    @Max(value = 10000, message = "单次积分数量不能超过10000")
    private Long point;
    
    @NotBlank(message = "业务类型不能为空")
    private String businessType;
}
```

#### 10.1.2 接口限流
```java
@RestController
@RequestMapping("/api/account")
public class AccountController {
    
    @RateLimiter(name = "accountQuery", fallbackMethod = "queryFallback")
    @GetMapping("/{memberId}")
    public Result<AccountSummaryVO> getAccountSummary(@PathVariable Long memberId) {
        return Result.success(accountService.getAccountSummary(memberId));
    }
    
    public Result<AccountSummaryVO> queryFallback(Long memberId, Exception ex) {
        return Result.error("系统繁忙，请稍后重试");
    }
}
```

### 10.2 业务安全

#### 10.2.1 防刷机制
```java
@Service
public class AntiCheatService {
    
    public boolean checkPointLimit(Long memberId, String businessType, Long point) {
        String key = "point:limit:" + memberId + ":" + businessType;
        String dailyKey = key + ":" + LocalDate.now().toString();
        
        // 检查单日获取积分上限
        Long dailyPoint = redisTemplate.opsForValue().get(dailyKey);
        if (dailyPoint != null && dailyPoint + point > MAX_DAILY_POINTS) {
            return false;
        }
        
        // 更新单日积分计数
        redisTemplate.opsForValue().increment(dailyKey, point);
        redisTemplate.expire(dailyKey, Duration.ofDays(1));
        
        return true;
    }
}
```

## 11. 监控告警

### 11.1 业务监控

#### 11.1.1 关键指标监控
```java
@Component
public class AccountMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Counter pointAddCounter;
    private final Timer pointProcessTimer;
    
    public AccountMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.pointAddCounter = Counter.builder("account.point.add.count")
                .description("积分增加次数")
                .register(meterRegistry);
        this.pointProcessTimer = Timer.builder("account.point.process.time")
                .description("积分处理耗时")
                .register(meterRegistry);
    }
    
    public void recordPointAdd() {
        pointAddCounter.increment();
    }
    
    public void recordPointProcessTime(Duration duration) {
        pointProcessTimer.record(duration);
    }
}
```

#### 11.1.2 异常监控
```java
@Component
public class AccountExceptionHandler {
    
    @EventListener
    public void handlePointException(PointProcessException exception) {
        // 记录异常日志
        log.error("积分处理异常", exception);
        
        // 发送告警
        alertService.sendAlert("积分处理异常", exception.getMessage());
        
        // 记录异常指标
        Metrics.counter("account.point.process.error", 
                "type", exception.getErrorType(),
                "member", String.valueOf(exception.getMemberId()))
                .increment();
    }
}
```

### 11.2 系统监控

#### 11.2.1 健康检查
```java
@Component
public class AccountSystemHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate redisTemplate;
    
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            dataSource.getConnection().close();
            
            // 检查Redis连接
            redisTemplate.opsForValue().get("health:check");
            
            return Health.up()
                    .withDetail("database", "UP")
                    .withDetail("redis", "UP")
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}
```

## 12. 部署方案

### 12.1 容器化部署

#### 12.1.1 Dockerfile
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app

COPY target/account-system.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 12.1.2 Docker Compose
```yaml
version: '3.8'
services:
  account-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: account_system
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 13. 测试方案

### 13.1 单元测试

#### 13.1.1 服务层测试
```java
@ExtendWith(MockitoExtension.class)
class PointServiceTest {
    
    @Mock
    private UserPointMapper pointAccountMapper;
    
    @Mock
    private PointTransactionMapper pointTransactionMapper;
    
    @InjectMocks
    private PointService pointService;
    
    @Test
    void testAddPoint() {
        // Given
        Long memberId = 123L;
        Long point = 100L;
        String businessType = "PURCHASE";
        
        // When
        boolean result = pointService.addPoint(memberId, point, businessType, "ORDER_001");
        
        // Then
        assertTrue(result);
        verify(pointAccountMapper).addPoint(memberId, point);
        verify(pointTransactionMapper).insert(any(PointTransaction.class));
    }
}
```

### 13.2 集成测试

#### 13.2.1 API测试
```java
@MicronautTest
@TestPropertySource(properties = {
    "datasources.default.url=jdbc:h2:mem:testdb",
    "redis.uri=redis://localhost:6379"
})
class AccountControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetAccountSummary() {
        // Given
        Long memberId = 1L;
        
        // When
        ResponseEntity<Result> response = restTemplate.getForEntity(
            "/api/account/summary/" + memberId, Result.class);
        
        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}
```

## 14. 项目总结

### 14.1 系统特点

1. **业务完整性**：涵盖积分、等级、成长值、勋章等完整的账户体系
2. **技术先进性**：采用微服务架构，支持高并发和高可用
3. **扩展性强**：模块化设计，便于功能扩展和业务迭代
4. **运营友好**：提供丰富的配置管理和数据分析功能
5. **安全可靠**：完善的安全机制和监控告警体系

### 14.2 核心优势

- **高性能**：通过缓存、异步处理、数据库优化等手段保证系统性能
- **高可用**：分布式架构设计，支持故障自动恢复
- **易维护**：清晰的代码结构和完善的文档，降低维护成本
- **可观测**：全面的监控指标和日志记录，便于问题定位

### 14.3 应用价值

1. **提升用户活跃度**：通过积分、等级、勋章等激励机制，提升用户参与度
2. **增强用户粘性**：差异化的等级权益，增强用户对品牌的忠诚度
3. **支持精准营销**：基于账户数据分析，实现个性化营销推荐
4. **驱动业务增长**：通过数据洞察，优化运营策略，促进业务增长

本设计文档为账户体系管理系统提供了完整的技术方案和实施指导，专注于积分、等级、成长值、勋章等核心功能，与会员管理系统解耦，可以作为项目开发和运维的重要参考依据。
