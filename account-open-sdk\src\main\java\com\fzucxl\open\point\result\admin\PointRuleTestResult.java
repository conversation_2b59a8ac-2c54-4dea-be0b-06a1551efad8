﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 测试积分规则结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleTestResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 计算得出的积分
     */
    private Long calculatedPoint;    
    /**
     * 测试数据
     */
    private java.util.Map<String, Object> testData;    
    /**
     * 计算详情
     */
    private String calculationDetail;    
    /**
     * 错误信息
     */
    private String errorMessage;}
