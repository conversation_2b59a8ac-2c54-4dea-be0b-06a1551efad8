﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值明细模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthDetail extends Extensible {    
    /**
     * 明细ID
     */
    private Long id;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 变动数量
     */
    private Long amount;    
    /**
     * 变动前成长值
     */
    private Long beforeGrowth;    
    /**
     * 变动后成长值
     */
    private Long afterGrowth;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 创建时间
     */
    private String createTime;}
