﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级规则
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRule extends Extensible {    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 计算方式：POINT(积分), GROWTH(成长值)
     */
    private String calculationType;    
    /**
     * 等级配置列表
     */
    private java.util.List<LevelConfig> levelConfigList;    
    /**
     * 特权配置列表
     */
    private java.util.List<PrivilegeConfig> privilegeConfigList;    
    /**
     * 倍率配置列表
     */
    private java.util.List<MultiplierConfig> multiplierConfigList;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;}
