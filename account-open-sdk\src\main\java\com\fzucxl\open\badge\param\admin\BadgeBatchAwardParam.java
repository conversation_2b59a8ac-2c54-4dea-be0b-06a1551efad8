﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 批量发放勋章参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeBatchAwardParam extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 用户ID列表
     */
    private java.util.List<Long> userIds;    
    /**
     * 发放来源
     */
    private String source;    
    /**
     * 发放原因
     */
    private String reason;    
    /**
     * 操作人ID
     */
    private Long operatorId;}
