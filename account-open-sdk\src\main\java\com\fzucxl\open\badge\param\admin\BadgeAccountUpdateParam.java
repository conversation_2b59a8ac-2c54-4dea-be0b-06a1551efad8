﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新勋章账户参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccountUpdateParam extends Extensible {    
    /**
     * 账户ID
     */
    private Long accountId;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 账户类型
     */
    private String accountType;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 操作人ID
     */
    private Long operatorId;}
