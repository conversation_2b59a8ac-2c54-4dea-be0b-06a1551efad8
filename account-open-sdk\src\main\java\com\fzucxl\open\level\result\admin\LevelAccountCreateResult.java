﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建等级账户结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelAccountCreateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 账户ID
     */
    private String accountId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 交易ID
     */
    private String transactionId;}
