﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新积分账户参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccountUpdateParam extends Extensible {    
    /**
     * 账户ID
     */
    private String accountId;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 账户状态
     */
    private String status;    
    /**
     * 备注
     */
    private String remark;}
