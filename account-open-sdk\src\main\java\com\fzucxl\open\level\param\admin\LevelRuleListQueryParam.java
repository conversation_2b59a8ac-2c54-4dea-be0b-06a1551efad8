﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级规则列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleListQueryParam extends Extensible {    
    /**
     * 规则名称（模糊查询）
     */
    private String ruleName;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 计算方式：POINT(积分), GROWTH(成长值)
     */
    private String calculationType;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 交易ID
     */
    private String transactionId;}
