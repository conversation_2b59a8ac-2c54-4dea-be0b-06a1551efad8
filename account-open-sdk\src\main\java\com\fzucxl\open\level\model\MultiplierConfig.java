﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 倍率配置
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MultiplierConfig extends Extensible {    
    /**
     * 等级
     */
    private Integer level;    
    /**
     * 倍率代码
     */
    private String multiplierCode;    
    /**
     * 倍率名称
     */
    private String multiplierName;    
    /**
     * 倍率类型
     */
    private String multiplierType;    
    /**
     * 倍率值
     */
    private Double multiplierValue;    
    /**
     * 倍率描述
     */
    private String description;}
