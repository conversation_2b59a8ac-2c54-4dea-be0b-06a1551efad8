package com.fzucxl.entity.point;

import com.fzucxl.open.base.TransactionStatus;
import com.fzucxl.open.base.point.PointTransactionType;
import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 积分交易记录实体
 * 
 * <AUTHOR>
 */
@MappedEntity("point_transaction")
@Data
public class PointTransaction {
    
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 交易积分
     */
    private Long point;

    /**
     * 变更前余额
     */
    private Long balanceBefore;
    /**
     * 变更后余额
     */
    private Long balanceAfter;

    /**
     * 可用积分变更前余额
     */
    private Long availableBefore;

    /**
     * 可用积分变更后余额
     */
    private Long availableAfter;

    /**
     * 冻结积分变更前余额
     */
    private Long frozenBefore = 0L;

    /**
     * 冻结积分变更后余额
     */
    private Long frozenAfter = 0L;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 交易类型
     */
    private PointTransactionType transactionType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 来源
     */
    private String source;
    /**
     * 描述
     */
    private String description;
    /**
     * 交易状态
     */
    private TransactionStatus transactionStatus = TransactionStatus.SUCCESS;
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 操作类型
     */
    private String operatorType;

    /**
     * 事务入参
     */
    private String inputData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    /**
     * 版本号
     */
    private Integer version = 0;
}