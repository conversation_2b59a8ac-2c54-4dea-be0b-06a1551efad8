﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 测试勋章规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRuleTestResult extends Extensible {    
    /**
     * 是否匹配
     */
    private Boolean matched;    
    /**
     * 匹配原因
     */
    private String reason;    
    /**
     * 测试结果详情
     */
    private String testResult;}
