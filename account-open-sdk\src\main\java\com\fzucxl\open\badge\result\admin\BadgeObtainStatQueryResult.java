﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章获得统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeObtainStatQueryResult extends Extensible {    
    /**
     * 获得率
     */
    private java.math.BigDecimal obtainRate;    
    /**
     * 总用户数
     */
    private Long totalUsers;    
    /**
     * 获得用户数
     */
    private Long obtainUsers;    
    /**
     * 获得统计明细
     */
    private java.util.List<BadgeObtainStatItem> obtainStatList;}
