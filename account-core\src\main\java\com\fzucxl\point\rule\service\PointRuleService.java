package com.fzucxl.point.rule.service;

import com.alibaba.qlexpress4.Express4Runner;
import com.alibaba.qlexpress4.QLOptions;
import com.fzucxl.common.exception.BusinessException;
import com.fzucxl.point.rule.dto.CreateRuleRequest;
import com.fzucxl.point.rule.dto.UpdateRuleRequest;
import com.fzucxl.point.rule.dto.RuleTestResult;
import com.fzucxl.point.rule.engine.RuleExecutionEngine;
import com.fzucxl.entity.point.PointRule;
import com.fzucxl.point.rule.repository.PointRuleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积分规则服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
@Transactional
public class PointRuleService {
    
    private static final Logger log = LoggerFactory.getLogger(PointRuleService.class);
    
    @Inject
    private PointRuleRepository ruleRepository;
    
    @Inject
    private RuleExecutionEngine executionEngine;
    
    @Inject
    private Express4Runner express4Runner;

    private static final QLOptions qlOptions = QLOptions.builder()
            .traceExpression(false)
            .precise(true)
            .shortCircuitDisable(false)
            .build();

    /**
     * 创建积分规则
     */
    public PointRule createRule(CreateRuleRequest request) {
        
        // 验证规则代码唯一性
        if (ruleRepository.existsByAccountCodeAndRuleCode(request.getAccountCode(), request.getRuleCode())) {
            throw new BusinessException("账户规则代码已存在");
        }
        
        // 构建QLExpress表达式
        String expression = buildExpression(request);
        
        // 验证表达式语法
        validateExpression(expression);
        
        // 创建规则
        PointRule rule = new PointRule();
        rule.setAccountCode(request.getAccountCode());
        rule.setRuleCode(request.getRuleCode());
        rule.setRuleName(request.getRuleName());
        rule.setConditionRule(expression);
        rule.setPriority(request.getPriority());
        rule.setDescription(request.getDescription());
        rule.setCreatedBy(request.getCreatorId());
        
        PointRule savedRule = ruleRepository.save(rule);
        
        log.info("创建积分规则成功: accountCode={}, ruleCode={}", request.getAccountCode(), request.getRuleCode());
        return savedRule;
    }
    
    /**
     * 更新积分规则
     */
    public PointRule updatePointRule(Long id, UpdateRuleRequest request) {
        
        PointRule rule = ruleRepository.findById(id)
            .orElseThrow(() -> new BusinessException("积分规则不存在"));
        
        // 构建QLExpress表达式
        String expression = buildUpdateExpression(request);
        
        // 验证表达式语法
        validateExpression(expression);
        
        // 更新规则
        rule.setRuleName(request.getRuleName());
        rule.setConditionRule(expression);
        rule.setPriority(request.getPriority());
        rule.setDescription(request.getDescription());
        rule.setUpdatedBy(request.getUpdaterId());
        
        PointRule savedRule = ruleRepository.save(rule);
        
        log.info("更新账户规则成功: accountCode={}, ruleCode={}", rule.getAccountCode(), rule.getRuleCode());
        return savedRule;
    }
    
    /**
     * 构建QLExpress表达式
     */
    private String buildExpression(CreateRuleRequest request) {
        StringBuilder expr = new StringBuilder();
        
        // 基础表达式
        expr.append("getAttribute(userId, '")
            .append(request.getAttributeCode())
            .append("')");
            
        // 添加比较条件
        expr.append(" ").append(request.getOperator()).append(" ")
            .append(request.getThreshold());
            
        // 如果有额外条件
        if (request.getAdditionalConditions() != null && !request.getAdditionalConditions().isEmpty()) {
            for (String condition : request.getAdditionalConditions()) {
                expr.append(" && ").append(condition);
            }
        }
        
        return expr.toString();
    }
    
    /**
     * 验证表达式语法
     */
    private void validateExpression(String expression) {
        try {
            Map<String, Object> testContext = new HashMap<>();
            testContext.put("userId", "test_user");

            express4Runner.execute(expression, testContext, qlOptions);
        } catch (Exception e) {
            throw new BusinessException("表达式语法验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建更新QLExpress表达式
     */
    private String buildUpdateExpression(UpdateRuleRequest request) {
        StringBuilder expr = new StringBuilder();
        
        // 基础表达式
        expr.append("getAttribute(userId, '")
            .append(request.getAttributeCode())
            .append("')");
            
        // 添加比较条件
        expr.append(" ").append(request.getOperator()).append(" ")
            .append(request.getThreshold());
            
        // 如果有额外条件
        if (request.getAdditionalConditions() != null && !request.getAdditionalConditions().isEmpty()) {
            for (String condition : request.getAdditionalConditions()) {
                expr.append(" && ").append(condition);
            }
        }
        
        return expr.toString();
    }
    
    /**
     * 测试规则
     */
    public RuleTestResult testRule(String accountCode,
                                          String ruleCode,
                                          String testUserId,
                                          Map<String, Object> testContext,
                                          Map<String, Object> attachment) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 添加testUserId到上下文
            testContext.put("userId", testUserId);
            boolean result = executionEngine.executeRule(accountCode, ruleCode, testContext, attachment);
            
            return RuleTestResult.builder()
                .success(true)
                .result(result)
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            return RuleTestResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }
    
    /**
     * 根据账户代码获取所有活跃规则
     */
    public List<PointRule> getActiveRuleByAccount(String accountCode) {
        return ruleRepository.findByAccountCodeAndStatus(accountCode, "ACTIVE");
    }
    
    /**
     * 启用规则
     */
    public void enableRule(Long ruleId) {
        PointRule rule = ruleRepository.findById(ruleId)
            .orElseThrow(() -> new BusinessException("规则不存在"));
        
        rule.setStatus("ACTIVE");
        ruleRepository.save(rule);
        
        log.info("启用规则成功: ruleId={}", ruleId);
    }
    
    /**
     * 禁用规则
     */
    public void disableRule(Long ruleId) {
        PointRule rule = ruleRepository.findById(ruleId)
            .orElseThrow(() -> new BusinessException("规则不存在"));
        
        rule.setStatus("INACTIVE");
        ruleRepository.save(rule);
        
        log.info("禁用规则成功: ruleId={}", ruleId);
    }
}