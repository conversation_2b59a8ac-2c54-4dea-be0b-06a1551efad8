﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 撤销积分参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointReverseParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 原交易ID
     */
    private String originalTransactionId;    
    /**
     * 撤销类型
     */
    private String reverseType;    
    /**
     * 撤销原因
     */
    private String reason;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
