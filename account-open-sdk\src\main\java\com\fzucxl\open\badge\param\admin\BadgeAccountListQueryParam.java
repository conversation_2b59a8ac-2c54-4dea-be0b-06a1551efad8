﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章账户列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccountListQueryParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户类型
     */
    private String accountType;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页大小
     */
    private Integer pageSize;}
