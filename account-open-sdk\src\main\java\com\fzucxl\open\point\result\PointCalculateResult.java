﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分计算结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointCalculateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 计算得出的积分数量
     */
    private Long calculatedPoint;    
    /**
     * 使用的规则代码
     */
    private String ruleCode;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 事件数据
     */
    private java.util.Map<String, Object> eventData;    
    /**
     * 计算详情
     */
    private String calculationDetail;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 计算时间
     */
    private String calculateTime;}
