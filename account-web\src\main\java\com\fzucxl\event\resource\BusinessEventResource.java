package com.fzucxl.event.resource;

import com.fzucxl.open.common.Result;
import com.fzucxl.event.dto.CreateBusinessEventRequest;
import com.fzucxl.event.dto.TriggerEventRequest;
import com.fzucxl.entity.event.BusinessEvent;
import com.fzucxl.entity.event.BusinessEventField;
import com.fzucxl.entity.event.BusinessEventInstance;
import com.fzucxl.event.model.BusinessEventContext;
import com.fzucxl.event.service.BusinessEventService;
import io.micronaut.http.annotation.*;
import io.micronaut.validation.Validated;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 业务事件控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Controller("/events")
@Validated
@Tag(name = "业务事件管理", description = "业务事件的创建、管理和触发")
public class BusinessEventResource {
    
    @Inject
    private BusinessEventService businessEventService;
    
    /**
     * 创建业务事件
     */
    @Post
    @Operation(summary = "创建业务事件", description = "创建新的业务事件定义")
    public Result<BusinessEvent> createEvent(@Valid @Body CreateBusinessEventRequest request) {
        try {
            // 创建事件
            BusinessEvent event = new BusinessEvent();
            event.setEventCode(request.getEventCode());
            event.setEventName(request.getEventName());
            event.setDescription(request.getDescription());
            event.setCategory(request.getCategory());
            
            BusinessEvent createdEvent = businessEventService.createEvent(event);
            
            // 创建事件字段
            if (request.getEventFields() != null) {
                for (var fieldDto : request.getEventFields()) {
                    BusinessEventField field = new BusinessEventField();
                    field.setEventId(createdEvent.getId());
                    field.setFieldCode(fieldDto.getFieldCode());
                    field.setFieldName(fieldDto.getFieldName());
                    field.setFieldType(fieldDto.getFieldType());
                    field.setDescription(fieldDto.getDescription());
                    field.setRequired(fieldDto.getRequired());
                    field.setDefaultValue(fieldDto.getDefaultValue());
                    field.setValidationRule(fieldDto.getValidationRule());
                    field.setSortOrder(fieldDto.getSortOrder());
                    
                    businessEventService.addEventField(field);
                }
            }
            
            return Result.success("事件创建成功", createdEvent);
        } catch (Exception e) {
            return Result.error("事件创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取事件列表
     */
    @Get
    @Operation(summary = "获取事件列表", description = "获取所有启用的业务事件")
    public Result<List<BusinessEvent>> getEvents() {
        try {
            List<BusinessEvent> events = businessEventService.getActiveEvents();
            return Result.success(events);
        } catch (Exception e) {
            return Result.error("获取事件列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据事件编码获取事件详情
     */
    @Get("/{eventCode}")
    @Operation(summary = "获取事件详情", description = "根据事件编码获取事件详细信息")
    public Result<BusinessEvent> getEventByCode(@PathVariable String eventCode) {
        try {
            Optional<BusinessEvent> event = businessEventService.getEventByCode(eventCode);
            if (event.isPresent()) {
                return Result.success(event.get());
            } else {
                return Result.error("事件不存在: " + eventCode);
            }
        } catch (Exception e) {
            return Result.error("获取事件详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取事件字段
     */
    @Get("/{eventCode}/fields")
    @Operation(summary = "获取事件字段", description = "获取指定事件的所有字段定义")
    public Result<List<BusinessEventField>> getEventFields(@PathVariable String eventCode) {
        try {
            Optional<BusinessEvent> event = businessEventService.getEventByCode(eventCode);
            if (event.isEmpty()) {
                return Result.error("事件不存在: " + eventCode);
            }
            
            List<BusinessEventField> fields = businessEventService.getEventFields(event.get().getId());
            return Result.success(fields);
        } catch (Exception e) {
            return Result.error("获取事件字段失败: " + e.getMessage());
        }
    }
    
    /**
     * 触发业务事件
     */
    @Post("/trigger")
    @Operation(summary = "触发业务事件", description = "触发指定的业务事件")
    public Result<BusinessEventInstance> triggerEvent(@Valid @Body TriggerEventRequest request) {
        try {
            BusinessEventInstance instance = businessEventService.triggerEvent(
                request.getEventCode(),
                request.getUserId(),
                request.getEventData(),
                request.getSource()
            );
            
            return Result.success("事件触发成功", instance);
        } catch (Exception e) {
            return Result.error("事件触发失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建事件上下文
     */
    @Post("/context")
    @Operation(summary = "构建事件上下文", description = "根据事件数据构建规则引擎上下文")
    public Result<Map<String, Object>> buildEventContext(@Valid @Body TriggerEventRequest request) {
        try {
            BusinessEventContext context = businessEventService.buildEventContext(
                request.getEventCode(),
                request.getUserId(),
                request.getEventData()
            );
            
            return Result.success("上下文构建成功", context.getFullContext());
        } catch (Exception e) {
            return Result.error("上下文构建失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户事件实例
     */
    @Get("/instances")
    @Operation(summary = "获取事件实例", description = "获取用户的事件实例列表")
    public Result<List<BusinessEventInstance>> getEventInstances(
            @QueryValue String eventCode,
            @QueryValue String userId,
            @QueryValue String status) {
        try {
            List<BusinessEventInstance> instances = businessEventService.getEventInstances(eventCode, userId, status);
            return Result.success(instances);
        } catch (Exception e) {
            return Result.error("获取事件实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新事件实例状态
     */
    @Put("/instances/{instanceId}/status")
    @Operation(summary = "更新实例状态", description = "更新事件实例的处理状态")
    public Result<String> updateInstanceStatus(
            @PathVariable Long instanceId,
            @QueryValue String status,
            @QueryValue String processResult,
            @QueryValue String errorMessage) {
        try {
            businessEventService.updateInstanceStatus(instanceId, status, processResult, errorMessage);
            return Result.success("状态更新成功");
        } catch (Exception e) {
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }
}