﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询交易记录结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointTransactionQueryResult extends Extensible {    
    /**
     * 总记录数
     */
    private Integer total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer pages;    
    /**
     * 交易记录列表
     */
    private java.util.List<PointTransaction> list;}
