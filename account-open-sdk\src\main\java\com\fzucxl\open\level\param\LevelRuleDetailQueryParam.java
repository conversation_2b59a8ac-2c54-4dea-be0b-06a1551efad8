﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级规则详情参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleDetailQueryParam extends Extensible {    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易ID
     */
    private String transactionId;}
