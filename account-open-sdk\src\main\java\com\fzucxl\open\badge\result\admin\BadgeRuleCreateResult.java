﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建勋章规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRuleCreateResult extends Extensible {    
    /**
     * 规则ID
     */
    private Long ruleId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 创建时间
     */
    private String createTime;}
