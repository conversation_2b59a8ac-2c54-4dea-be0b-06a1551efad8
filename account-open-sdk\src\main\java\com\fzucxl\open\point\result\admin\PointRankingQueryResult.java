﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分排行榜结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRankingQueryResult extends Extensible {    
    /**
     * 排行榜类型
     */
    private String rankingType;    
    /**
     * 总数量
     */
    private Integer totalCount;    
    /**
     * 更新时间
     */
    private String updateTime;    
    /**
     * 排行榜列表
     */
    private java.util.List<PointRankingItem> rankingList;}
