﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分统计结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointStatQueryResult extends Extensible {    
    /**
     * 总发放积分
     */
    private Long totalIssued;    
    /**
     * 总消费积分
     */
    private Long totalConsumed;    
    /**
     * 总冻结积分
     */
    private Long totalFrozen;    
    /**
     * 总过期积分
     */
    private Long totalExpired;    
    /**
     * 活跃用户数
     */
    private Integer activeUser;    
    /**
     * 统计详情
     */
    private java.util.List<PointStatDetail> statDetail;}
