-- 属性元数据表
CREATE TABLE IF NOT EXISTS `attribute_meta` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `attribute_code` varchar(100) NOT NULL COMMENT '属性编码',
    `attribute_name` varchar(200) NOT NULL COMMENT '属性名称',
    `description` text COMMENT '属性描述',
    `data_type` varchar(50) DEFAULT 'STRING' COMMENT '数据类型(STRING,LONG,DOUBLE,BOOLEAN)',
    `data_source` varchar(50) NOT NULL COMMENT '数据源',
    `target_table` varchar(100) NOT NULL COMMENT '目标表',
    `aggregate_function` varchar(20) NOT NULL DEFAULT 'COUNT' COMMENT '聚合函数',
    `aggregate_field` varchar(100) COMMENT '聚合字段',
    `filter_conditions` json COMMENT '过滤条件JSON',
    `time_range_config` json COMMENT '时间范围配置JSON',
    `cache_ttl` int(11) DEFAULT 300 COMMENT '缓存TTL(秒)',
    `default_value` decimal(10,2) DEFAULT 0.00 COMMENT '默认值',
    `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    `created_by` varchar(100) NOT NULL COMMENT '创建人',
    `updated_by` varchar(100) NOT NULL COMMENT '更新人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_attribute_code` (`attribute_code`),
    KEY `idx_data_source` (`data_source`),
    KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='属性元数据表';

-- 插入示例数据
INSERT INTO `attribute_meta` (
    `attribute_code`, `attribute_name`, `description`, `data_source`, `target_table`,
    `aggregate_function`, `aggregate_field`, `filter_conditions`, `time_range_config`,
    `cache_ttl`, `default_value`, `status`, `created_by`, `updated_by`
) VALUES (
        'purchase_count_1year', '一年内购买次数', '统计用户一年内的购买次数，排除退单',
        'order_db', 'orders', 'COUNT', '*',
        '{"status": {"operator": "!=", "value": "REFUNDED"}}',
        '{"type": "RELATIVE", "value": 365, "unit": "DAYS"}',
        300, 0.00, 'ACTIVE', 'system', 'system'
);

-- 业务事件表
CREATE TABLE IF NOT EXISTS business_event (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_code VARCHAR(100) NOT NULL UNIQUE COMMENT '事件编码',
    event_name VARCHAR(200) NOT NULL COMMENT '事件名称',
    description TEXT COMMENT '事件描述',
    category VARCHAR(50) COMMENT '事件分类',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
    version BIGINT NOT NULL COMMENT '版本号',
    created_by varchar(100) NOT NULL COMMENT '创建人',
    updated_by varchar(100) NOT NULL COMMENT '更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_event_code (event_code),
    INDEX idx_category (category),
    INDEX idx_status (status)
) COMMENT '业务事件定义表';

-- 业务事件字段表
CREATE TABLE IF NOT EXISTS business_event_field (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL COMMENT '事件ID',
    field_code VARCHAR(100) NOT NULL COMMENT '字段编码',
    field_name VARCHAR(200) NOT NULL COMMENT '字段名称',
    field_type VARCHAR(50) NOT NULL COMMENT '字段类型：STRING, INTEGER, LONG, DOUBLE, BOOLEAN, DATE, DATETIME, LIST, JSON',
    description TEXT COMMENT '字段描述',
    required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    default_value TEXT COMMENT '默认值',
    validation_rule TEXT COMMENT '验证规则JSON',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (event_id) REFERENCES business_event(id) ON DELETE CASCADE,
    UNIQUE KEY uk_event_field (event_id, field_code),
    INDEX idx_event_id (event_id),
    INDEX idx_field_code (field_code)
) COMMENT '业务事件字段定义表';

-- 业务事件实例表
CREATE TABLE IF NOT EXISTS business_event_instance (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL COMMENT '事件ID',
    event_code VARCHAR(100) NOT NULL COMMENT '事件编码',
    user_id bigint(20) NOT NULL COMMENT '用户ID',
    event_data JSON COMMENT '事件数据',
    source VARCHAR(100) COMMENT '事件来源',
    event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '事件发生时间',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '处理状态：PENDING-待处理，PROCESSING-处理中，PROCESSED-已处理，FAILED-失败',
    process_result TEXT COMMENT '处理结果',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_event_code (event_code),
    INDEX idx_user_id (user_id),
    INDEX idx_event_time (event_time),
    INDEX idx_status (status),
    INDEX idx_event_user (event_code, user_id)
) COMMENT '业务事件实例表';

-- 插入会员下单事件定义
INSERT INTO business_event (event_code, event_name, description, category, status, version, created_by, updated_by) VALUES
('member_order', '会员下单', '会员下单事件，包含完整订单信息和子订单数据', 'MEMBER', 'ACTIVE', 1, 'system', 'system');

-- 获取刚插入的事件ID
SET @member_order_event_id = LAST_INSERT_ID();

-- 插入会员下单事件的字段定义
INSERT INTO business_event_field (event_id, field_code, field_name, field_type, description, required, sort_order, status) VALUES
-- 主订单基础信息
(@member_order_event_id, 'order_id', '订单ID', 'STRING', '主订单唯一标识', TRUE, 1, 'ACTIVE'),
(@member_order_event_id, 'member_id', '会员ID', 'STRING', '下单会员的唯一标识', TRUE, 2, 'ACTIVE'),
(@member_order_event_id, 'member_level', '会员等级', 'STRING', '会员等级：BRONZE, SILVER, GOLD, PLATINUM, DIAMOND', FALSE, 3, 'ACTIVE'),
(@member_order_event_id, 'order_amount', '订单总金额', 'DOUBLE', '订单总金额（元）', TRUE, 4, 'ACTIVE'),
(@member_order_event_id, 'discount_amount', '折扣金额', 'DOUBLE', '订单折扣金额（元）', FALSE, 5, 'ACTIVE'),
(@member_order_event_id, 'actual_amount', '实付金额', 'DOUBLE', '订单实际支付金额（元）', TRUE, 6, 'ACTIVE'),
(@member_order_event_id, 'payment_method', '支付方式', 'STRING', '支付方式：ALIPAY, WECHAT, BANK_CARD, POINTS', TRUE, 7, 'ACTIVE'),
(@member_order_event_id, 'order_status', '订单状态', 'STRING', '订单状态：PENDING, PAID, SHIPPED, DELIVERED, CANCELLED', TRUE, 8, 'ACTIVE'),
(@member_order_event_id, 'order_time', '下单时间', 'DATETIME', '订单创建时间', TRUE, 9, 'ACTIVE'),
(@member_order_event_id, 'delivery_address', '收货地址', 'STRING', '订单收货地址', FALSE, 10, 'ACTIVE'),

-- 子订单列表（LIST类型）
(@member_order_event_id, 'orderItems', '子订单列表', 'LIST', '包含所有子订单的列表数据', TRUE, 11, 'ACTIVE'),

-- 子订单层级字段（使用orderItems.前缀）
(@member_order_event_id, 'orderItems.item_id', '商品ID', 'STRING', '子订单中的商品唯一标识', TRUE, 12, 'ACTIVE'),
(@member_order_event_id, 'orderItems.item_name', '商品名称', 'STRING', '子订单中的商品名称', TRUE, 13, 'ACTIVE'),
(@member_order_event_id, 'orderItems.category', '商品分类', 'STRING', '商品所属分类', FALSE, 14, 'ACTIVE'),
(@member_order_event_id, 'orderItems.brand', '商品品牌', 'STRING', '商品品牌', FALSE, 15, 'ACTIVE'),
(@member_order_event_id, 'orderItems.unit_price', '商品单价', 'DOUBLE', '商品单价（元）', TRUE, 16, 'ACTIVE'),
(@member_order_event_id, 'orderItems.quantity', '购买数量', 'INTEGER', '商品购买数量', TRUE, 17, 'ACTIVE'),
(@member_order_event_id, 'orderItems.subtotal', '子订单小计', 'DOUBLE', '子订单金额小计（元）', TRUE, 18, 'ACTIVE'),
(@member_order_event_id, 'orderItems.discount', '子订单折扣', 'DOUBLE', '子订单折扣金额（元）', FALSE, 19, 'ACTIVE'),
(@member_order_event_id, 'orderItems.payment', '子订单实付', 'DOUBLE', '子订单实际支付金额（元）', TRUE, 20, 'ACTIVE'),

-- 会员相关信息
(@member_order_event_id, 'member_point_used', '使用积分', 'INTEGER', '本次订单使用的积分数量', FALSE, 21, 'ACTIVE'),
(@member_order_event_id, 'member_point_earned', '获得积分', 'INTEGER', '本次订单预计获得的积分数量', FALSE, 22, 'ACTIVE'),
(@member_order_event_id, 'member_coupon_used', '使用优惠券', 'STRING', '本次订单使用的优惠券ID', FALSE, 23, 'ACTIVE'),
(@member_order_event_id, 'is_first_order', '是否首单', 'BOOLEAN', '是否为该会员的首次下单', FALSE, 24, 'ACTIVE'),

-- 营销活动相关
(@member_order_event_id, 'promotion_id', '促销活动ID', 'STRING', '参与的促销活动标识', FALSE, 25, 'ACTIVE'),
(@member_order_event_id, 'promotion_type', '促销类型', 'STRING', '促销活动类型：DISCOUNT, FULL_REDUCTION, GIFT', FALSE, 26, 'ACTIVE'),

-- 渠道信息
(@member_order_event_id, 'channel', '下单渠道', 'STRING', '下单渠道：APP, WEB, MINI_PROGRAM, H5', TRUE, 27, 'ACTIVE'),
(@member_order_event_id, 'device_type', '设备类型', 'STRING', '下单设备类型：IOS, ANDROID, PC, OTHER', FALSE, 28, 'ACTIVE'),

-- 地理位置信息
(@member_order_event_id, 'city', '下单城市', 'STRING', '会员下单时所在城市', FALSE, 29, 'ACTIVE'),
(@member_order_event_id, 'province', '下单省份', 'STRING', '会员下单时所在省份', FALSE, 30, 'ACTIVE');
