package com.fzucxl.entity.event;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.data.annotation.*;
import io.micronaut.serde.annotation.Serdeable;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 业务事件实例实体
 * 用于存储具体的事件数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedEntity("business_event_instance")
@Introspected
@Serdeable
@Data
public class BusinessEventInstance {
    
    @Id
    @GeneratedValue(GeneratedValue.Type.IDENTITY)
    private Long id;

    /**
     * 关联的业务事件ID
     */
    private Long eventId;
    
    /**
     * 事件编码
     */
    private String eventCode;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 事件数据（JSON格式存储动态字段值）
     */
    private String eventData;
    
    /**
     * 事件来源
     */
    private String source;
    
    /**
     * 事件状态：PENDING-待处理，PROCESSED-已处理，FAILED-处理失败
     */
    private String status = "PENDING";
    
    /**
     * 处理结果
     */
    private String processResult;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 事件发生时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 创建时间
     */
    @DateCreated
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @DateUpdated
    private LocalDateTime updatedAt;
    
    // Constructors
    public BusinessEventInstance() {}
    
    public BusinessEventInstance(String eventCode, Integer userId, String eventData) {
        this.eventCode = eventCode;
        this.userId = userId;
        this.eventData = eventData;
        this.eventTime = LocalDateTime.now();
    }
}