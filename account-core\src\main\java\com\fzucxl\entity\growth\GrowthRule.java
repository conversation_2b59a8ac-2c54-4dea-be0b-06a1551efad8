package com.fzucxl.entity.growth;

import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.event.PrePersist;
import io.micronaut.data.annotation.event.PreUpdate;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@MappedEntity("growth_rule")
@Data
public class GrowthRule {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;
    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 基础成长值
     */
    private Long baseGrowth;

    /**
     * 倍率
     */
    private BigDecimal multiplier;

    /**
     * 每日限制
     */
    private Long dailyLimit;

    /**
     * 每月限制
     */
    private Long monthlyLimit;

    /**
     * 条件规则表达式
     */
    private String conditionRule;

    /**
     * 动作规则表达式
     */
    private String actionRule;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态：ACTIVE-激活，INACTIVE-停用
     */
    private String status;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        if (this.status == null) {
            this.status = "ACTIVE";
        }
        if (this.multiplier == null) {
            this.multiplier = BigDecimal.ONE;
        }
        if (this.priority == null) {
            this.priority = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}
