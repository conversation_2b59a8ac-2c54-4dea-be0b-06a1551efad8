﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分发放统计详情模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointIssueStatDetail extends Extensible {    
    /**
     * 统计日期
     */
    private String statDate;    
    /**
     * 积分来源
     */
    private String source;    
    /**
     * 来源名称
     */
    private String sourceName;    
    /**
     * 发放积分
     */
    private Long totalIssued;    
    /**
     * 用户数
     */
    private Integer totalUser;    
    /**
     * 平均发放积分
     */
    private Double avgIssued;}
