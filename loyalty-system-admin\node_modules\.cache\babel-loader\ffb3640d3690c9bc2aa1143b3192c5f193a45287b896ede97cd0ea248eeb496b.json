{"ast": null, "code": "import isNumber from './is-number';\n/**\n * 判断值是否为奇数\n * @return 是否为奇数\n */\nexport default function isOdd(num) {\n  return isNumber(num) && num % 2 !== 0;\n}", "map": {"version": 3, "names": ["isNumber", "isOdd", "num"], "sources": ["D:\\code\\private\\fast-account\\loyalty-system-admin\\node_modules\\@antv\\util\\src\\lodash\\is-odd.ts"], "sourcesContent": ["import isNumber from './is-number';\n\n/**\n * 判断值是否为奇数\n * @return 是否为奇数\n */\nexport default function isOdd(num: number): boolean {\n  return isNumber(num) && num % 2 !== 0;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAElC;;;;AAIA,eAAc,SAAUC,KAAKA,CAACC,GAAW;EACvC,OAAOF,QAAQ,CAACE,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,KAAK,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}