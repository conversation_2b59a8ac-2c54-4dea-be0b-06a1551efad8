package com.fzucxl.point.rule.resource;

import com.fzucxl.open.common.Result;
import com.fzucxl.point.rule.dto.CreateRuleRequest;
import com.fzucxl.point.rule.dto.RuleTestResult;
import com.fzucxl.point.rule.dto.UpdateRuleRequest;
import com.fzucxl.entity.point.PointRule;
import com.fzucxl.point.rule.repository.PointRuleRepository;
import com.fzucxl.point.rule.service.PointRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.inject.Inject;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import io.micronaut.http.annotation.*;
import io.micronaut.validation.Validated;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 积分规则管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Controller("/point/rule")
@Validated
public class PointRuleResource {
    
    private static final Logger log = LoggerFactory.getLogger(PointRuleResource.class);
    
    @Inject
    private PointRuleService configService;
    
    @Inject
    private PointRuleRepository ruleRepository;
    
    /**
     * 创建积分规则
     */
    @Post
    public Result<PointRule> createRule(@Body @Valid CreateRuleRequest request) {
        try {
            PointRule result = configService.createRule(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建积分规则失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新积分规则
     */
    @Put("/{id}")
    public Result<PointRule> updateRule(
            @PathVariable Long id,
            @Body @Valid UpdateRuleRequest request) {
        try {
            PointRule result = configService.updatePointRule(id, request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新积分规则失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取积分规则详情
     */
    @Get("/{id}")
    public Result<PointRule> getRule(@PathVariable Long id) {
        try {
            PointRule result = ruleRepository.findById(id).orElse(null);
            if (result == null) {
                return Result.error("积分规则不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取积分规则失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询积分规则
     */
    @Get
    public Result<Page<PointRule>> listRule(
            @QueryValue String accountCode,
            @QueryValue String status,
            Pageable pageable) {
        try {
            Page<PointRule> result;
            if (accountCode != null && status != null) {
                result = ruleRepository.findByAccountCodeAndStatus(accountCode, status, pageable);
            } else if (accountCode != null) {
                result = ruleRepository.findByAccountCode(accountCode, pageable);
            } else if (status != null) {
                result = ruleRepository.findByStatus(status, pageable);
            } else {
                result = ruleRepository.findAll(pageable);
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询积分规则失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据账户代码获取活跃规则
     */
    @Get("/account/{accountCode}")
    public Result<List<PointRule>> getRuleByAccount(@PathVariable String accountCode) {
        try {
            List<PointRule> result = configService.getActiveRuleByAccount(accountCode);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取积分规则失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试积分规则
     */
    @Post("/test")
    public Result<RuleTestResult> testRule(@Body Map<String, Object> testRequest) {
        try {
            String accountCode = (String) testRequest.get("accountCode");
            String ruleCode = (String) testRequest.get("ruleCode");
            String testUserId = (String) testRequest.get("testUserId");
            Map<String, Object> testContext = (Map<String, Object>) testRequest.get("testContext");
            Map<String, Object> attachment = (Map<String, Object>) testRequest.get("attachment");
            RuleTestResult result = configService.testRule(accountCode, ruleCode, testUserId, testContext, attachment);
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试积分规则失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 启用规则
     */
    @Post("/{id}/enable")
    public Result<Void> enableRule(@PathVariable Long id) {
        try {
            configService.enableRule(id);
            return Result.success();
        } catch (Exception e) {
            log.error("启用规则失败", e);
            return Result.error("启用失败: " + e.getMessage());
        }
    }
    
    /**
     * 禁用规则
     */
    @Post("/{id}/disable")
    public Result<Void> disableRule(@PathVariable Long id) {
        try {
            configService.disableRule(id);
            return Result.success();
        } catch (Exception e) {
            log.error("禁用规则失败", e);
            return Result.error("禁用失败: " + e.getMessage());
        }
    }
}