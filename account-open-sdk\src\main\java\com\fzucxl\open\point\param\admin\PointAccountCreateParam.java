﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建积分账户参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccountCreateParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 初始积分
     */
    private Long initialPoint;    
    /**
     * 账户状态
     */
    private String status;    
    /**
     * 备注
     */
    private String remark;}
