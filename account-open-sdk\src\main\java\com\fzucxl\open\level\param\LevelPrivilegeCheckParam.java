﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 检查等级特权参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelPrivilegeCheckParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 特权代码
     */
    private String privilegeCode;    
    /**
     * 检查数据
     */
    private java.util.Map<String, Object> checkData;    
    /**
     * 交易ID
     */
    private String transactionId;}
