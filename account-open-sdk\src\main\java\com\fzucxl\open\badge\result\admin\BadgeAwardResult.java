﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 手动发放勋章结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAwardResult extends Extensible {    
    /**
     * 用户勋章ID
     */
    private Long userBadgeId;    
    /**
     * 勋章代码
     */
    private String badgeCode;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 获得时间
     */
    private String obtainTime;}
