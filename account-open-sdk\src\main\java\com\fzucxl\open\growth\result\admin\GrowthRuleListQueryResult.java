﻿package com.fzucxl.open.growth.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值规则列表结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleListQueryResult extends Extensible {    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;    
    /**
     * 规则列表
     */
    private java.util.List<GrowthRule> list;}
