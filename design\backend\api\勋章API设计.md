# 勋章API设计文档

## 1. 概述

### 1.1 服务简介
勋章服务提供用户勋章的配置管理、成就检测、发放回收和展示管理功能，是账户体系中用户荣誉激励的核心模块。

### 1.2 核心功能
- 勋章配置管理
- 成就条件检测
- 勋章发放和回收
- 勋章展示管理
- 勋章进度跟踪

### 1.3 接口汇总

#### 1.3.1 用户端API接口汇总

| 功能模块       | 接口名称     | 请求方式 | 接口路径 | 接口描述                           |
|------------|----------| -------- | -------- |--------------------------------|
| **用户勋章管理** | 查询用户勋章列表 | GET | `/api/v1/badge/user/list` | 查询指定用户的勋章列表，支持多维度筛选        |
|            | 查询勋章详情   | GET | `/api/v1/badge/detail/{userBadgeId}` | 查询用户勋章的详细信息，包括获得记录等        |
|            | 更新勋章展示设置 | PUT | `/api/v1/badge/display` | 更新勋章的展示状态和排序          |
| **勋章进度**   | 查询勋章进度   | GET | `/api/v1/badge/progress` | 查询用户的勋章获得进度                    |
| **勋章配置**   | 获取勋章账户列表 | GET | `/api/v1/badge/account/list` | 获取勋章账户列表                    |
| **勋章事务管理** | 勋章事务查询 | GET | `/api/v1/badge/transaction` | 查询勋章事务记录，支持按多种条件筛选和分页查询 |
|            | 勋章事务详情查询 | GET | `/api/v1/badge/transaction/{transactionId}` | 根据事务ID查询勋章事务的详细信息，包含执行日志和规则信息 |

#### 1.3.2 Web管理端API接口汇总

| 功能模块 | 接口名称 | 请求方式 | 接口路径 | 接口描述 |
| -------- | -------- | -------- | -------- | -------- |
| **勋章账户管理** | 创建勋章账户 | POST | `/admin/api/v1/badge/account` | 创建新的勋章账户 |
| | 查询勋章账户列表 | GET | `/admin/api/v1/badge/account/list` | 查询勋章账户列表 |
| | 查询勋章账户详情 | GET | `/admin/api/v1/badge/account/{accountCode}` | 查询勋章账户详情 |
| | 更新勋章账户 | PUT | `/admin/api/v1/badge/account/{accountCode}` | 更新勋章账户信息 |
| | 删除勋章账户 | DELETE | `/admin/api/v1/badge/account/{accountCode}` | 删除勋章账户 |
| **勋章发放管理** | 手动发放勋章 | POST | `/admin/api/v1/badge/award` | 手动发放勋章给用户 |
| | 撤销用户勋章 | POST | `/admin/api/v1/badge/revoke` | 撤销用户已获得的勋章 |
| | 批量发放勋章 | POST | `/admin/api/v1/badge/batch/award` | 批量发放勋章给多个用户 |
| **勋章记录管理** | 查询勋章记录 | GET | `/admin/api/v1/badge/records` | 查询勋章操作记录 |
| | 查询用户勋章记录 | GET | `/admin/api/v1/badge/user/{userId}/records` | 查询指定用户的勋章记录 |
| **勋章统计分析** | 勋章发放统计 | GET | `/admin/api/v1/badge/stat/award` | 查询勋章发放统计数据 |
| | 勋章获得统计 | GET | `/admin/api/v1/badge/stat/obtain` | 查询勋章获得统计数据 |
| | 勋章进度统计 | GET | `/admin/api/v1/badge/stat/progress` | 查询勋章进度统计数据 |

## 2. 用户端接口详细设计

### 2.1 查询用户勋章列表

**基本信息**
- **接口路径**：`GET /api/v1/badge/user/list`
- **接口描述**：查询指定用户的勋章列表，支持多维度筛选
- **权限要求**：用户权限
- **限流策略**：100次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Query | 是 | 用户唯一标识 | 123456 | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码 | "MALL_BADGE" | 长度1-32 |
| status | String | Query | 否 | 勋章状态 | "VALID" | VALID/EXPIRED/REVOKED |
| isDisplayed | Boolean | Query | 否 | 是否展示 | true | true/false |
| badgeType | String | Query | 否 | 勋章类型 | "ACHIEVEMENT" | ACHIEVEMENT/MILESTONE/ACTIVITY/SPECIAL |
| badgeCategory | String | Query | 否 | 勋章分类 | "PURCHASE" | PURCHASE/SOCIAL/GROWTH/ACTIVITY/SPECIAL |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Long | 总记录数 | 10 |
| data.list | Array | 勋章列表 | - |
| data.list[].id | Long | 用户勋章ID | 1 |
| data.list[].badgeId | Long | 勋章ID | 100 |
| data.list[].accountCode | String | 账户代码 | "MALL_BADGE" |
| data.list[].accountName | String | 账户名称 | "商城勋章" |
| data.list[].obtainTime | String | 获得时间 | "2024-01-15 10:30:00" |
| data.list[].expireTime | String | 过期时间 | null |
| data.list[].source | String | 获得来源 | "PURCHASE" |
| data.list[].status | String | 勋章状态 | "VALID" |
| data.list[].obtainCount | Integer | 获得次数 | 1 |
| data.list[].isDisplayed | Boolean | 是否展示 | true |
| data.list[].displayOrder | Integer | 展示顺序 | 1 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

### 2.2 查询勋章详情

**基本信息**
- **接口路径**：`GET /api/v1/badge/detail/{userBadgeId}`
- **接口描述**：查询用户勋章的详细信息，包括获得记录等
- **权限要求**：用户权限
- **限流策略**：200次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userBadgeId | Long | Path | 是 | 用户勋章ID | 1 | 长度1-64 |
| userId | Long | Query | 是 | 用户唯一标识 | 123456 | 长度1-64 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.id | Long | 用户勋章ID | 1 |
| data.badgeId | Long | 勋章ID | 100 |
| data.accountCode | String | 账户代码 | "MALL_BADGE" |
| data.accountName | String | 账户名称 | "商城勋章" |
| data.obtainTime | String | 获得时间 | "2024-01-15 10:30:00" |
| data.source | String | 获得来源 | "PURCHASE" |
| data.businessId | String | 业务ID | "order_001" |
| data.status | String | 勋章状态 | "VALID" |
| data.obtainCount | Integer | 获得次数 | 1 |
| data.isDisplayed | Boolean | 是否展示 | true |
| data.displayOrder | Integer | 展示顺序 | 1 |
| data.extraData | String | 额外数据 | "{\"orderAmount\": 299.00}" |
| data.records | Array | 操作记录 | - |
| data.records[].operationType | String | 操作类型 | "AWARD" |
| data.records[].operationTime | String | 操作时间 | "2024-01-15 10:30:00" |
| data.records[].source | String | 操作来源 | "PURCHASE" |
| data.records[].reason | String | 操作原因 | "系统自动发放" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章不存在 | 指定的用户勋章不存在 |
| 500 | 系统错误 | 服务器内部错误 |

### 2.3 更新勋章展示设置

**基本信息**
- **接口路径**：`PUT /api/v1/badge/display`
- **接口描述**：更新勋章的展示状态和排序
- **权限要求**：用户权限
- **限流策略**：50次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Body | 是 | 用户唯一标识 | 123456 | 长度1-64 |
| userBadgeId | Long | Body | 是 | 用户勋章ID | 1 | 长度1-64 |
| isDisplayed | Boolean | Body | 是 | 是否展示 | true | true/false |
| displayOrder | Integer | Body | 否 | 展示顺序 | 1 | 范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "更新成功" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章不存在 | 指定的用户勋章不存在 |
| 500 | 系统错误 | 服务器内部错误 |

### 2.4 查询勋章进度

**基本信息**
- **接口路径**：`GET /api/v1/badge/progress`
- **接口描述**：查询用户的勋章获得进度
- **权限要求**：用户权限
- **限流策略**：100次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Query | 是 | 用户唯一标识 | 123456 | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码 | "MALL_BADGE" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Array | 进度列表 | - |
| data[].id | Long | 进度ID | 1 |
| data[].userId | Long | 用户ID | 123456 |
| data[].badgeId | Long | 勋章ID | 101 |
| data[].accountCode | String | 账户代码 | "MALL_BADGE" |
| data[].accountName | String | 账户名称 | "商城勋章" |
| data[].currentProgress | Long | 当前进度 | 8 |
| data[].targetProgress | Long | 目标进度 | 10 |
| data[].progressPercent | Double | 进度百分比 | 80.0 |
| data[].progressData | String | 进度数据 | "{\"purchaseCount\": 8, \"targetCount\": 10}" |
| data[].startTime | String | 开始时间 | "2024-01-01 00:00:00" |
| data[].lastUpdateTime | String | 最后更新时间 | "2024-01-15 10:30:00" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

### 2.5 获取勋章账户列表

**基本信息**
- **接口路径**：`GET /api/v1/badge/account/list`
- **接口描述**：查询勋章账户列表
- **权限要求**：管理员权限
- **限流策略**：100次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32，模糊匹配 |
| accountName | String | Query | 否 | 账户名称 | "忠诚勋章" | 长度1-64，模糊匹配 |
| badgeType | String | Query | 否 | 勋章类型 | "ACHIEVEMENT" | ACHIEVEMENT/MILESTONE/ACTIVITY/SPECIAL |
| badgeCategory | String | Query | 否 | 勋章分类 | "LOYALTY" | PURCHASE/SOCIAL/GROWTH/ACTIVITY/SPECIAL |
| brandCode | String | Query | 否 | 品牌代码 | "BRAND_001" | 长度1-32 |
| status | String | Query | 否 | 状态 | "ACTIVE" | ACTIVE/INACTIVE |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页大小 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Long | 总记录数 | 50 |
| data.list | Array | 账户列表 | - |
| data.list[].id | Long | 勋章账户ID | 1 |
| data.list[].accountCode | String | 账户代码 | "LOYALTY_BADGE" |
| data.list[].accountName | String | 账户名称 | "忠诚勋章" |
| data.list[].accountType | String | 账户类型 | "SYSTEM" |
| data.list[].badgeType | String | 勋章类型 | "ACHIEVEMENT" |
| data.list[].badgeCategory | String | 勋章分类 | "LOYALTY" |
| data.list[].brandCode | String | 品牌代码 | "BRAND_001" |
| data.list[].status | String | 状态 | "ACTIVE" |
| data.list[].createTime | String | 创建时间 | "2024-01-01 00:00:00" |
| data.list[].updateTime | String | 更新时间 | "2024-01-15 10:30:00" |
| data.list[].statistics | Object | 统计信息 | - |
| data.list[].statistics.totalUsers | Long | 用户总数 | 1000 |
| data.list[].statistics.activeUsers | Long | 活跃用户数 | 800 |
| data.list[].statistics.totalAwards | Long | 总发放数 | 1200 |
| data.list[].statistics.todayAwards | Long | 今日发放数 | 50 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

### 2.6 勋章事务查询

**基本信息**
- **接口路径**：`GET /api/v1/badge/transaction`
- **接口描述**：查询勋章事务记录，支持按多种条件筛选和分页查询
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Query | 否 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码 | "MALL_APP" | 长度1-32 |
| transactionId | String | Query | 否 | 事务ID | "BT20240115001" | 长度1-64 |
| businessId | String | Query | 否 | 业务单号 | "biz_20240115001" | 长度1-64 |
| transactionType | String | Query | 否 | 事务类型 | "BADGE_AWARD" | 见事务类型枚举 |
| status | String | Query | 否 | 事务状态 | "SUCCESS" | PENDING/PROCESSING/SUCCESS/FAILED/CANCELLED |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01 00:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| total | Integer | 总记录数 | 150 |
| pageNum | Integer | 当前页码 | 1 |
| pageSize | Integer | 每页记录数 | 20 |
| pages | Integer | 总页数 | 8 |
| list | Array | 事务记录列表 | - |
| list[].transactionId | String | 事务ID | "BT20240115001" |
| list[].userId | String | 用户ID | "user_123456" |
| list[].accountCode | String | 账户代码 | "MALL_APP" |
| list[].badgeId | Long | 勋章ID | 1001 |
| list[].badgeName | String | 勋章名称 | "购物达人" |
| list[].businessId | String | 业务单号 | "biz_20240115001" |
| list[].businessType | String | 业务类型 | "PURCHASE" |
| list[].transactionType | String | 事务类型 | "BADGE_AWARD" |
| list[].status | String | 事务状态 | "SUCCESS" |
| list[].description | String | 事务描述 | "购买商品获得勋章" |
| list[].source | String | 勋章来源 | "SYSTEM" |
| list[].processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| list[].errorMessage | String | 错误信息 | null |
| list[].retryCount | Integer | 重试次数 | 0 |
| list[].createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| list[].updateTime | String | 更新时间 | "2024-01-15 10:00:01" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 150,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 8,
    "list": [
      {
        "transactionId": "BT20240115001",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "badgeId": 1001,
        "badgeName": "购物达人",
        "businessId": "biz_20240115001",
        "businessType": "PURCHASE",
        "transactionType": "BADGE_AWARD",
        "status": "SUCCESS",
        "description": "购买商品获得勋章",
        "source": "SYSTEM",
        "processedTime": "2024-01-15 10:00:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 10:00:00",
        "updateTime": "2024-01-15 10:00:01"
      },
      {
        "transactionId": "BT20240115002",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "badgeId": 1002,
        "badgeName": "活跃用户",
        "businessId": "biz_20240115002",
        "businessType": "ACTIVITY",
        "transactionType": "BADGE_AWARD",
        "status": "SUCCESS",
        "description": "连续签到获得勋章",
        "source": "SYSTEM",
        "processedTime": "2024-01-15 14:30:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 14:30:00",
        "updateTime": "2024-01-15 14:30:01"
      }
    ]
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**事务类型枚举**

| 类型 | 描述 |
| ---- | ---- |
| BADGE_AWARD | 勋章发放 |
| BADGE_REVOKE | 勋章撤销 |
| BADGE_EXPIRE | 勋章过期 |
| BADGE_DISPLAY_UPDATE | 勋章展示更新 |
| BADGE_BATCH_AWARD | 勋章批量发放 |
| BADGE_MANUAL_ADJUST | 勋章手动调整 |

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查参数格式和取值范围 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数 |

### 2.7 勋章事务详情查询

**基本信息**
- **接口路径**：`GET /api/v1/badge/transaction/{transactionId}`
- **接口描述**：根据事务ID查询勋章事务的详细信息
- **访问权限**：需要API认证
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| transactionId | String | Path | 是 | 事务ID | "BT20240115001" | 长度1-64 |
| includeDetail | Boolean | Query | 否 | 是否包含详细信息 | true | 默认false |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| transactionId | String | 事务ID | "BT20240115001" |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "MALL_APP" |
| badgeId | Long | 勋章ID | 1001 |
| badgeName | String | 勋章名称 | "购物达人" |
| businessId | String | 业务单号 | "biz_20240115001" |
| businessType | String | 业务类型 | "PURCHASE" |
| transactionType | String | 事务类型 | "BADGE_AWARD" |
| status | String | 事务状态 | "SUCCESS" |
| description | String | 事务描述 | "购买商品获得勋章" |
| source | String | 勋章来源 | "SYSTEM" |
| processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| errorMessage | String | 错误信息 | null |
| retryCount | Integer | 重试次数 | 0 |
| maxRetryCount | Integer | 最大重试次数 | 3 |
| operatorId | String | 操作人ID | "system" |
| extraData | String | 扩展数据 | "{\"remark\":\"系统自动处理\"}" |
| createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| updateTime | String | 更新时间 | "2024-01-15 10:00:01" |
| version | Integer | 版本号 | 1 |
| detail | Object | 详细信息（当includeDetail=true时返回） | - |
| detail.ruleInfo | Object | 规则信息 | - |
| detail.ruleInfo.ruleId | Long | 规则ID | 1001 |
| detail.ruleInfo.ruleName | String | 规则名称 | "购物达人勋章规则" |
| detail.ruleInfo.ruleExpression | String | 规则表达式 | "user.orderCount >= 10 && user.totalAmount >= 1000" |
| detail.badgeInfo | Object | 勋章信息 | - |
| detail.badgeInfo.badgeType | String | 勋章类型 | "ACHIEVEMENT" |
| detail.badgeInfo.badgeCategory | String | 勋章分类 | "PURCHASE" |
| detail.badgeInfo.badgeLevel | String | 勋章等级 | "GOLD" |
| detail.executionLog | Array | 执行日志 | - |
| detail.executionLog[].step | String | 执行步骤 | "RULE_EVALUATION" |
| detail.executionLog[].timestamp | String | 时间戳 | "2024-01-15 10:00:00.123" |
| detail.executionLog[].message | String | 日志信息 | "规则评估完成，满足勋章获得条件" |
| detail.executionLog[].data | Object | 相关数据 | {} |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "transactionId": "BT20240115001",
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "badgeId": 1001,
    "badgeName": "购物达人",
    "businessId": "biz_20240115001",
    "businessType": "PURCHASE",
    "transactionType": "BADGE_AWARD",
    "status": "SUCCESS",
    "description": "购买商品获得勋章",
    "source": "SYSTEM",
    "processedTime": "2024-01-15 10:00:01",
    "errorMessage": null,
    "retryCount": 0,
    "maxRetryCount": 3,
    "operatorId": "system",
    "extraData": "{\"remark\":\"系统自动处理\"}",
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:01",
    "version": 1,
    "detail": {
      "ruleInfo": {
        "ruleId": 1001,
        "ruleName": "购物达人勋章规则",
        "ruleExpression": "user.orderCount >= 10 && user.totalAmount >= 1000"
      },
      "badgeInfo": {
        "badgeType": "ACHIEVEMENT",
        "badgeCategory": "PURCHASE",
        "badgeLevel": "GOLD"
      },
      "executionLog": [
        {
          "step": "TRANSACTION_CREATE",
          "timestamp": "2024-01-15 10:00:00.001",
          "message": "创建勋章事务",
          "data": {}
        },
        {
          "step": "RULE_EVALUATION",
          "timestamp": "2024-01-15 10:00:00.123",
          "message": "规则评估完成，满足勋章获得条件",
          "data": {
            "ruleId": 1001,
            "userOrderCount": 12,
            "userTotalAmount": 1500
          }
        },
        {
          "step": "BADGE_AWARD",
          "timestamp": "2024-01-15 10:00:00.456",
          "message": "发放勋章给用户",
          "data": {
            "badgeId": 1001,
            "badgeName": "购物达人"
          }
        },
        {
          "step": "TRANSACTION_COMPLETE",
          "timestamp": "2024-01-15 10:00:01.000",
          "message": "事务处理完成",
          "data": {}
        }
      ]
    }
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查事务ID格式 |
| 2201 | 事务不存在 | 确认事务ID是否正确 |
| 2202 | 事务已过期 | 事务记录已超过保存期限 |

## 3. 管理端接口详细设计

### 3.1 勋章账户管理

#### 3.1.1 创建勋章账户

**基本信息**
- **接口路径**：`POST /admin/api/v1/badge/account`
- **接口描述**：创建新的勋章账户
- **权限要求**：管理员权限
- **限流策略**：10次/分钟
- **幂等性**：否

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Body | 是 | 账户代码 | "LOYALTY_BADGE" | 长度1-32，唯一 |
| accountName | String | Body | 是 | 账户名称 | "忠诚勋章" | 长度1-64 |
| accountType | String | Body | 是 | 账户类型 | "SYSTEM" | SYSTEM/BUSINESS/PERSONAL |
| badgeType | String | Body | 是 | 勋章类型 | "ACHIEVEMENT" | ACHIEVEMENT/MILESTONE/ACTIVITY/SPECIAL |
| badgeCategory | String | Body | 是 | 勋章分类 | "LOYALTY" | PURCHASE/SOCIAL/GROWTH/ACTIVITY/SPECIAL |
| brandCode | String | Body | 是 | 品牌代码 | "BRAND_001" | 长度1-32 |
| description | String | Body | 否 | 勋章描述 | "忠诚用户专属勋章" | 长度1-200 |
| gainCondition | Object | Body | 是 | 获得条件 | - | JSON对象 |
| gainCondition.type | String | Body | 是 | 条件类型 | "CONSECUTIVE_DAYS" | - |
| gainCondition.targetValue | Number | Body | 是 | 目标值 | 30 | - |
| gainCondition.actionType | String | Body | 否 | 动作类型 | "PURCHASE" | - |
| gainCondition.eventTypes | Array | Body | 是 | 事件类型列表 | ["ORDER_COMPLETED"] | - |
| rewardConfig | Object | Body | 否 | 奖励配置 | - | JSON对象 |
| rewardConfig.pointReward | Long | Body | 否 | 积分奖励 | 500 | - |
| rewardConfig.itemRewards | Array | Body | 否 | 物品奖励 | ["COUPON_001"] | - |
| basicConfig | Object | Body | 否 | 基础配置 | - | JSON对象 |
| basicConfig.maxObtainCount | Integer | Body | 否 | 最大获得次数 | 1 | - |
| basicConfig.validDays | Integer | Body | 否 | 有效天数 | 365 | - |
| basicConfig.autoAward | Boolean | Body | 否 | 自动发放 | true | - |
| riskControlConfig | Object | Body | 否 | 风控配置 | - | JSON对象 |
| riskControlConfig.dailyLimit | Integer | Body | 否 | 每日限制 | 100 | - |
| riskControlConfig.monthlyLimit | Integer | Body | 否 | 每月限制 | 1000 | - |
| riskControlConfig.blacklistCheck | Boolean | Body | 否 | 黑名单检查 | true | - |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "创建成功" |
| data | Object | 响应数据 | - |
| data.id | Long | 勋章账户ID | 1 |
| data.accountCode | String | 账户代码 | "LOYALTY_BADGE" |
| data.accountName | String | 账户名称 | "忠诚勋章" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 409 | 账户代码已存在 | 指定的账户代码已被使用 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.1.2 查询勋章账户列表

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/account/list`
- **接口描述**：查询勋章账户列表
- **权限要求**：管理员权限
- **限流策略**：100次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32，模糊匹配 |
| accountName | String | Query | 否 | 账户名称 | "忠诚勋章" | 长度1-64，模糊匹配 |
| badgeType | String | Query | 否 | 勋章类型 | "ACHIEVEMENT" | ACHIEVEMENT/MILESTONE/ACTIVITY/SPECIAL |
| badgeCategory | String | Query | 否 | 勋章分类 | "LOYALTY" | PURCHASE/SOCIAL/GROWTH/ACTIVITY/SPECIAL |
| brandCode | String | Query | 否 | 品牌代码 | "BRAND_001" | 长度1-32 |
| status | String | Query | 否 | 状态 | "ACTIVE" | ACTIVE/INACTIVE |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页大小 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Long | 总记录数 | 50 |
| data.list | Array | 账户列表 | - |
| data.list[].id | Long | 勋章账户ID | 1 |
| data.list[].accountCode | String | 账户代码 | "LOYALTY_BADGE" |
| data.list[].accountName | String | 账户名称 | "忠诚勋章" |
| data.list[].accountType | String | 账户类型 | "SYSTEM" |
| data.list[].badgeType | String | 勋章类型 | "ACHIEVEMENT" |
| data.list[].badgeCategory | String | 勋章分类 | "LOYALTY" |
| data.list[].brandCode | String | 品牌代码 | "BRAND_001" |
| data.list[].status | String | 状态 | "ACTIVE" |
| data.list[].createTime | String | 创建时间 | "2024-01-01 00:00:00" |
| data.list[].updateTime | String | 更新时间 | "2024-01-15 10:30:00" |
| data.list[].statistics | Object | 统计信息 | - |
| data.list[].statistics.totalUsers | Long | 用户总数 | 1000 |
| data.list[].statistics.activeUsers | Long | 活跃用户数 | 800 |
| data.list[].statistics.totalAwards | Long | 总发放数 | 1200 |
| data.list[].statistics.todayAwards | Long | 今日发放数 | 50 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.1.3 查询勋章账户详情

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/account/{accountCode}`
- **接口描述**：查询勋章账户详情
- **权限要求**：管理员权限
- **限流策略**：200次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Path | 是 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| statistics | Boolean | Query | 否 | 是否包含统计数据 | true | 默认false |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 账户详情 | - |
| data.id | Long | 勋章账户ID | 1 |
| data.accountCode | String | 账户代码 | "LOYALTY_BADGE" |
| data.accountName | String | 账户名称 | "忠诚勋章" |
| data.accountType | String | 账户类型 | "SYSTEM" |
| data.badgeType | String | 勋章类型 | "ACHIEVEMENT" |
| data.badgeCategory | String | 勋章分类 | "LOYALTY" |
| data.brandCode | String | 品牌代码 | "BRAND_001" |
| data.status | String | 状态 | "ACTIVE" |
| data.description | String | 勋章描述 | "忠诚用户专属勋章" |
| data.gainCondition | String | 获得条件 | "{\"type\": \"CONSECUTIVE_DAYS\", \"targetValue\": 30}" |
| data.rewardConfig | String | 奖励配置 | "{\"pointReward\": 500}" |
| data.basicConfig | String | 基础配置 | "{\"maxObtainCount\": 1, \"validDays\": 365}" |
| data.riskControlConfig | String | 风控配置 | "{\"dailyLimit\": 100, \"monthlyLimit\": 1000}" |
| data.createTime | String | 创建时间 | "2024-01-01 00:00:00" |
| data.updateTime | String | 更新时间 | "2024-01-15 10:30:00" |
| data.statistics | Object | 统计信息 | - |
| data.statistics.totalUsers | Long | 用户总数 | 1000 |
| data.statistics.activeUsers | Long | 活跃用户数 | 800 |
| data.statistics.totalAwards | Long | 总发放数 | 1200 |
| data.statistics.todayAwards | Long | 今日发放数 | 50 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章账户不存在 | 指定的勋章账户不存在 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.1.4 更新勋章账户

**基本信息**
- **接口路径**：`PUT /admin/api/v1/badge/account/{accountCode}`
- **接口描述**：更新勋章账户信息
- **权限要求**：管理员权限
- **限流策略**：50次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Path | 是 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| accountName | String | Body | 否 | 账户名称 | "忠诚勋章升级版" | 长度1-64 |
| status | String | Body | 否 | 状态 | "ACTIVE" | ACTIVE/INACTIVE |
| description | String | Body | 否 | 勋章描述 | "忠诚用户专属勋章升级版" | 长度1-200 |
| gainCondition | Object | Body | 否 | 获得条件 | - | JSON对象 |
| rewardConfig | Object | Body | 否 | 奖励配置 | - | JSON对象 |
| basicConfig | Object | Body | 否 | 基础配置 | - | JSON对象 |
| riskControlConfig | Object | Body | 否 | 风控配置 | - | JSON对象 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "更新成功" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章账户不存在 | 指定的勋章账户不存在 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.1.5 删除勋章账户

**基本信息**
- **接口路径**：`DELETE /admin/api/v1/badge/account/{accountCode}`
- **接口描述**：删除勋章账户
- **权限要求**：管理员权限
- **限流策略**：10次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Path | 是 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "删除成功" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章账户不存在 | 指定的勋章账户不存在 |
| 409 | 账户正在使用 | 账户下存在用户勋章，无法删除 |
| 500 | 系统错误 | 服务器内部错误 |

### 3.2 勋章发放管理

#### 3.2.1 手动发放勋章

**基本信息**
- **接口路径**：`POST /admin/api/v1/badge/award`
- **接口描述**：手动发放勋章给用户
- **权限要求**：管理员权限
- **限流策略**：50次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Body | 是 | 用户ID | 123456 | 长度1-64 |
| badgeId | Long | Body | 是 | 勋章ID | 100 | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| source | String | Body | 是 | 发放来源 | "MANUAL" | 长度1-32 |
| businessId | String | Body | 否 | 业务ID | "manual_001" | 长度1-64 |
| reason | String | Body | 是 | 发放原因 | "管理员手动发放" | 长度1-100 |
| extraData | Object | Body | 否 | 额外数据 | {"operator": "admin001", "note": "特殊奖励"} | JSON对象 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "发放成功" |
| data | Object | 响应数据 | - |
| data.userBadgeId | Long | 用户勋章ID | 1 |
| data.badgeCode | String | 勋章代码 | "LOYALTY_BADGE" |
| data.badgeName | String | 勋章名称 | "忠诚勋章" |
| data.obtainTime | String | 获得时间 | "2024-01-15 10:30:00" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章不存在 | 指定的勋章不存在 |
| 409 | 勋章已获得 | 用户已获得该勋章 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.2.2 撤销用户勋章

**基本信息**
- **接口路径**：`POST /admin/api/v1/badge/revoke`
- **接口描述**：撤销用户已获得的勋章
- **权限要求**：管理员权限
- **限流策略**：30次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Body | 是 | 用户ID | 123456 | 长度1-64 |
| userBadgeId | Long | Body | 是 | 用户勋章ID | 1 | 长度1-64 |
| reason | String | Body | 是 | 撤销原因 | "违规操作" | 长度1-100 |
| operatorId | Long | Body | 是 | 操作人ID | 1001 | 长度1-64 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "撤销成功" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章不存在 | 指定的用户勋章不存在 |
| 409 | 勋章已撤销 | 该勋章已被撤销 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.2.3 批量发放勋章

**基本信息**
- **接口路径**：`POST /admin/api/v1/badge/batch/award`
- **接口描述**：批量发放勋章给多个用户
- **权限要求**：管理员权限
- **限流策略**：10次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| badgeId | Long | Body | 是 | 勋章ID | 100 | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| userIds | Array | Body | 是 | 用户ID列表 | [123456, 123457, 123458] | 最多100个 |
| source | String | Body | 是 | 发放来源 | "BATCH_MANUAL" | 长度1-32 |
| reason | String | Body | 是 | 发放原因 | "批量活动奖励" | 长度1-100 |
| operatorId | Long | Body | 是 | 操作人ID | 1001 | 长度1-64 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "批量发放完成" |
| data | Object | 响应数据 | - |
| data.totalCount | Integer | 总用户数 | 100 |
| data.successCount | Integer | 成功数量 | 95 |
| data.failCount | Integer | 失败数量 | 5 |
| data.failDetails | Array | 失败详情 | - |
| data.failDetails[].userId | Long | 用户ID | 123459 |
| data.failDetails[].reason | String | 失败原因 | "用户已获得该勋章" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 404 | 勋章不存在 | 指定的勋章不存在 |
| 413 | 用户数量超限 | 用户ID列表超过最大限制 |
| 500 | 系统错误 | 服务器内部错误 |

### 3.3 勋章记录管理

#### 3.3.1 查询勋章记录

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/records`
- **接口描述**：查询勋章操作记录
- **权限要求**：管理员权限
- **限流策略**：100次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Query | 否 | 用户ID | 123456 | 长度1-64 |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| badgeId | Long | Query | 否 | 勋章ID | 100 | 长度1-64 |
| operationType | String | Query | 否 | 操作类型 | "AWARD" | AWARD/REVOKE/EXPIRE/DISPLAY/HIDE |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01" | 格式：yyyy-MM-dd |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31" | 格式：yyyy-MM-dd |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页大小 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Long | 总记录数 | 100 |
| data.list | Array | 记录列表 | - |
| data.list[].id | Long | 记录ID | 1 |
| data.list[].userId | Long | 用户ID | 123456 |
| data.list[].badgeId | Long | 勋章ID | 100 |
| data.list[].userBadgeId | Long | 用户勋章ID | 1 |
| data.list[].accountCode | String | 账户代码 | "LOYALTY_BADGE" |
| data.list[].accountName | String | 账户名称 | "忠诚勋章" |
| data.list[].operationType | String | 操作类型 | "AWARD" |
| data.list[].operationTime | String | 操作时间 | "2024-01-15 10:30:00" |
| data.list[].operatorId | Long | 操作人ID | 1001 |
| data.list[].source | String | 操作来源 | "PURCHASE" |
| data.list[].transactionId | String | 交易ID | "BADGE_1705123456_abc123" |
| data.list[].businessId | String | 业务ID | "order_001" |
| data.list[].beforeStatus | String | 操作前状态 | null |
| data.list[].afterStatus | String | 操作后状态 | "VALID" |
| data.list[].reason | String | 操作原因 | "系统自动发放" |
| data.list[].operationData | String | 操作数据 | "{\"orderAmount\": 299.00}" |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.3.2 查询用户勋章记录

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/user/{userId}/records`
- **接口描述**：查询指定用户的勋章记录
- **权限要求**：管理员权限
- **限流策略**：100次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | Long | Path | 是 | 用户ID | 123456 | 长度1-64 |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| operationType | String | Query | 否 | 操作类型 | "AWARD" | AWARD/REVOKE/EXPIRE/DISPLAY/HIDE |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页大小 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Long | 总记录数 | 50 |
| data.list | Array | 记录列表 | - |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

### 3.4 勋章统计分析

#### 3.4.1 勋章发放统计

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/stat/award`
- **接口描述**：查询勋章发放统计数据
- **权限要求**：管理员权限
- **限流策略**：50次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| badgeId | Long | Query | 否 | 勋章ID | 100 | 长度1-64 |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01" | 格式：yyyy-MM-dd |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31" | 格式：yyyy-MM-dd |
| groupBy | String | Query | 否 | 分组维度 | "DAY" | DAY/WEEK/MONTH |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.summary | Object | 汇总信息 | - |
| data.summary.totalUsers | Long | 总用户数 | 1000 |
| data.summary.obtainedUsers | Long | 获得用户数 | 150 |
| data.summary.obtainRate | Double | 获得率 | 15.0 |
| data.summary.totalObtainCount | Long | 总获得次数 | 180 |
| data.summary.avgObtainCount | Double | 平均获得次数 | 1.2 |
| data.trends | Array | 趋势数据 | - |
| data.trends[].date | String | 日期 | "2024-01-01" |
| data.trends[].awardCount | Long | 发放数量 | 10 |
| data.trends[].userCount | Long | 用户数量 | 8 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.4.2 勋章获得统计

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/stat/obtain`
- **接口描述**：查询勋章获得统计数据
- **权限要求**：管理员权限
- **限流策略**：50次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| badgeType | String | Query | 否 | 勋章类型 | "ACHIEVEMENT" | ACHIEVEMENT/MILESTONE/ACTIVITY/SPECIAL |
| badgeCategory | String | Query | 否 | 勋章分类 | "LOYALTY" | PURCHASE/SOCIAL/GROWTH/ACTIVITY/SPECIAL |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01" | 格式：yyyy-MM-dd |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31" | 格式：yyyy-MM-dd |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.typeDistribution | Array | 类型分布 | - |
| data.typeDistribution[].badgeType | String | 勋章类型 | "ACHIEVEMENT" |
| data.typeDistribution[].count | Long | 数量 | 100 |
| data.typeDistribution[].percentage | Double | 百分比 | 50.0 |
| data.categoryDistribution | Array | 分类分布 | - |
| data.categoryDistribution[].badgeCategory | String | 勋章分类 | "PURCHASE" |
| data.categoryDistribution[].count | Long | 数量 | 80 |
| data.categoryDistribution[].percentage | Double | 百分比 | 40.0 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

#### 3.4.3 勋章进度统计

**基本信息**
- **接口路径**：`GET /admin/api/v1/badge/stat/progress`
- **接口描述**：查询勋章进度统计数据
- **权限要求**：管理员权限
- **限流策略**：50次/分钟
- **幂等性**：是

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Query | 否 | 账户代码 | "LOYALTY_BADGE" | 长度1-32 |
| badgeId | Long | Query | 否 | 勋章ID | 100 | 长度1-64 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.totalProgressCount | Long | 总进度记录数 | 500 |
| data.completedCount | Long | 已完成数量 | 150 |
| data.completionRate | Double | 完成率 | 30.0 |
| data.progressDistribution | Array | 进度分布 | - |
| data.progressDistribution[].progressRange | String | 进度范围 | "0-20%" |
| data.progressDistribution[].count | Long | 数量 | 100 |
| data.progressDistribution[].percentage | Double | 百分比 | 20.0 |

**错误码**

| 错误码 | 错误信息 | 描述 |
| ------ | -------- | ---- |
| 400 | 参数错误 | 请求参数格式错误或缺失必填参数 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问该接口 |
| 500 | 系统错误 | 服务器内部错误 |

## 4. 通用规范

### 4.1 响应格式

所有API接口均采用统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | Integer | 响应码，200表示成功，非200表示失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| timestamp | Long | 响应时间戳 |
| traceId | String | 链路追踪ID |

### 4.2 分页响应格式

对于分页查询接口，data字段结构如下：

```json
{
  "total": 100,
  "pageNum": 1,
  "pageSize": 10,
  "pages": 10,
  "list": []
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Long | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页记录数 |
| pages | Integer | 总页数 |
| list | Array | 数据列表 |

### 4.3 错误码定义

| 错误码 | 描述 | 处理建议 |
| ------ | ---- | -------- |
| 200 | 成功 | - |
| 400 | 参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 检查用户登录状态和token有效性 |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 检查请求的资源ID是否正确 |
| 409 | 资源冲突 | 检查资源是否已存在或状态冲突 |
| 413 | 请求实体过大 | 减少请求数据量 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 系统内部错误 | 联系系统管理员 |

### 4.4 业务错误码

| 错误码 | 描述 | 处理建议 |
| ------ | ---- | -------- |
| 10001 | 勋章账户不存在 | 检查勋章账户代码是否正确 |
| 10002 | 用户勋章不存在 | 检查用户勋章ID是否正确 |
| 10003 | 勋章已获得 | 用户已获得该勋章，无法重复获得 |
| 10004 | 勋章已撤销 | 该勋章已被撤销，无法操作 |
| 10005 | 勋章已过期 | 该勋章已过期，无法操作 |
| 10006 | 勋章条件不满足 | 用户不满足勋章获得条件 |
| 10007 | 勋章账户已停用 | 勋章账户已停用，无法操作 |
| 10008 | 勋章正在使用中 | 勋章账户下存在用户勋章，无法删除 |

### 4.5 技术规范

- **协议**：HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **API版本**：v1
- **认证方式**：Bearer Token / API Key
- **限流策略**：基于用户/IP的令牌桶算法

### 4.6 请求头规范

| 头部名称 | 是否必填 | 描述 | 示例 |
| -------- | -------- | ---- | ---- |
| Content-Type | 是 | 请求内容类型 | application/json |
| Authorization | 是 | 认证信息 | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |
| X-Request-ID | 否 | 请求唯一标识 | req-********* |
| X-Client-Version | 否 | 客户端版本 | 1.0.0 |
| X-Platform | 否 | 客户端平台 | web/ios/android |

## 5. 示例代码

### 5.1 JavaScript示例

```javascript
// 查询用户勋章列表
async function getUserBadges(userId, accountCode) {
  try {
    const response = await fetch(`/api/v1/badge/user/list?userId=${userId}&accountCode=${accountCode}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      }
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取用户勋章失败:', error);
    throw error;
  }
}

// 手动发放勋章
async function awardBadge(userId, badgeId, accountCode, reason) {
  try {
    const response = await fetch('/admin/api/v1/badge/award', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + getToken()
      },
      body: JSON.stringify({
        userId: userId,
        badgeId: badgeId,
        accountCode: accountCode,
        source: 'MANUAL',
        reason: reason
      })
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('发放勋章失败:', error);
    throw error;
  }
}
```

### 5.2 Java示例

```java
// 查询用户勋章列表
@RestController
@RequestMapping("/api/v1/badge")
public class BadgeController {
    
    @Autowired
    private BadgeService badgeService;
    
    @GetMapping("/user/list")
    public Result<PageResult<UserBadgeVO>> getUserBadges(
            @RequestParam Long userId,
            @RequestParam String accountCode,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        
        try {
            PageResult<UserBadgeVO> result = badgeService.getUserBadges(
                userId, accountCode, pageNum, pageSize);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询用户勋章失败: userId={}, accountCode={}", userId, accountCode, e);
            return Result.error("查询用户勋章失败");
        }
    }
}

// 勋章服务实现
@Service
public class BadgeServiceImpl implements BadgeService {
    
    @Override
    public PageResult<UserBadgeVO> getUserBadges(Long userId, String accountCode, 
            Integer pageNum, Integer pageSize) {
        
        // 参数校验
        if (userId == null || StringUtils.isBlank(accountCode)) {
            throw new BadgeException("参数错误");
        }
        
        // 分页查询
        PageHelper.startPage(pageNum, pageSize);
        List<UserBadge> badges = userBadgeRepository.findByUserIdAndAccountCode(userId, accountCode);
        
        // 转换VO
        List<UserBadgeVO> badgeVOs = badges.stream()
            .map(UserBadgeVO::from)
            .collect(Collectors.toList());
        
        PageInfo<UserBadge> pageInfo = new PageInfo<>(badges);
        return PageResult.<UserBadgeVO>builder()
            .total(pageInfo.getTotal())
            .pageNum(pageInfo.getPageNum())
            .pageSize(pageInfo.getPageSize())
            .pages(pageInfo.getPages())
            .list(badgeVOs)
            .build();
    }
}
```

### 5.3 Python示例

```python
import requests
import json

class BadgeClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
    
    def get_user_badges(self, user_id, account_code, page_num=1, page_size=20):
        """查询用户勋章列表"""
        url = f"{self.base_url}/api/v1/badge/user/list"
        params = {
            'userId': user_id,
            'accountCode': account_code,
            'pageNum': page_num,
            'pageSize': page_size
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers)
            response.raise_for_status()
            
            result = response.json()
            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(result['message'])
        except Exception as e:
            print(f"获取用户勋章失败: {e}")
            raise
    
    def award_badge(self, user_id, badge_id, account_code, reason):
        """手动发放勋章"""
        url = f"{self.base_url}/admin/api/v1/badge/award"
        data = {
            'userId': user_id,
            'badgeId': badge_id,
            'accountCode': account_code,
            'source': 'MANUAL',
            'reason': reason
        }
        
        try:
            response = requests.post(url, json=data, headers=self.headers)
            response.raise_for_status()
            
            result = response.json()
            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(result['message'])
        except Exception as e:
            print(f"发放勋章失败: {e}")
            raise

# 使用示例
client = BadgeClient('https://api.example.com', 'your-token')

# 查询用户勋章
badges = client.get_user_badges(123456, 'MALL_BADGE')
print(f"用户勋章数量: {badges['total']}")

# 发放勋章
result = client.award_badge(123456, 100, 'LOYALTY_BADGE', '管理员手动发放')
print(f"发放成功，勋章ID: {result['userBadgeId']}")
```

## 6. 测试用例

### 6.1 用户端接口测试

```bash
# 查询用户勋章列表
curl -X GET "https://api.example.com/api/v1/badge/user/list?userId=123456&accountCode=MALL_BADGE" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"

# 查询勋章详情
curl -X GET "https://api.example.com/api/v1/badge/detail/1?userId=123456" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"

# 更新勋章展示设置
curl -X PUT "https://api.example.com/api/v1/badge/display" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 123456,
    "userBadgeId": 1,
    "isDisplayed": true,
    "displayOrder": 1
  }'

# 查询勋章进度
curl -X GET "https://api.example.com/api/v1/badge/progress?userId=123456&accountCode=MALL_BADGE" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"
```

### 6.2 管理端接口测试

```bash
# 创建勋章账户
curl -X POST "https://api.example.com/admin/api/v1/badge/account" \
  -H "Authorization: Bearer admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "accountCode": "LOYALTY_BADGE",
    "accountName": "忠诚勋章",
    "accountType": "SYSTEM",
    "badgeType": "ACHIEVEMENT",
    "badgeCategory": "LOYALTY",
    "brandCode": "BRAND_001",
    "description": "忠诚用户专属勋章",
    "gainCondition": {
      "type": "CONSECUTIVE_DAYS",
      "targetValue": 30,
      "actionType": "PURCHASE",
      "eventTypes": ["ORDER_COMPLETED"]
    },
    "rewardConfig": {
      "pointReward": 500
    }
  }'

# 手动发放勋章
curl -X POST "https://api.example.com/admin/api/v1/badge/award" \
  -H "Authorization: Bearer admin-token" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 123456,
    "badgeId": 100,
    "accountCode": "LOYALTY_BADGE",
    "source": "MANUAL",
    "reason": "管理员手动发放"
  }'

# 查询勋章统计
curl -X GET "https://api.example.com/admin/api/v1/badge/stat/award?accountCode=LOYALTY_BADGE&startTime=2024-01-01&endTime=2024-01-31" \
  -H "Authorization: Bearer admin-token" \
  -H "Content-Type: application/json"
```

## 7. 版本更新记录

| 版本 | 日期 | 更新内容 | 作者 |
| ---- | ---- | -------- | ---- |
| v1.0.0 | 2024-01-15 | 初始版本，包含基础的勋章管理功能 | 开发团队 |
| v1.1.0 | 2024-02-01 | 新增批量发放勋章接口 | 开发团队 |
| v1.2.0 | 2024-02-15 | 新增勋章进度统计接口 | 开发团队 |

## 8. 附录

### 8.1 勋章类型说明

| 类型 | 描述 | 示例 |
| ---- | ---- | ---- |
| ACHIEVEMENT | 成就勋章 | 首次购买、连续签到等 |
| MILESTONE | 里程碑勋章 | 累计消费达标、会员升级等 |
| ACTIVITY | 活动勋章 | 节日活动、限时活动等 |
| SPECIAL | 特殊勋章 | 内测用户、VIP专属等 |

### 8.2 勋章分类说明

| 分类 | 描述 | 示例 |
| ---- | ---- | ---- |
| PURCHASE | 购买类 | 首单达人、购物狂人等 |
| SOCIAL | 社交类 | 分享达人、邀请专家等 |
| GROWTH | 成长类 | 新手上路、经验丰富等 |
| ACTIVITY | 活动类 | 活动参与者、任务完成者等 |
| SPECIAL | 特殊类 | 内测勋章、纪念勋章等 |

### 8.3 操作类型说明

| 操作类型 | 描述 |
| -------- | ---- |
| AWARD | 发放勋章 |
| REVOKE | 撤销勋章 |
| EXPIRE | 勋章过期 |
| DISPLAY | 设置展示 |
| HIDE | 隐藏勋章 |
| UPDATE | 更新勋章 |

---