﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelStatQueryResult extends Extensible {    
    /**
     * 等级统计列表
     */
    private java.util.List<LevelStat> statList;    
    /**
     * 总用户数
     */
    private Long totalUser;    
    /**
     * 总升级次数
     */
    private Long totalUpgrade;    
    /**
     * 总降级次数
     */
    private Long totalDowngrade;    
    /**
     * 统计时间
     */
    private String statTime;}
