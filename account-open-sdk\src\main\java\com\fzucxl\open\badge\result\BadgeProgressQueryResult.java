﻿package com.fzucxl.open.badge.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章进度结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeProgressQueryResult extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 当前进度
     */
    private Integer currentProgress;    
    /**
     * 目标进度
     */
    private Integer targetProgress;    
    /**
     * 进度百分比
     */
    private java.math.BigDecimal progressPercentage;    
    /**
     * 是否完成
     */
    private Boolean isCompleted;    
    /**
     * 下一个里程碑
     */
    private Integer nextMilestone;    
    /**
     * 预计完成时间
     */
    private String estimatedCompletionTime;}
