﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级变更统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelChangeStatQueryResult extends Extensible {    
    /**
     * 等级变更统计列表
     */
    private java.util.List<LevelChangeStat> statList;    
    /**
     * 总升级次数
     */
    private Long totalUpgrade;    
    /**
     * 总降级次数
     */
    private Long totalDowngrade;    
    /**
     * 统计时间
     */
    private String statTime;}
