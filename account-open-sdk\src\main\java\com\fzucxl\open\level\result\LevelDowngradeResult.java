﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级降级结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelDowngradeResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 降级前等级
     */
    private Integer beforeLevel;    
    /**
     * 降级后等级
     */
    private Integer afterLevel;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 降级时间
     */
    private String downgradeTime;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 处理状态
     */
    private String status;}
