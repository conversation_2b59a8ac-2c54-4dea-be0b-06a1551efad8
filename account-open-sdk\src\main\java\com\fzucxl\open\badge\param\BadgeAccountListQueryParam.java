﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章账户列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccountListQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 交易ID
     */
    private String transactionId;}
