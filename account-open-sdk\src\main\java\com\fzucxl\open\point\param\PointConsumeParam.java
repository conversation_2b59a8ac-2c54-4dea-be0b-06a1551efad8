﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 消费积分参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointConsumeParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 消费积分数量
     */
    private Long point;    
    /**
     * 消费来源代码
     */
    private String source;    
    /**
     * 来源名称
     */
    private String sourceName;    
    /**
     * 详细描述
     */
    private String description;    
    /**
     * 消费模式
     */
    private String consumeMode;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;    
    /**
     * 是否检查余额充足
     */
    private Boolean checkBalance;}
