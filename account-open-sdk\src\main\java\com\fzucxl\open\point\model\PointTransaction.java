﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分交易模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointTransaction extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 积分数量
     */
    private Long point;    
    /**
     * 交易前余额
     */
    private Long beforeBalance;    
    /**
     * 交易后余额
     */
    private Long afterBalance;    
    /**
     * 积分来源
     */
    private String source;    
    /**
     * 来源名称
     */
    private String sourceName;    
    /**
     * 详细描述
     */
    private String description;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易状态
     */
    private String status;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;    
    /**
     * 备注
     */
    private String remark;}
