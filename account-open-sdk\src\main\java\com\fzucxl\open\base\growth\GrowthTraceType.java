package com.fzucxl.open.base.growth;

/**
 * 追踪类型枚举
 */
public enum GrowthTraceType {
    EARN("发放追踪"),
    EXPIRE_TO_EARN("过期追踪发放"),
    REVOKE_TO_EARN("回收追踪发放"),
    TRANSFER_OUT("转出追踪"),
    TRANSFER_IN("转入追踪"),
    ADJUST_TO_EARN("调整追踪发放");

    private final String description;

    GrowthTraceType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
