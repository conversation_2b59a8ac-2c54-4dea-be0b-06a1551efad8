﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分发放统计结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointIssueStatQueryResult extends Extensible {    
    /**
     * 总发放积分
     */
    private Long totalIssued;    
    /**
     * 总用户数
     */
    private Integer totalUser;    
    /**
     * 平均发放积分
     */
    private Double avgIssued;    
    /**
     * 发放统计详情
     */
    private java.util.List<PointIssueStatDetail> statDetail;}
