﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值排行查询参数（管理端）
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRankingQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 排行类型：TOTAL-总成长值，EARN-获得成长值，ACTIVITY-活跃成长值
     */
    private String type;    
    /**
     * 统计周期：ALL-全部，YEAR-本年，MONTH-本月，WEEK-本周
     */
    private String period;    
    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    private String startDate;    
    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    private String endDate;    
    /**
     * 返回记录数，默认100，最大1000
     */
    private Integer limit;}
