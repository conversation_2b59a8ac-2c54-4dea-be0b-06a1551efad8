package com.fzucxl.entity.point;

import com.fzucxl.open.base.RecordStatus;
import com.fzucxl.open.base.point.PointTransactionType;
import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 积分明细实体
 * 
 * <AUTHOR>
 */
@MappedEntity("point_record")
@Data
public class PointRecord {
    
    @Id
    @GeneratedValue
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;
    
    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 交易类型
     */
    private PointTransactionType transactionType;

    /**
     * 积分余额变化前
     */
    private Long balanceBefore;

    /**
     * 积分变化量
     */
    private Long point;

    /**
     * 积分余额变化后
     */
    private Long balanceAfter;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则
     */
    private String source;
    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 描述
     */
    private String description;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 状态
     */
    private RecordStatus status = RecordStatus.VALID;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 扩展数据
     */
    private String extraData;
}