﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值规则模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRule extends Extensible {    
    /**
     * 规则ID
     */
    private Long id;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 基础成长值
     */
    private Long baseGrowth;    
    /**
     * 倍率
     */
    private java.math.BigDecimal multiplier;    
    /**
     * 每日限制
     */
    private Long dailyLimit;    
    /**
     * 每月限制
     */
    private Long monthlyLimit;    
    /**
     * 规则表达式
     */
    private String ruleExpression;    
    /**
     * 优先级
     */
    private Integer priority;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 创建时间
     */
    private String createTime;}
