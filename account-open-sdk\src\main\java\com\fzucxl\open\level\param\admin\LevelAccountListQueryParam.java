﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级账户列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelAccountListQueryParam extends Extensible {    
    /**
     * 账户代码（模糊查询）
     */
    private String accountCode;    
    /**
     * 账户名称（模糊查询）
     */
    private String accountName;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 交易ID
     */
    private String transactionId;}
