﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值明细结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthDetailQueryResult extends Extensible {    
    /**
     * 成长值明细列表
     */
    private java.util.List<GrowthDetail> detailList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
