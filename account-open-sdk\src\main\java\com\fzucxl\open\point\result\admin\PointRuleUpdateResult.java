﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新积分规则结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleUpdateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 更新时间
     */
    private String updateTime;}
