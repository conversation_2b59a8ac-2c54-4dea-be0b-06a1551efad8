package com.fzucxl.open.base.point;

/**
 * 追踪类型枚举
 */
public enum PointTraceType {
    EARN("发放追踪"),
    CONSUME_TO_EARN("消费追踪发放"),
    TRANSFER_OUT("转出追踪"),
    TRANSFER_IN("转入追踪"),
    EXPIRE_TO_EARN("过期追踪发放"),
    FREEZE_TO_EARN("冻结追踪发放"),
    UNFREEZE_TO_EARN("解冻追踪发放");

    private final String description;

    PointTraceType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
