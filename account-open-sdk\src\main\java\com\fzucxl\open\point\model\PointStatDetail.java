﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分统计详情模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointStatDetail extends Extensible {    
    /**
     * 统计日期
     */
    private String statDate;    
    /**
     * 统计类型
     */
    private String statType;    
    /**
     * 发放积分
     */
    private Long totalIssued;    
    /**
     * 消费积分
     */
    private Long totalConsumed;    
    /**
     * 冻结积分
     */
    private Long totalFrozen;    
    /**
     * 过期积分
     */
    private Long totalExpired;    
    /**
     * 活跃用户数
     */
    private Integer activeUser;}
