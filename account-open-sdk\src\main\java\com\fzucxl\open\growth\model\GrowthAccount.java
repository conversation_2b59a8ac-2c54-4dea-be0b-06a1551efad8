﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值账户模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAccount extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 总成长值
     */
    private Long totalGrowth;    
    /**
     * 可用成长值
     */
    private Long availableGrowth;    
    /**
     * 冻结成长值
     */
    private Long frozenGrowth;    
    /**
     * 账户状态
     */
    private String status;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;}
