package com.fzucxl.open.base.point;

/**
 * 冻结记录状态枚举
 */
public enum FreezeRecordStatus {
    FROZEN("冻结中"),
    UNFROZEN("已解冻"),
    WRITTEN_OFF("已核销"),
    EXPIRED("已过期");

    private final String description;

    FreezeRecordStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为有效冻结状态（需要扣减余额）
     */
    public boolean isEffectiveFrozen() {
        return this == FROZEN;
    }
}
