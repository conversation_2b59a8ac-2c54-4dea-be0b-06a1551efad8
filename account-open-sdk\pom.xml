<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>account-open-sdk</artifactId>
    <version>0.1.0</version>
    <packaging>${packaging}</packaging>

    <parent>
        <groupId>com.fzucxl</groupId>
        <artifactId>fast-account</artifactId>
        <version>0.1.0</version>
    </parent>

    <properties>
        <packaging>jar</packaging>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <micronaut.version>4.9.2</micronaut.version>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>com.fzucxl.aot.generated</micronaut.aot.packageName>
        <micronaut.runtime>netty</micronaut.runtime>
        <exec.mainClass>com.fzucxl.Application</exec.mainClass>
    </properties>

    <dependencies>

        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>
