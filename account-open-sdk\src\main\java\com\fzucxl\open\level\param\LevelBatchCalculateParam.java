﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 批量等级计算参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelBatchCalculateParam extends Extensible {    
    /**
     * 用户等级计算列表
     */
    private java.util.List<UserLevelCalculate> userLevelList;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 计算方式：POINT(积分), GROWTH(成长值)
     */
    private String calculationType;    
    /**
     * 交易ID
     */
    private String transactionId;}
