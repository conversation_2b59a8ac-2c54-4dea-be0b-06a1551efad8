-- 积分规则管理相关表结构
-- 积分规则表
CREATE TABLE IF NOT EXISTS `point_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码',
  `rule_code` varchar(100) NOT NULL COMMENT '规则代码',
  `rule_name` varchar(200) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '规则描述',
  `conditionRule` text NOT NULL COMMENT '条件规则表达式',
  `actionRule` text NOT NULL COMMENT '动作规则表达式',
  `priority` int(11) DEFAULT 0 COMMENT '优先级',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `effective_time` datetime COMMENT '生效时间',
  `expire_time` datetime COMMENT '失效时间',
  `created_by` varchar(100) NOT NULL COMMENT '创建人',
  `updated_by` varchar(100) NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_rule` (`account_code`, `rule_code`),
  KEY `idx_account_code` (`account_code`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分规则表';

INSERT INTO `point_rule` (
  `account_code`, `rule_code`, `rule_name`, `description`, `rule_expression`,
  `priority`, `status`, `created_by`, `updated_by`
) VALUES (
  'account_a', 'vip_upgrade_rule', 'VIP升级规则', '根据购买次数判断是否可以升级VIP',
  '${purchase_count_1year} >= 10',
  1, 'ACTIVE', 'system', 'system'
),
(
  'account_b', 'discount_rule', '折扣规则', '根据购买次数给予不同折扣',
  '${purchase_count_1year} >= 5 ? 0.8 : 1.0',
  1, 'ACTIVE', 'system', 'system'
);

-- 用户积分表
CREATE TABLE IF NOT EXISTS `user_point` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `total_point` bigint(20) NOT NULL DEFAULT 0 COMMENT '总积分',
  `available_point` bigint(20) NOT NULL DEFAULT 0 COMMENT '可用积分',
  `frozen_point` bigint(20) NOT NULL DEFAULT 0 COMMENT '冻结积分',
  `expired_point` bigint(20) NOT NULL DEFAULT 0 COMMENT '已过期积分',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态：ACTIVE-正常，INACTIVE-停用，FROZEN-冻结，DELETED-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_account_brand` (`user_id`, `account_code`, `brand_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_code` (`account_code`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 积分账户表
CREATE TABLE IF NOT EXISTS `point_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `account_name` varchar(200) NOT NULL COMMENT '账户名称',
  `account_type` varchar(50) NOT NULL COMMENT '账户类型',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态',
  `description` varchar(500) COMMENT '账户描述',
  `basic_config` text COMMENT '基础配置',
  `risk_control_config` text COMMENT '风控配置',
  `extension_config` text COMMENT '扩展配置',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_code` (`account_code`),
  UNIQUE KEY `uk_account_brand` (`account_code`, `brand_code`),
  KEY `idx_account_code` (`account_code`),
  KEY `idx_account_type` (`account_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分账户表';

-- 积分冻结记录表
CREATE TABLE IF NOT EXISTS `point_freeze_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `frozen_point` bigint(20) NOT NULL COMMENT '冻结积分',
  `freeze_time` datetime NOT NULL COMMENT '冻结时间',
  `unfreeze_time` datetime COMMENT '解冻时间',
  `freeze_deadline` datetime COMMENT '冻结截止时间',
  `writeoff_time` datetime COMMENT '核销时间',
  `freeze_transaction_id` varchar(100) NOT NULL COMMENT '冻结交易ID',
  `unfreeze_transaction_id` varchar(100) COMMENT '解冻交易ID',
  `writeoff_transaction_id` varchar(100) COMMENT '核销交易ID',
  `status` varchar(20) NOT NULL DEFAULT 'FROZEN' COMMENT '冻结状态',
  `freeze_reason` varchar(500) COMMENT '冻结原因',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `operator_id` bigint(20) COMMENT '操作员ID',
  `remark` varchar(500) COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_code` (`account_code`),
  KEY `idx_freeze_transaction_id` (`freeze_transaction_id`),
  KEY `idx_freeze_time` (`freeze_time`),
  KEY `idx_business_id` (`business_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分冻结记录表';

-- 积分明细表
CREATE TABLE IF NOT EXISTS `point_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `channel_type` varchar(50) NOT NULL COMMENT '渠道类型',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `balance_before` bigint(20) NOT NULL COMMENT '积分余额变化前',
  `point` bigint(20) NOT NULL COMMENT '积分变化量',
  `balance_after` bigint(20) NOT NULL COMMENT '积分余额变化后',
  `rule_code` varchar(100) COMMENT '规则编码',
  `source` varchar(50) NOT NULL COMMENT '来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则',
  `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
  `description` varchar(500) COMMENT '描述',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  `expire_time` datetime COMMENT '失效时间',
  `status` varchar(20) NOT NULL DEFAULT 'VALID' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `extra_data` text COMMENT '扩展数据',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),-- 积分明细追踪表
  KEY `idx_account_code` (`account_code`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分明细表';

CREATE TABLE IF NOT EXISTS `point_record_trace` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `source_transaction_id` varchar(100) DEFAULT NULL COMMENT '源交易ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `trace_type` varchar(50) NOT NULL COMMENT '追踪类型',
  `point` bigint(20) NOT NULL COMMENT '积分',
  `source_record_id` bigint(20) DEFAULT NULL COMMENT '源明细ID',
  `target_record_id` bigint(20) DEFAULT NULL COMMENT '目标明细ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `trace_reason` varchar(500) DEFAULT NULL COMMENT '追踪原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_source_transaction_id` (`source_transaction_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分明细追踪表';

-- 积分交易记录表
CREATE TABLE IF NOT EXISTS `point_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `point` bigint(20) NOT NULL COMMENT '交易积分',
  `balance_before` bigint(20) NOT NULL COMMENT '变更前余额',
  `balance_after` bigint(20) NOT NULL COMMENT '变更后余额',
  `available_before` bigint(20) NOT NULL COMMENT '可用积分变更前余额',
  `available_after` bigint(20) NOT NULL COMMENT '可用积分变更后余额',
  `frozen_before` bigint(20) NOT NULL DEFAULT 0 COMMENT '冻结积分变更前余额',
  `frozen_after` bigint(20) NOT NULL DEFAULT 0 COMMENT '冻结积分变更后余额',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `source` varchar(100) DEFAULT NULL COMMENT '来源',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `transaction_status` varchar(20) NOT NULL DEFAULT 'SUCCESS' COMMENT '交易状态',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
  `input_data` text DEFAULT NULL COMMENT '事务入参',
  `completeTime` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分交易记录表';

-- 有效积分记录表
CREATE TABLE IF NOT EXISTS `point_valid_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `channel_type` varchar(50) NOT NULL COMMENT '渠道类型',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `issued_point` bigint(20) NOT NULL COMMENT '积分发放数量',
  `remaining_point` bigint(20) NOT NULL COMMENT '剩余积分数量',
  `issue_time` datetime NOT NULL COMMENT '积分发放时间',
  `expire_time` datetime DEFAULT NULL COMMENT '积分过期时间',
  `record_status` varchar(20) NOT NULL DEFAULT 'VALID' COMMENT '记录状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_record_status` (`record_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='有效积分记录表';
