package com.fzucxl.entity.growth;

import com.fzucxl.open.base.growth.GrowthTransactionType;
import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.event.PrePersist;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@MappedEntity("growth_record")
@Data
public class GrowthRecord {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户代码
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;

    /**
     * 事务类型：EARN-获得，EXPIRE-过期，ADJUST-调整，TRANSFER-转移，REVOKE-回收
     */
    private GrowthTransactionType transactionType;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 成长值数量
     */
    private Long growthValue;

    /**
     * 业务类型：PURCHASE-购买，ACTIVITY-活动，SOCIAL-社交，TASK-任务
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则
     */
    private String source;
    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 原因描述
     */
    private String reason;

    /**
     * 事务ID
     */
    private String transactionId;

    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 基础成长值
     */
    private Long baseGrowth;

    /**
     * 倍率
     */
    private BigDecimal multiplier;

    /**
     * 扩展数据（JSON格式）
     */
    private String extraData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @PrePersist
    protected void onCreate() {
        this.createTime = LocalDateTime.now();
    }
}
