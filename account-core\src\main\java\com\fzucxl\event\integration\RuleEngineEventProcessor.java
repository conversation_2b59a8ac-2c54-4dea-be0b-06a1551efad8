package com.fzucxl.event.integration;

import com.fzucxl.entity.event.BusinessEventInstance;
import com.fzucxl.event.model.BusinessEventContext;
import com.fzucxl.event.service.BusinessEventService;
import com.fzucxl.point.rule.engine.RuleExecutionEngine;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;

/**
 * 规则引擎事件处理器
 * 将业务事件与规则引擎集成
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class RuleEngineEventProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(RuleEngineEventProcessor.class);
    
    @Inject
    private BusinessEventService businessEventService;
    
    @Inject
    private RuleExecutionEngine ruleExecutionEngine;
    
    /**
     * 处理业务事件，执行相关规则
     */
    public void processEvent(Long instanceId) {
        try {
            // 构建事件上下文
            BusinessEventContext context = businessEventService.buildEventContext(instanceId);
            
            log.info("开始处理业务事件: eventCode={}, userId={}, instanceId={}", 
                context.getEventCode(), context.getUserId(), instanceId);
            
            // 获取完整上下文用于规则执行
            Map<String, Object> ruleContext = context.getFullContext();
            
            // 执行规则引擎
            // 这里可以根据事件类型执行不同的规则
            executeRuleForEvent(context.getEventCode(), context.getUserId(), ruleContext);
            
            // 更新事件实例状态为已处理
            businessEventService.updateInstanceStatus(instanceId, "PROCESSED", "规则执行成功", null);
            
            log.info("业务事件处理完成: eventCode={}, userId={}, instanceId={}", 
                context.getEventCode(), context.getUserId(), instanceId);
                
        } catch (Exception e) {
            log.error("业务事件处理失败: instanceId={}", instanceId, e);
            
            // 更新事件实例状态为处理失败
            businessEventService.updateInstanceStatus(instanceId, "FAILED", null, e.getMessage());
        }
    }
    
    /**
     * 根据事件类型执行相应的规则
     */
    private void executeRuleForEvent(String eventCode, String userId, Map<String, Object> context) {
        switch (eventCode) {
            case "user_login":
                executeLoginRule(userId, context);
                break;
            case "user_purchase":
                executePurchaseRule(userId, context);
                break;
            case "user_share":
                executeShareRule(userId, context);
                break;
            case "user_comment":
                executeCommentRule(userId, context);
                break;
            case "user_signup":
                executeSignupRule(userId, context);
                break;
            default:
                log.warn("未知的事件类型，跳过规则执行: eventCode={}", eventCode);
        }
    }
    
    /**
     * 执行登录相关规则
     */
    private void executeLoginRule(String userId, Map<String, Object> context) {
        try {
            // 执行登录积分规则
            ruleExecutionEngine.executeRule("login_point_rule", userId, context, Collections.emptyMap());
            
            // 执行连续登录规则
            ruleExecutionEngine.executeRule("consecutive_login_rule", userId, context, Collections.emptyMap());
            
            log.info("登录规则执行完成: userId={}", userId);
        } catch (Exception e) {
            log.error("登录规则执行失败: userId={}", userId, e);
            throw e;
        }
    }
    
    /**
     * 执行购买相关规则
     */
    private void executePurchaseRule(String userId, Map<String, Object> context) {
        try {
            // 执行购买积分规则
            ruleExecutionEngine.executeRule("purchase_point_rule", userId, context, Collections.emptyMap());
            
            // 执行购买等级规则
            ruleExecutionEngine.executeRule("purchase_level_rule", userId, context, Collections.emptyMap());
            
            // 执行购买勋章规则
            ruleExecutionEngine.executeRule("purchase_badge_rule", userId, context, Collections.emptyMap());
            
            log.info("购买规则执行完成: userId={}", userId);
        } catch (Exception e) {
            log.error("购买规则执行失败: userId={}", userId, e);
            throw e;
        }
    }
    
    /**
     * 执行分享相关规则
     */
    private void executeShareRule(String userId, Map<String, Object> context) {
        try {
            // 执行分享积分规则
            ruleExecutionEngine.executeRule("share_point_rule", userId, context, Collections.emptyMap());
            
            // 执行分享成长值规则
            ruleExecutionEngine.executeRule("share_growth_rule", userId, context, Collections.emptyMap());
            
            log.info("分享规则执行完成: userId={}", userId);
        } catch (Exception e) {
            log.error("分享规则执行失败: userId={}", userId, e);
            throw e;
        }
    }
    
    /**
     * 执行评论相关规则
     */
    private void executeCommentRule(String userId, Map<String, Object> context) {
        try {
            // 执行评论积分规则
            ruleExecutionEngine.executeRule("comment_point_rule", userId, context, Collections.emptyMap());
            
            // 执行评论活跃度规则
            ruleExecutionEngine.executeRule("comment_activity_rule", userId, context, Collections.emptyMap());
            
            log.info("评论规则执行完成: userId={}", userId);
        } catch (Exception e) {
            log.error("评论规则执行失败: userId={}", userId, e);
            throw e;
        }
    }
    
    /**
     * 执行注册相关规则
     */
    private void executeSignupRule(String userId, Map<String, Object> context) {
        try {
            // 执行注册欢迎积分规则
            ruleExecutionEngine.executeRule("signup_welcome_rule", userId, context, Collections.emptyMap());
            
            // 执行新用户等级规则
            ruleExecutionEngine.executeRule("new_user_level_rule", userId, context, Collections.emptyMap());
            
            // 执行推荐奖励规则
            if (context.containsKey("referrer_id") && context.get("referrer_id") != null) {
                ruleExecutionEngine.executeRule("referral_reward_rule", userId, context, Collections.emptyMap());
            }
            
            log.info("注册规则执行完成: userId={}", userId);
        } catch (Exception e) {
            log.error("注册规则执行失败: userId={}", userId, e);
            throw e;
        }
    }
    
    /**
     * 批量处理待处理的事件
     */
    public void processPendingEvents() {
        try {
            var pendingInstances = businessEventService.getPendingInstances();
            
            log.info("开始批量处理待处理事件，数量: {}", pendingInstances.size());
            
            for (BusinessEventInstance instance : pendingInstances) {
                try {
                    processEvent(instance.getId());
                } catch (Exception e) {
                    log.error("处理事件实例失败: instanceId={}", instance.getId(), e);
                    // 继续处理下一个事件，不中断批量处理
                }
            }
            
            log.info("批量处理待处理事件完成");
        } catch (Exception e) {
            log.error("批量处理待处理事件失败", e);
        }
    }
}