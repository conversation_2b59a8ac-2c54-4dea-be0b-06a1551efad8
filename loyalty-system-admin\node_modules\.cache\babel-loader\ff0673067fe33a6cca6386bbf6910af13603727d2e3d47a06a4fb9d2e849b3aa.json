{"ast": null, "code": "const TabPane = () => null;\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'DeprecatedTabPane';\n}\nexport default TabPane;", "map": {"version": 3, "names": ["TabPane", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/code/private/fast-account/loyalty-system-admin/node_modules/antd/es/tabs/TabPane.js"], "sourcesContent": ["const TabPane = () => null;\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'DeprecatedTabPane';\n}\nexport default TabPane;"], "mappings": "AAAA,MAAMA,OAAO,GAAGA,CAAA,KAAM,IAAI;AAC1B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,OAAO,CAACI,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}