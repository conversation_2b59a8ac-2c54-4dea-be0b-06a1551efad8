﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 删除积分规则结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleDeleteResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 删除时间
     */
    private String deleteTime;}
