package com.fzucxl.entity.badge;

import com.fzucxl.open.base.TransactionStatus;
import com.fzucxl.open.base.badge.BadgeTransactionType;
import io.micronaut.data.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@MappedEntity("badge_transaction")
@Data
public class BadgeTransaction {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 勋章ID
     */
    private Long badgeId;
    /**
     * 账户代码
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;
    /**
     * 业务ID
     */
    private String businessId;
    /**
     * 交易ID
     */
    private String transactionId;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 交易类型
     */
    private BadgeTransactionType transactionType;
    /**
     * 交易状态
     */
    private TransactionStatus status = TransactionStatus.PENDING;
    /**
     * 交易描述
     */
    private String description;
    /**
     * 交易来源
     */
    private String source;

    /**
     * 交易过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 业务上下文（JSON格式）
     */
    private String inputData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
