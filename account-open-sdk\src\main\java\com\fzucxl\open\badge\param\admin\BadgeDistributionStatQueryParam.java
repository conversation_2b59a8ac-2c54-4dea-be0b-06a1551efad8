﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章分布统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeDistributionStatQueryParam extends Extensible {    
    /**
     * 统计维度：TYPE(按类型), LEVEL(按等级), RARITY(按稀有度)
     */
    private String dimension;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;}
