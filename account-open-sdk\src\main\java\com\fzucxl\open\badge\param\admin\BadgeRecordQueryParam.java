﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章记录参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRecordQueryParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 操作类型：AWARD/REVOKE/EXPIRE/DISPLAY/HIDE
     */
    private String operationType;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页大小
     */
    private Integer pageSize;}
