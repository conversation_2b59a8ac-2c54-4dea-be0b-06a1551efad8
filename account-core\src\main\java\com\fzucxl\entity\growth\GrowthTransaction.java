package com.fzucxl.entity.growth;

import com.fzucxl.open.base.TransactionStatus;
import com.fzucxl.open.base.growth.GrowthTransactionType;
import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.event.PrePersist;
import io.micronaut.data.annotation.event.PreUpdate;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("growth_transaction")
@Data
public class GrowthTransaction {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 事务ID，全局唯一
     */
    private String transactionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 事务类型：EARN-获得，EXPIRE-过期，ADJUST-调整，TRANSFER-转移，REVOKE-回收
     */
    private GrowthTransactionType transactionType;

    /**
     * 总成长值变动
     */
    private Long totalGrowth;

    /**
     * 消费成长值变动
     */
    private Long consumeGrowth;

    /**
     * 活跃成长值变动
     */
    private Long activityGrowth;

    /**
     * 社交成长值变动
     */
    private Long socialGrowth;

    /**
     * 任务成长值变动
     */
    private Long taskGrowth;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 事务状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消
     */
    private TransactionStatus status;

    /**
     * 事务来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则，API-接口
     */

    private String source;

    /**
     * 业务上下文（JSON格式）
     */
    private String inputData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;


    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;

        if (this.transactionId == null) {
            this.transactionId = generateTransactionId();
        }
        if (this.status == null) {
            this.status = TransactionStatus.PENDING;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();

        if (this.status == TransactionStatus.SUCCESS || this.status == TransactionStatus.FAILED) {
            this.completeTime = LocalDateTime.now();
        }
    }

    /**
     * 生成事务ID
     */
    private String generateTransactionId() {
        return "GT" + System.currentTimeMillis() + String.format("%04d",
                (int)(Math.random() * 10000));
    }

    /**
     * 标记事务成功
     */
    public void markSuccess() {
        this.status = TransactionStatus.SUCCESS;
        this.completeTime = LocalDateTime.now();
    }

    /**
     * 标记事务失败
     */
    public void markFailed(String errorMessage) {
        this.status = TransactionStatus.FAILED;
        this.completeTime = LocalDateTime.now();
    }


    /**
     * 计算各维度成长值变动
     */
    public void calculateGrowthByType() {
        if (this.businessType == null) return;

        switch (this.businessType.toUpperCase()) {
            case "PURCHASE":
            case "CONSUME":
                this.consumeGrowth = this.totalGrowth;
                break;
            case "ACTIVITY":
                this.activityGrowth = this.totalGrowth;
                break;
            case "SOCIAL":
                this.socialGrowth = this.totalGrowth;
                break;
            case "TASK":
                this.taskGrowth = this.totalGrowth;
                break;
        }
    }
}
