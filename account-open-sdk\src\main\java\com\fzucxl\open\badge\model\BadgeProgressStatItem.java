﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章进度统计项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeProgressStatItem extends Extensible {    
    /**
     * 进度范围
     */
    private String progressRange;    
    /**
     * 用户数量
     */
    private Long userCount;    
    /**
     * 占比
     */
    private java.math.BigDecimal percentage;}
