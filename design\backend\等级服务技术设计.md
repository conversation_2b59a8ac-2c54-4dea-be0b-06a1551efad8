# 等级服务技术设计文档

## 1. 服务概述

### 1.1 服务职责
等级服务负责用户等级的计算、管理和权益分配，是账户体系中用户价值分层的核心模块。

### 1.2 核心功能
- 用户等级计算
- 等级升降级处理
- 等级权益管理
- 等级配置管理
- 等级变更通知

## 2. 技术架构

### 2.1 服务架构图
```
┌─────────────────────────────────────────┐
│              等级服务                    │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐       │
│  │  等级控制器   │  │  等级服务层   │       │
│  └─────────────┘  └─────────────┘       │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  计算引擎    │  │  权益管理器   │       │
│  └─────────────┘  └─────────────┘       │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  通知服务     │  │  数据访问层  │       │
│  └─────────────┘  └─────────────┘       │
├─────────────────────────────────────────┤
│              数据存储                    │
│  ┌─────────────┐  ┌─────────────┐       │
│  │    MySQL    │  │    Redis    │       │
│  └─────────────┘  └─────────────┘       │
└─────────────────────────────────────────┘
```

### 2.2 核心类设计

#### 2.2.1 等级模块实体概览

等级系统涉及多个核心实体，用于支撑完整的等级业务流程。以下是各实体的统一描述：

| 实体名称       | 实体类              | 表名                | 主要用途            | 核心字段 |
|------------|------------------|-------------------|-----------------| -------- |
| **用户等级**   | UserLevel        | user_level        | 存储用户当前等级信息和等级进度 | userId, currentLevel, currentLevelName, levelUpTime, effectiveTime |
| **等级账户**   | LevelAccount     | level_account     | 管理等级账户配置和权益设置   | accountCode, accountName, levelCount, rankingFactors, benefitConfig |
| **等级规则**   | LevelRule        | level_rule        | 定义各等级的规则和权益配置   | accountCode, level, levelName, pointMultiplier, upgradeRule, privileges |
| **等级变更记录** | LevelRecord      | level_record      | 记录用户等级变更的历史轨迹   | userId, fromLevel, toLevel, changeType, changeReason, effectiveTime |
| **等级事务**   | LevelTransaction | level_transaction | 记录等级交易的完整生命周期   | transactionId, userId, type, status, businessId |

#### 实体关系说明

**核心关系链路：**
1. **用户等级流程**：LevelAccount → LevelRule → UserLevel
2. **等级变更链路**：UserLevel ← LevelChangeRecord
3. **权益管理链路**：LevelRule → 权益配置 → 用户权益

**数据一致性保障：**
- 用户等级基于成长值实时计算，确保等级准确性
- 等级变更记录完整追踪所有等级变化，支持审计
- 等级配置支持动态调整，实时生效
- 权益配置与等级绑定，自动继承

**表索引设计：**
- user_level: 唯一索引(user_id, account_code)，索引(current_level)
- level_account: 唯一索引(account_code)，索引(brand, status)
- level_config: 唯一索引(account_code, level)，索引(status)
- level_change_record: 复合索引(user_id, create_time)，索引(change_type)

#### 2.2.2 用户等级实体
```java
@MappedEntity("user_level")
public class UserLevel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", unique = true, nullable = false)
    private Long userId;

    @Column(name = "account_code", nullable = false)
    private String accountCode;
    
    @Column(name = "current_level", nullable = false)
    private Integer currentLevel = 1;

    @Column(name = "current_level_ name", nullable = false)
    private Integer currentLevelName = 1;
    
    @Column(name = "level_up_time")
    private LocalDateTime levelUpTime;

    @Column(name = "effective_time", nullable = false)
    private LocalDateTime effectiveTime;

    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Version
    private Integer version = 0;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
}
```

#### 2.2.3 等级账户实体
```java
@MappedEntity("level_account")
public class LevelAccount {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "account_code", unique = true, nullable = false)
    private String accountCode;
    
    @Column(name = "account_name", nullable = false)
    private String accountName;

    @Column(name = "account_description")
    private String accountDescription;
    
    @Column(name = "brand", nullable = false)
    private String brand;
    
    @Column(name = "status")
    private AccountStatus status = AccountStatus.ACTIVE;

    @Column(name = "level_count", nullable = false)
    private Integer levelCount;

    @Column(name = "ranking_factors", nullable = false)
    private String rankingFactors;

    @Column(name = "benefit_config", type = Types.JSON)
    private String benefitConfig;

    @Column(name = "notify_config", type = Types.JSON)
    private String notifyConfig;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
}
```

#### 2.2.4 等级配置实体
```java
@MappedEntity("level_rule")
public class LevelRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "account_code", nullable = false)
    private String accountCode;
    
    @Column(name = "level", unique = true, nullable = false)
    private Integer level;
    
    @Column(name = "level_name", nullable = false)
    private String levelName;
    
    @Column(name = "level_icon")
    private String levelIcon;

    @Column(name = "description")
    private String description;
    
    @Column(name = "point_multiplier", nullable = false)
    private BigDecimal pointMultiplier = BigDecimal.ONE;
    
    @Column(name = "discount_rate")
    private BigDecimal discountRate;
    
    @Column(name = "free_shipping_threshold")
    private BigDecimal freeShippingThreshold;
    
    @Column(name = "upgrade_reward_point")
    private Long upgradeRewardPoint = 0L;

    @Column(name = "upgrade_rule")
    private String upgradeRule;

    @Column(name = "recalculate_rule")
    private String recalculateRule;
    
    @Column(name = "privileges", columnDefinition = "JSON")
    private String privileges;
    
    @Column(name = "status")
    private ConfigStatus status = ConfigStatus.ACTIVE;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
}
```

#### 2.2.5 等级变更记录实体
```java
@MappedEntity("level_record")
public class LevelRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "account_code", nullable = false)
    private String accountCode;

    @Column(name = "transaction_id", nullable = false)
    private String transactionId;

    @Column(name = "business_id")
    private String businessId;
    
    @Column(name = "from_level")
    private Integer fromLevel;
    
    @Column(name = "to_level", nullable = false)
    private Integer toLevel;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "change_type", nullable = false)
    private LevelChangeType changeType;
    
    @Column(name = "change_reason")
    private String changeReason;

    @Column(name = "effective_time", nullable = false)
    private LocalDateTime effectiveTime;

    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @CreationTimestamp
    private LocalDateTime createTime;

    @Column(name = "extra_data", columnDefinition = "JSON")
    private String extraData;
}
```

#### 2.2.6 等级事务实体
```java
@MappedEntity("level_transaction")
public class LevelTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "transaction_id", unique = true, nullable = false)
    private String transactionId;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "account_code", nullable = false)
    private String accountCode;

    @Column(name = "business_id")
    private String businessId;

    @Column(name = "business_type", nullable = false)
    private String businessType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private LevelTransactionType transactionType;
    
    @Column(name = "from_level")
    private Integer fromLevel;
    
    @Column(name = "to_level")
    private Integer toLevel;
    
    @Column(name = "growth_change")
    private Long growthChange;
    
    @Column(name = "reward_point")
    private Long rewardPoint;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TransactionStatus status = TransactionStatus.PENDING;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "effective_time")
    private LocalDateTime effectiveTime;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Column(name = "processed_time")
    private LocalDateTime processedTime;
    
    @Column(name = "error_message")
    private String errorMessage;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;

    @Column(name = "input_data", columnDefinition = "JSON")
    private String inputData;
}
```

**等级事务类型枚举：**
```java
public enum LevelTransactionType {
    LEVEL_UPGRADE("等级升级"),
    LEVEL_DOWNGRADE("等级降级"),
    LEVEL_RESET("等级重置"),
    LEVEL_MANUAL_ADJUST("等级手动调整"),
    LEVEL_BATCH_UPDATE("等级批量更新");
    
    private final String description;
    
    LevelTransactionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

**事务状态枚举：**
```java
public enum TransactionStatus {
    PENDING("待处理"),
    PROCESSING("处理中"),
    SUCCESS("成功"),
    FAILED("失败"),
    CANCELLED("已取消"),
    EXPIRED("已过期");
    
    private final String description;
    
    TransactionStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

## 3. 等级事务管理

### 3.1 等级事务服务
```java
@Service
@Transactional
public class LevelTransactionService {
    
    @Autowired
    private LevelTransactionRepository levelTransactionRepository;
    
    @Autowired
    private LevelService levelService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 创建等级事务
     */
    public LevelTransaction createLevelTransaction(CreateLevelTransactionRequest request) {
        // 1. 生成事务ID
        String transactionId = generateTransactionId();
        
        // 2. 创建事务记录
        LevelTransaction transaction = new LevelTransaction();
        transaction.setTransactionId(transactionId);
        transaction.setUserId(request.getUserId());
        transaction.setAccountCode(request.getAccountCode());
        transaction.setBusinessId(request.getBusinessId());
        transaction.setBusinessType(request.getBusinessType());
        transaction.setTransactionType(request.getTransactionType());
        transaction.setGrowthChange(request.getGrowthChange());
        transaction.setDescription(request.getDescription());
        transaction.setEffectiveTime(request.getEffectiveTime());
        transaction.setExpireTime(request.getExpireTime());
        transaction.setStatus(TransactionStatus.PENDING);
        transaction.setExtraData(request.getExtraData());
        
        LevelTransaction savedTransaction = levelTransactionRepository.save(transaction);
        
        // 3. 异步处理事务
        processLevelTransactionAsync(savedTransaction.getId());
        
        return savedTransaction;
    }
    
    /**
     * 处理等级事务
     */
    @Async("levelTransactionExecutor")
    public void processLevelTransactionAsync(Long transactionId) {
        try {
            processLevelTransaction(transactionId);
        } catch (Exception e) {
            log.error("处理等级事务失败: transactionId={}", transactionId, e);
            handleTransactionError(transactionId, e.getMessage());
        }
    }
    
    /**
     * 同步处理等级事务
     */
    @Transactional
    public void processLevelTransaction(Long transactionId) {
        LevelTransaction transaction = levelTransactionRepository.findById(transactionId)
            .orElseThrow(() -> new BusinessException("等级事务不存在: " + transactionId));
        
        // 检查事务状态
        if (transaction.getStatus() != TransactionStatus.PENDING) {
            log.warn("等级事务状态不正确: transactionId={}, status={}", transactionId, transaction.getStatus());
            return;
        }
        
        // 检查是否过期
        if (transaction.getExpireTime() != null && LocalDateTime.now().isAfter(transaction.getExpireTime())) {
            transaction.setStatus(TransactionStatus.EXPIRED);
            levelTransactionRepository.save(transaction);
            return;
        }
        
        try {
            // 更新状态为处理中
            transaction.setStatus(TransactionStatus.PROCESSING);
            transaction.setProcessedTime(LocalDateTime.now());
            levelTransactionRepository.save(transaction);
            
            // 执行等级事务处理
            processTransactionByType(transaction);
            
            // 更新状态为成功
            transaction.setStatus(TransactionStatus.SUCCESS);
            levelTransactionRepository.save(transaction);
            
            // 发布事务完成事件
            eventPublisher.publishEvent(new LevelTransactionCompletedEvent(transaction));
            
        } catch (Exception e) {
            log.error("处理等级事务失败: transactionId={}", transactionId, e);
            handleTransactionError(transactionId, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 根据事务类型处理事务
     */
    private void processTransactionByType(LevelTransaction transaction) {
        switch (transaction.getTransactionType()) {
            case LEVEL_UPGRADE:
                processLevelUpgrade(transaction);
                break;
            case LEVEL_DOWNGRADE:
                processLevelDowngrade(transaction);
                break;
            case LEVEL_RESET:
                processLevelReset(transaction);
                break;
            case LEVEL_MANUAL_ADJUST:
                processLevelManualAdjust(transaction);
                break;
            case LEVEL_BATCH_UPDATE:
                processLevelBatchUpdate(transaction);
                break;
            default:
                throw new BusinessException("不支持的事务类型: " + transaction.getTransactionType());
        }
    }
    
    /**
     * 处理等级升级事务
     */
    private void processLevelUpgrade(LevelTransaction transaction) {
        Long userId = transaction.getUserId();
        
        // 获取当前用户等级
        UserLevel currentLevel = levelService.getUserLevel(userId);
        
        // 计算新等级
        LevelCalculationResult calculationResult = levelService.calculateLevel(userId);
        
        // 检查是否确实需要升级
        if (calculationResult.getCurrentLevel() <= currentLevel.getCurrentLevel()) {
            throw new BusinessException("用户等级不满足升级条件");
        }
        
        // 更新用户等级
        levelService.updateUserLevel(userId, calculationResult);
        
        // 记录事务结果
        transaction.setFromLevel(currentLevel.getCurrentLevel());
        transaction.setToLevel(calculationResult.getCurrentLevel());
        
        // 处理升级奖励
        if (transaction.getRewardPoint() != null && transaction.getRewardPoint() > 0) {
            // 发放升级奖励积分
            pointService.earnPoint(EarnPointRequest.builder()
                .userId(userId)
                .point(transaction.getRewardPoint())
                .businessType("LEVEL_UPGRADE_REWARD")
                .businessId(transaction.getTransactionId())
                .description("等级升级奖励")
                .build());
        }
    }
    
    /**
     * 生成事务ID
     */
    private String generateTransactionId() {
        return "LT" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(6);
    }
}
```

### 3.2 等级事务仓储
```java
@Repository
public interface LevelTransactionRepository extends JpaRepository<LevelTransaction, Long> {
    
    Optional<LevelTransaction> findByTransactionId(String transactionId);
    
    Page<LevelTransaction> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);
    
    List<LevelTransaction> findByStatusAndCreateTimeBefore(TransactionStatus status, LocalDateTime createTime);
    
    List<LevelTransaction> findByStatusAndRetryCountLessThan(TransactionStatus status, Integer maxRetryCount);
    
    @Query("SELECT COUNT(t) FROM LevelTransaction t WHERE t.userId = :userId AND t.status = :status AND t.createTime >= :startTime")
    Long countByUserIdAndStatusAndCreateTimeAfter(@Param("userId") Long userId, 
                                                  @Param("status") TransactionStatus status, 
                                                  @Param("startTime") LocalDateTime startTime);
}
```

### 3.3 等级事务事件
```java
/**
 * 等级事务完成事件
 */
public class LevelTransactionCompletedEvent extends ApplicationEvent {
    
    private final LevelTransaction transaction;
    
    public LevelTransactionCompletedEvent(LevelTransaction transaction) {
        super(transaction);
        this.transaction = transaction;
    }
    
    public LevelTransaction getTransaction() {
        return transaction;
    }
}

/**
 * 等级事务事件监听器
 */
@Component
@EventListener
public class LevelTransactionEventListener {
    
    @Autowired
    private LevelNotificationService notificationService;
    
    @Autowired
    private LevelMetrics levelMetrics;
    
    /**
     * 处理等级事务完成事件
     */
    @EventListener
    @Async("levelEventExecutor")
    public void handleLevelTransactionCompleted(LevelTransactionCompletedEvent event) {
        LevelTransaction transaction = event.getTransaction();
        
        try {
            // 发送通知
            if (transaction.getTransactionType() == LevelTransactionType.LEVEL_UPGRADE) {
                notificationService.sendLevelUpgradeNotification(
                    transaction.getUserId(), 
                    transaction.getFromLevel(), 
                    transaction.getToLevel()
                );
                
                // 记录升级指标
                levelMetrics.recordLevelUpgrade(transaction.getFromLevel(), transaction.getToLevel());
                
            } else if (transaction.getTransactionType() == LevelTransactionType.LEVEL_DOWNGRADE) {
                notificationService.sendLevelDowngradeNotification(
                    transaction.getUserId(), 
                    transaction.getFromLevel(), 
                    transaction.getToLevel()
                );
                
                // 记录降级指标
                levelMetrics.recordLevelDowngrade(transaction.getFromLevel(), transaction.getToLevel());
            }
            
        } catch (Exception e) {
            log.error("处理等级事务完成事件失败: transactionId={}", transaction.getTransactionId(), e);
        }
    }
}
```

## 4. 核心业务逻辑

### 4.1 等级计算引擎
```java
@Component
public class LevelCalculationEngine {
    
    @Autowired
    private LevelRuleRepository levelConfigRepository;
    
    @Autowired
    private GrowthService growthService;
    
    public LevelCalculationResult calculateLevel(Long userId) {
        // 1. 获取用户总成长值
        Long totalGrowth = growthService.getTotalGrowth(userId);
        
        // 2. 获取等级配置
        List<LevelRule> levelConfigs = getLevelRules();
        
        // 3. 计算当前等级
        LevelCalculationResult result = new LevelCalculationResult();
        result.setUserId(userId);
        result.setTotalGrowth(totalGrowth);
        
        // 4. 遍历等级配置找到匹配的等级
        for (int i = levelConfigs.size() - 1; i >= 0; i--) {
            LevelRule config = levelConfigs.get(i);
            if (totalGrowth >= config.getRequiredGrowth()) {
                result.setCurrentLevel(config.getLevel());
                result.setCurrentLevelGrowth(config.getRequiredGrowth());
                result.setLevelName(config.getLevelName());
                
                // 计算下一等级信息
                if (i < levelConfigs.size() - 1) {
                    LevelRule nextConfig = levelConfigs.get(i + 1);
                    result.setNextLevel(nextConfig.getLevel());
                    result.setNextLevelGrowth(nextConfig.getRequiredGrowth());
                    result.setNextLevelName(nextConfig.getLevelName());
                    
                    // 计算升级进度
                    long currentLevelGrowth = totalGrowth - config.getRequiredGrowth();
                    long requiredGrowth = nextConfig.getRequiredGrowth() - config.getRequiredGrowth();
                    result.setProgress((double) currentLevelGrowth / requiredGrowth);
                } else {
                    // 已达到最高等级
                    result.setNextLevel(config.getLevel());
                    result.setNextLevelGrowth(config.getRequiredGrowth());
                    result.setProgress(1.0);
                }
                break;
            }
        }
        
        return result;
    }
    
    private List<LevelRule> getLevelRules() {
        return levelConfigRepository.findByStatusOrderByLevel(ConfigStatus.ACTIVE);
    }
}
```

### 3.2 等级服务核心逻辑
```java
@Service
@Transactional
public class LevelService {
    
    @Autowired
    private UserLevelRepository userLevelRepository;
    
    @Autowired
    private LevelChangeRecordRepository changeRecordRepository;
    
    @Autowired
    private LevelCalculationEngine calculationEngine;
    
    @Autowired
    private LevelNotificationService notificationService;
    
    @Autowired
    private PointService pointService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public UserLevel getUserLevel(Long userId) {
        // 1. 先从缓存获取
        String cacheKey = "level:user:" + userId;
        UserLevel cachedLevel = (UserLevel) redisTemplate.opsForValue().get(cacheKey);
        if (cachedLevel != null) {
            return cachedLevel;
        }
        
        // 2. 从数据库获取
        UserLevel userLevel = userLevelRepository.findByUserId(userId);
        if (userLevel == null) {
            userLevel = createInitialUserLevel(userId);
        }
        
        // 3. 缓存结果
        redisTemplate.opsForValue().set(cacheKey, userLevel, Duration.ofMinutes(30));
        
        return userLevel;
    }
    
    public void checkAndUpdateLevel(Long userId) {
        // 1. 获取当前用户等级
        UserLevel currentUserLevel = getUserLevel(userId);
        
        // 2. 计算最新等级
        LevelCalculationResult calculationResult = calculationEngine.calculateLevel(userId);
        
        // 3. 检查是否需要升级或降级
        if (!calculationResult.getCurrentLevel().equals(currentUserLevel.getCurrentLevel())) {
            updateUserLevel(userId, currentUserLevel, calculationResult);
        } else {
            // 更新成长值信息
            updateGrowthInfo(currentUserLevel, calculationResult);
        }
    }
    
    private void updateUserLevel(Long userId, UserLevel currentLevel, LevelCalculationResult newLevel) {
        // 1. 确定变更类型
        LevelChangeType changeType = newLevel.getCurrentLevel() > currentLevel.getCurrentLevel() 
            ? LevelChangeType.UPGRADE : LevelChangeType.DOWNGRADE;
        
        // 2. 更新用户等级
        currentLevel.setCurrentLevel(newLevel.getCurrentLevel());
        currentLevel.setCurrentGrowth(newLevel.getCurrentLevelGrowth());
        currentLevel.setNextLevelGrowth(newLevel.getNextLevelGrowth());
        currentLevel.setTotalGrowth(newLevel.getTotalGrowth());
        
        if (changeType == LevelChangeType.UPGRADE) {
            currentLevel.setLevelUpTime(LocalDateTime.now());
        }
        
        userLevelRepository.save(currentLevel);
        
        // 3. 记录等级变更
        LevelChangeRecord changeRecord = new LevelChangeRecord();
        changeRecord.setUserId(userId);
        changeRecord.setFromLevel(currentLevel.getCurrentLevel());
        changeRecord.setToLevel(newLevel.getCurrentLevel());
        changeRecord.setChangeType(changeType);
        changeRecord.setGrowthValue(newLevel.getTotalGrowth());
        
        // 4. 处理升级奖励
        if (changeType == LevelChangeType.UPGRADE) {
            LevelRule levelConfig = getLevelRule(newLevel.getCurrentLevel());
            if (levelConfig.getUpgradeRewardPoint() > 0) {
                // 发放升级奖励积分
                pointService.earnPoint(EarnPointRequest.builder()
                    .userId(userId)
                    .point(levelConfig.getUpgradeRewardPoint())
                    .businessType("LEVEL_UPGRADE")
                    .businessId(String.valueOf(newLevel.getCurrentLevel()))
                    .description("等级升级奖励")
                    .build());
                
                changeRecord.setRewardPoint(levelConfig.getUpgradeRewardPoint());
            }
        }
        
        changeRecordRepository.save(changeRecord);
        
        // 5. 清除缓存
        clearLevelCache(userId);
        
        // 6. 发送通知
        notificationService.sendLevelChangeNotification(userId, changeType, 
            currentLevel.getCurrentLevel(), newLevel.getCurrentLevel());
        
        // 7. 发布事件
        publishLevelChangeEvent(userId, changeType, currentLevel.getCurrentLevel(), newLevel.getCurrentLevel());
    }
    
    private UserLevel createInitialUserLevel(Long userId) {
        UserLevel userLevel = new UserLevel();
        userLevel.setUserId(userId);
        userLevel.setCurrentLevel(1);
        userLevel.setCurrentGrowth(0L);
        userLevel.setNextLevelGrowth(getLevelRule(2).getRequiredGrowth());
        userLevel.setTotalGrowth(0L);
        
        return userLevelRepository.save(userLevel);
    }
}
```

### 3.3 等级权益管理
```java
@Service
public class LevelPrivilegeService {
    
    @Autowired
    private LevelRuleRepository levelConfigRepository;
    
    @Autowired
    private LevelService levelService;
    
    public List<LevelPrivilege> getUserPrivileges(Long userId) {
        UserLevel userLevel = levelService.getUserLevel(userId);
        LevelRule levelConfig = getLevelRule(userLevel.getCurrentLevel());
        
        return parsePrivileges(levelConfig.getPrivileges());
    }
    
    public boolean hasPrivilege(Long userId, String privilegeType) {
        List<LevelPrivilege> privileges = getUserPrivileges(userId);
        return privileges.stream()
            .anyMatch(privilege -> privilege.getType().equals(privilegeType));
    }
    
    public BigDecimal getPointMultiplier(Long userId) {
        UserLevel userLevel = levelService.getUserLevel(userId);
        LevelRule levelConfig = getLevelRule(userLevel.getCurrentLevel());
        return levelConfig.getPointMultiplier();
    }
    
    public BigDecimal getDiscountRate(Long userId) {
        UserLevel userLevel = levelService.getUserLevel(userId);
        LevelRule levelConfig = getLevelRule(userLevel.getCurrentLevel());
        return levelConfig.getDiscountRate();
    }
    
    public BigDecimal getFreeShippingThreshold(Long userId) {
        UserLevel userLevel = levelService.getUserLevel(userId);
        LevelRule levelConfig = getLevelRule(userLevel.getCurrentLevel());
        return levelConfig.getFreeShippingThreshold();
    }
    
    private List<LevelPrivilege> parsePrivileges(String privilegesJson) {
        if (StringUtils.isBlank(privilegesJson)) {
            return Collections.emptyList();
        }
        
        try {
            return JSON.parseArray(privilegesJson, LevelPrivilege.class);
        } catch (Exception e) {
            log.error("解析等级权益失败: {}", privilegesJson, e);
            return Collections.emptyList();
        }
    }
}
```

## 4. 等级配置管理

### 4.1 配置管理服务
```java
@Service
public class LevelRuleService {
    
    @Autowired
    private LevelRuleRepository levelConfigRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CONFIG_CACHE_KEY = "level:config:all";
    
    public List<LevelRule> getAllLevelRules() {
        // 1. 先从缓存获取
        List<LevelRule> cachedConfigs = (List<LevelRule>) redisTemplate.opsForValue().get(CONFIG_CACHE_KEY);
        if (cachedConfigs != null) {
            return cachedConfigs;
        }
        
        // 2. 从数据库获取
        List<LevelRule> configs = levelConfigRepository.findByStatusOrderByLevel(ConfigStatus.ACTIVE);
        
        // 3. 缓存结果
        redisTemplate.opsForValue().set(CONFIG_CACHE_KEY, configs, Duration.ofHours(24));
        
        return configs;
    }
    
    public LevelRule getLevelRule(Integer level) {
        return getAllLevelRules().stream()
            .filter(config -> config.getLevel().equals(level))
            .findFirst()
            .orElseThrow(() -> new BusinessException("等级配置不存在: " + level));
    }
    
    public LevelRule createLevelRule(CreateLevelRuleRequest request) {
        // 1. 参数校验
        validateLevelRule(request);
        
        // 2. 检查等级是否已存在
        if (levelConfigRepository.existsByLevel(request.getLevel())) {
            throw new BusinessException("等级已存在: " + request.getLevel());
        }
        
        // 3. 创建配置
        LevelRule config = new LevelRule();
        BeanUtils.copyProperties(request, config);
        config.setStatus(ConfigStatus.ACTIVE);
        
        LevelRule savedConfig = levelConfigRepository.save(config);
        
        // 4. 清除缓存
        clearConfigCache();
        
        return savedConfig;
    }
    
    public LevelRule updateLevelRule(Long configId, UpdateLevelRuleRequest request) {
        // 1. 获取现有配置
        LevelRule config = levelConfigRepository.findById(configId)
            .orElseThrow(() -> new BusinessException("等级配置不存在"));
        
        // 2. 更新配置
        if (request.getLevelName() != null) {
            config.setLevelName(request.getLevelName());
        }
        if (request.getRequiredGrowth() != null) {
            config.setRequiredGrowth(request.getRequiredGrowth());
        }
        if (request.getPointMultiplier() != null) {
            config.setPointMultiplier(request.getPointMultiplier());
        }
        if (request.getDiscountRate() != null) {
            config.setDiscountRate(request.getDiscountRate());
        }
        if (request.getUpgradeRewardPoint() != null) {
            config.setUpgradeRewardPoint(request.getUpgradeRewardPoint());
        }
        if (request.getPrivileges() != null) {
            config.setPrivileges(request.getPrivileges());
        }
        
        LevelRule savedConfig = levelConfigRepository.save(config);
        
        // 3. 清除缓存
        clearConfigCache();
        
        return savedConfig;
    }
    
    private void clearConfigCache() {
        redisTemplate.delete(CONFIG_CACHE_KEY);
    }
}
```

## 5. 等级通知服务

### 5.1 通知服务设计
```java
@Service
public class LevelNotificationService {
    
    @Autowired
    private MessageService messageService;
    
    @Autowired
    private LevelRuleService levelConfigService;
    
    public void sendLevelChangeNotification(Long userId, LevelChangeType changeType, 
                                          Integer fromLevel, Integer toLevel) {
        try {
            if (changeType == LevelChangeType.UPGRADE) {
                sendUpgradeNotification(userId, fromLevel, toLevel);
            } else {
                sendDowngradeNotification(userId, fromLevel, toLevel);
            }
        } catch (Exception e) {
            log.error("发送等级变更通知失败: userId={}, changeType={}, fromLevel={}, toLevel={}", 
                userId, changeType, fromLevel, toLevel, e);
        }
    }
    
    private void sendUpgradeNotification(Long userId, Integer fromLevel, Integer toLevel) {
        LevelRule newLevelRule = levelConfigService.getLevelRule(toLevel);
        
        NotificationMessage message = NotificationMessage.builder()
            .userId(userId)
            .type(NotificationType.LEVEL_UPGRADE)
            .title("恭喜您等级提升！")
            .content(String.format("恭喜您从%s等级提升至%s等级！", 
                getLevelName(fromLevel), newLevelRule.getLevelName()))
            .data(Map.of(
                "fromLevel", fromLevel,
                "toLevel", toLevel,
                "levelName", newLevelRule.getLevelName(),
                "rewardPoint", newLevelRule.getUpgradeRewardPoint()
            ))
            .build();
        
        messageService.sendNotification(message);
    }
    
    private void sendDowngradeNotification(Long userId, Integer fromLevel, Integer toLevel) {
        LevelRule newLevelRule = levelConfigService.getLevelRule(toLevel);
        
        NotificationMessage message = NotificationMessage.builder()
            .userId(userId)
            .type(NotificationType.LEVEL_DOWNGRADE)
            .title("等级变更通知")
            .content(String.format("您的等级已从%s调整为%s", 
                getLevelName(fromLevel), newLevelRule.getLevelName()))
            .data(Map.of(
                "fromLevel", fromLevel,
                "toLevel", toLevel,
                "levelName", newLevelRule.getLevelName()
            ))
            .build();
        
        messageService.sendNotification(message);
    }
}
```

## 6. 缓存策略

### 6.1 缓存设计
```java
@Service
public class LevelCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String USER_LEVEL_KEY = "level:user:";
    private static final String LEVEL_CONFIG_KEY = "level:config:";
    private static final String USER_PRIVILEGES_KEY = "level:privileges:";
    
    // 用户等级缓存
    public void cacheUserLevel(Long userId, UserLevel userLevel) {
        String key = USER_LEVEL_KEY + userId;
        redisTemplate.opsForValue().set(key, userLevel, Duration.ofMinutes(30));
    }
    
    public UserLevel getUserLevelFromCache(Long userId) {
        String key = USER_LEVEL_KEY + userId;
        return (UserLevel) redisTemplate.opsForValue().get(key);
    }
    
    // 等级配置缓存
    public void cacheLevelRules(List<LevelRule> configs) {
        String key = LEVEL_CONFIG_KEY + "all";
        redisTemplate.opsForValue().set(key, configs, Duration.ofHours(24));
    }
    
    public List<LevelRule> getLevelRulesFromCache() {
        String key = LEVEL_CONFIG_KEY + "all";
        return (List<LevelRule>) redisTemplate.opsForValue().get(key);
    }
    
    // 用户权益缓存
    public void cacheUserPrivileges(Long userId, List<LevelPrivilege> privileges) {
        String key = USER_PRIVILEGES_KEY + userId;
        redisTemplate.opsForValue().set(key, privileges, Duration.ofMinutes(30));
    }
    
    public List<LevelPrivilege> getUserPrivilegesFromCache(Long userId) {
        String key = USER_PRIVILEGES_KEY + userId;
        return (List<LevelPrivilege>) redisTemplate.opsForValue().get(key);
    }
    
    // 清除用户相关缓存
    public void clearUserCache(Long userId) {
        Set<String> keys = redisTemplate.keys(USER_LEVEL_KEY + userId);
        keys.addAll(redisTemplate.keys(USER_PRIVILEGES_KEY + userId));
        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }
}
```

## 7. 性能优化

### 7.1 批量等级计算
```java
@Service
public class LevelBatchService {
    
    @Autowired
    private LevelService levelService;
    
    @Autowired
    private GrowthService growthService;
    
    @Async("levelTaskExecutor")
    public CompletableFuture<Void> batchUpdateLevels(List<Long> userIds) {
        for (Long userId : userIds) {
            try {
                levelService.checkAndUpdateLevel(userId);
            } catch (Exception e) {
                log.error("批量更新用户等级失败: userId={}", userId, e);
            }
        }
        return CompletableFuture.completedFuture(null);
    }
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void scheduledLevelUpdate() {
        log.info("开始执行定时等级更新任务");
        
        // 获取需要更新等级的用户列表
        List<Long> userIds = getUsersNeedLevelUpdate();
        
        // 分批处理
        int batchSize = 1000;
        for (int i = 0; i < userIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, userIds.size());
            List<Long> batchUserIds = userIds.subList(i, endIndex);
            
            batchUpdateLevels(batchUserIds);
        }
        
        log.info("定时等级更新任务完成，处理用户数: {}", userIds.size());
    }
    
    private List<Long> getUsersNeedLevelUpdate() {
        // 获取最近有成长值变化的用户
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        return growthService.getUsersWithGrowthChangeSince(yesterday);
    }
}
```

### 7.2 数据库优化
```sql
-- 用户等级表索引
CREATE UNIQUE INDEX uk_user_level_user_id ON user_level(user_id);
CREATE INDEX idx_user_level_current_level ON user_level(current_level);
CREATE INDEX idx_user_level_total_growth ON user_level(total_growth);
CREATE INDEX idx_user_level_update_time ON user_level(update_time);

-- 等级配置表索引
CREATE UNIQUE INDEX uk_level_config_level ON level_config(level);
CREATE INDEX idx_level_config_status ON level_config(status);

-- 等级变更记录表索引
CREATE INDEX idx_level_change_user_id ON level_change_record(user_id);
CREATE INDEX idx_level_change_create_time ON level_change_record(create_time);
CREATE INDEX idx_level_change_type ON level_change_record(change_type);
```

## 8. 监控和日志

### 8.1 业务监控
```java
@Component
public class LevelMetrics {
    
    private final Counter levelUpCounter;
    private final Counter levelDownCounter;
    private final Timer levelCalculationTimer;
    private final Gauge levelDistributionGauge;
    
    public LevelMetrics(MeterRegistry meterRegistry) {
        this.levelUpCounter = Counter.builder("level.upgrade.total")
            .description("等级升级总数")
            .register(meterRegistry);
            
        this.levelDownCounter = Counter.builder("level.downgrade.total")
            .description("等级降级总数")
            .register(meterRegistry);
            
        this.levelCalculationTimer = Timer.builder("level.calculation.duration")
            .description("等级计算耗时")
            .register(meterRegistry);
    }
    
    public void recordLevelUpgrade(Integer fromLevel, Integer toLevel) {
        levelUpCounter.increment(Tags.of(
            "from_level", String.valueOf(fromLevel),
            "to_level", String.valueOf(toLevel)
        ));
    }
    
    public void recordLevelDowngrade(Integer fromLevel, Integer toLevel) {
        levelDownCounter.increment(Tags.of(
            "from_level", String.valueOf(fromLevel),
            "to_level", String.valueOf(toLevel)
        ));
    }
    
    public void recordCalculationTime(Duration duration) {
        levelCalculationTimer.record(duration);
    }
}
```

### 8.2 业务日志
```java
@Slf4j
@Component
public class LevelLogger {
    
    public void logLevelChange(Long userId, LevelChangeType changeType, 
                              Integer fromLevel, Integer toLevel, Long growthValue) {
        log.info("用户等级变更: userId={}, changeType={}, fromLevel={}, toLevel={}, growthValue={}", 
            userId, changeType, fromLevel, toLevel, growthValue);
    }
    
    public void logLevelCalculation(Long userId, LevelCalculationResult result) {
        log.info("等级计算结果: userId={}, currentLevel={}, totalGrowth={}, progress={}", 
            userId, result.getCurrentLevel(), result.getTotalGrowth(), result.getProgress());
    }
    
    public void logPrivilegeAccess(Long userId, String privilegeType, boolean hasPrivilege) {
        log.info("权益访问: userId={}, privilegeType={}, hasPrivilege={}", 
            userId, privilegeType, hasPrivilege);
    }
}
```

## 9. 测试用例

### 9.1 单元测试
```java
@ExtendWith(MockitoExtension.class)
class LevelServiceTest {
    
    @Mock
    private UserLevelRepository userLevelRepository;
    
    @Mock
    private LevelCalculationEngine calculationEngine;
    
    @Mock
    private LevelNotificationService notificationService;
    
    @InjectMocks
    private LevelService levelService;
    
    @Test
    void checkAndUpdateLevel_Upgrade_Success() {
        // Given
        Long userId = 1L;
        
        UserLevel currentLevel = new UserLevel();
        currentLevel.setUserId(userId);
        currentLevel.setCurrentLevel(1);
        currentLevel.setTotalGrowth(500L);
        
        LevelCalculationResult newLevel = new LevelCalculationResult();
        newLevel.setUserId(userId);
        newLevel.setCurrentLevel(2);
        newLevel.setTotalGrowth(1000L);
        
        when(userLevelRepository.findByUserId(userId)).thenReturn(currentLevel);
        when(calculationEngine.calculateLevel(userId)).thenReturn(newLevel);
        
        // When
        levelService.checkAndUpdateLevel(userId);
        
        // Then
        verify(userLevelRepository).save(any(UserLevel.class));
        verify(notificationService).sendLevelChangeNotification(
            eq(userId), eq(LevelChangeType.UPGRADE), eq(1), eq(2));
    }
    
    @Test
    void getUserLevel_NotExists_CreateNew() {
        // Given
        Long userId = 1L;
        when(userLevelRepository.findByUserId(userId)).thenReturn(null);
        
        UserLevel newLevel = new UserLevel();
        newLevel.setUserId(userId);
        newLevel.setCurrentLevel(1);
        
        when(userLevelRepository.save(any(UserLevel.class))).thenReturn(newLevel);
        
        // When
        UserLevel result = levelService.getUserLevel(userId);
        
        // Then
        assertNotNull(result);
        assertEquals(userId, result.getUserId());
        assertEquals(Integer.valueOf(1), result.getCurrentLevel());
        verify(userLevelRepository).save(any(UserLevel.class));
    }
}
```

## 10. 总结

等级服务作为账户体系的重要组成部分，通过完善的技术设计实现了：

1. **智能等级计算**：基于成长值的自动等级计算和升降级处理
2. **灵活权益管理**：可配置的等级权益体系，支持多样化的用户激励
3. **高性能处理**：通过缓存、批量处理、异步操作保证系统性能
4. **完善的通知机制**：及时的等级变更通知，提升用户体验
5. **全面的监控体系**：详细的业务监控和日志记录，便于运维管理

该设计方案能够支撑复杂的等级体系需求，为用户提供差异化的服务体验，促进用户活跃度和忠诚度的提升。
