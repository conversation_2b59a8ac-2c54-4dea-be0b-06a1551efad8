﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 计算成长值结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthCalculateResult extends Extensible {    
    /**
     * 计算得出的成长值
     */
    private Long growthValue;    
    /**
     * 适用的规则列表
     */
    private java.util.List<GrowthRuleItem> ruleList;}
