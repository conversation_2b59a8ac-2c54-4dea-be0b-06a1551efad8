# 勋章服务技术设计文档

## 1. 服务概述

### 1.1 服务职责
勋章服务负责用户勋章的配置管理、成就检测、发放回收和展示管理，是账户体系中用户荣誉激励的核心模块。

### 1.2 核心功能
- 勋章配置管理
- 成就条件检测
- 勋章发放和回收
- 勋章展示管理
- 勋章进度跟踪

## 2. 技术架构

### 2.1 服务架构图
```
┌─────────────────────────────────────────┐
│             勋章服务                     │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐       │
│  │ 勋章控制器   │  │ 勋章服务层   │       │
│  └─────────────┘  └─────────────┘       │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  检测引擎   │  │  发放管理器  │       │
│  └─────────────┘  └─────────────┘       │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  进度跟踪   │  │  数据访问层  │       │
│  └─────────────┘  └─────────────┘       │
├─────────────────────────────────────────┤
│              数据存储                    │
│  ┌─────────────┐  ┌─────────────┐       │
│  │    MySQL    │  │    Redis    │       │
│  └─────────────┘  └─────────────┘       │
└─────────────────────────────────────────┘
```

### 2.2 核心类设计

#### 2.2.1 勋章账户实体

勋章系统涉及多个核心实体，用于支撑完整的勋章业务流程。以下是各实体的统一描述：

| 实体名称       | 实体类 | 表名            | 主要用途 | 核心字段 |
|------------| ------ |---------------| -------- | -------- |
| **勋章账户**   | BadgeAccount | badge_account | 存储勋章账户的基本配置信息和统计数据，管理勋章账户的生命周期 | accountCode, accountName, badgeType, badgeCategory, status, basicConfig |
| **用户勋章**   | UserBadge | user_badge | 记录用户获得的勋章信息，包括获得时间、来源、展示状态等 | userId, badgeId, obtainTime, source, status, isDisplayed |
| **勋章记录**   | BadgeRecord | badge_record | 记录所有勋章相关操作的详细信息，包括发放、撤销、过期等操作记录 | userId, badgeId, operationType, operationTime, operatorId, reason |
| **勋章进度**   | BadgeProgress | badge_progress | 跟踪用户获得勋章的进度信息，用于展示进度和自动发放 | userId, badgeId, currentProgress, targetProgress, progressData |
| **勋章事务**   | BadgeTransaction | badge_transaction | 记录所有勋章相关事务的详细信息，确保勋章操作的可追溯性和一致性 | transactionId, userId, badgeId, transactionType, status, processedTime |

##### 实体关系说明

**核心关系链路：**
1. **勋章配置流程**：BadgeAccount → 勋章配置 → 获得条件
2. **勋章获得链路**：BadgeAccount → UserBadge → BadgeRecord
3. **进度跟踪链路**：BadgeAccount → BadgeProgress → UserBadge
4. **事务管理链路**：BadgeTransaction → UserBadge/BadgeRecord

**数据一致性保障：**
- 勋章发放基于条件检测，确保获得的合理性
- 勋章记录完整追踪所有勋章操作，支持审计
- 勋章进度实时更新，支持进度展示和自动发放
- 勋章事务确保操作的原子性和可靠性

**表索引设计：**
- badge_account: 唯一索引(account_code)，索引(badge_type, status)，索引(badge_category, status)
- user_badge: 复合索引(user_id, status)，索引(badge_id)，索引(obtain_time)，索引(expire_time, status)
- badge_record: 复合索引(user_id, operation_time)，索引(badge_id)，索引(operation_type)，索引(transaction_id)
- badge_progress: 复合索引(user_id, badge_id)，索引(current_progress, target_progress)
- badge_transaction: 唯一索引(transaction_id)，复合索引(user_id, create_time)，索引(status)

##### 业务流程说明

**勋章发放流程：**
1. 事件触发 → 勋章检测引擎 → 条件评估
2. 条件满足 → 创建勋章事务 → 发放勋章
3. 更新用户勋章 → 记录操作日志 → 发放奖励
4. 清理进度记录 → 发布事件通知

**进度跟踪流程：**
1. 用户行为 → 进度计算 → 更新进度记录
2. 进度达标 → 自动触发发放 → 完成勋章获得

**事务管理流程：**
1. 创建事务记录 → 异步处理事务 → 执行业务逻辑
2. 更新事务状态 → 错误重试机制 → 完成事务处理
3. 事务完成 → 更新相关记录 → 发布事件通知

**字段说明：**

**BadgeAccount字段说明：**
- **accountCode**: 勋章账户代码，全局唯一标识
- **accountName**: 勋章账户名称，用于显示和管理
- **accountType**: 账户类型，区分业务账户、个人账户、系统账户
- **brandCode**: 品牌代码，支持多品牌管理
- **status**: 账户状态，控制账户的可用性
- **basicConfig**: 基础配置，JSON格式存储勋章相关配置
- **displayConfig**: 展示配置，JSON格式存储展示相关配置
- **totalUsers**: 用户总数统计
- **totalBadges**: 勋章总数统计
- **activeBadges**: 活跃勋章统计

**UserBadge字段说明：**
- **userId**: 用户ID，关联用户
- **badgeId**: 勋章ID，关联勋章配置
- **obtainTime**: 获得时间
- **expireTime**: 过期时间，可为空表示永久有效
- **source**: 获得来源，如系统发放、活动获得等
- **status**: 勋章状态，有效、过期、撤销等
- **obtainCount**: 获得次数，支持重复获得的勋章
- **isDisplayed**: 是否展示，用户可控制是否在个人页面展示
- **displayOrder**: 展示顺序，用于排序

**BadgeRecord字段说明：**
- **operationType**: 操作类型，发放、撤销、过期等
- **operationTime**: 操作时间
- **operatorId**: 操作人ID，可为空表示系统操作
- **source**: 操作来源
- **reason**: 操作原因
- **beforeStatus**: 操作前状态
- **afterStatus**: 操作后状态
- **operationData**: 操作相关数据，JSON格式

**BadgeProgress字段说明：**
- **currentProgress**: 当前进度值
- **targetProgress**: 目标进度值
- **progressData**: 进度相关数据，JSON格式存储详细信息
- **lastUpdateTime**: 最后更新时间
- **startTime**: 开始时间，用于时间范围内的进度计算

#### 2.2.2 勋章账户实体

**实体名称**：勋章账户  
**实体类**：BadgeAccount  
**表名**：badge_account  
**用途**：存储勋章账户的基本配置信息和统计数据，管理勋章账户的生命周期  
**核心字段**：accountCode, accountName, accountType, brandCode, status, basicConfig

```java
@MappedEntity("badge_account")
public class BadgeAccount {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "account_code", unique = true, nullable = false)
    private String accountCode;
    
    @Column(name = "account_name", nullable = false)
    private String accountName;

    @Enumerated(EnumType.STRING)
    @Column(name = "badge_type", nullable = false)
    private BadgeType badgeType;

    @Enumerated(EnumType.STRING)
    @Column(name = "badge_category", nullable = false)
    private BadgeCategory badgeCategory;
    
    @Column(name = "brand_code", nullable = false)
    private String brandCode;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AccountStatus status = AccountStatus.ACTIVE;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "basic_config", columnDefinition = "JSON")
    private String basicConfig;
    
    @Column(name = "gain_condition", columnDefinition = "JSON")
    private String gainCondition;

    @Column(name = "reward_config", columnDefinition = "JSON")
    private String rewardConfig;
    
    @Column(name = "design_config", columnDefinition = "JSON")
    private String designConfig;
    
    @Column(name = "display_config", columnDefinition = "JSON")
    private String displayConfig;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    @Version
    private Integer version = 0;
}

/**
 * 勋章类型枚举
 */
public enum BadgeType {
    ACHIEVEMENT("成就勋章"),
    MILESTONE("里程碑勋章"),
    ACTIVITY("活动勋章"),
    SPECIAL("特殊勋章");

    private final String description;

    BadgeType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

/**
 * 勋章分类枚举
 */
public enum BadgeCategory {
    PURCHASE("购买类"),
    SOCIAL("社交类"),
    GROWTH("成长类"),
    ACTIVITY("活动类"),
    SPECIAL("特殊类");

    private final String description;

    BadgeCategory(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

/**
 * 勋章状态枚举
 */
public enum BadgeStatus {
    ACTIVE("激活"),
    INACTIVE("停用"),
    DELETED("已删除");

    private final String description;

    BadgeStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
```

#### 2.2.3 用户勋章实体

**实体名称**：用户勋章  
**实体类**：UserBadge  
**表名**：user_badge  
**用途**：记录用户获得的勋章信息，包括获得时间、来源、展示状态等  
**核心字段**：userId, badgeId, obtainTime, source, status, isDisplayed

```java
@MappedEntity("user_badge")
public class UserBadge {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "badge_id", nullable = false)
    private Long badgeId;
    
    @Column(name = "account_code", nullable = false)
    private String accountCode;

    @Column(name = "account_name", nullable = false)
    private String accountName;
    
    @Column(name = "obtain_time", nullable = false)
    private LocalDateTime obtainTime;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Column(name = "source", nullable = false)
    private String source;

    @Column(name = "transaction_id", nullable = false)
    private String transactionId;

    @Column(name = "business_id")
    private String businessId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserBadgeStatus status = UserBadgeStatus.VALID;
    
    @Column(name = "obtain_count")
    private Integer obtainCount = 1;
    
    @Column(name = "is_displayed")
    private Boolean isDisplayed = true;
    
    @Column(name = "display_order")
    private Integer displayOrder;
    
    @Column(name = "extra_data", columnDefinition = "JSON")
    private String extraData;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
}

/**
 * 用户勋章状态枚举
 */
public enum UserBadgeStatus {
    VALID("有效"),
    EXPIRED("已过期"),
    REVOKED("已撤销"),
    DELETED("已删除");
    
    private final String description;
    
    UserBadgeStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

#### 2.2.4 勋章记录实体

**实体名称**：勋章记录  
**实体类**：BadgeRecord  
**表名**：badge_record  
**用途**：记录所有勋章相关操作的详细信息，包括发放、撤销、过期等操作记录  
**核心字段**：userId, badgeId, operationType, operationTime, operatorId, reason

```java
@MappedEntity("badge_record")
public class BadgeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "badge_id", nullable = false)
    private Long badgeId;
    
    @Column(name = "user_badge_id")
    private Long userBadgeId;
    
    @Column(name = "account_code", nullable = false)
    private String accountCode;

    @Column(name = "account_name", nullable = false)
    private String accountName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false)
    private BadgeOperationType operationType;
    
    @Column(name = "operation_time", nullable = false)
    private LocalDateTime operationTime;
    
    @Column(name = "operator_id")
    private Long operatorId;
    
    @Column(name = "source", nullable = false)
    private String source;

    @Column(name = "transaction_id", nullable = false)
    private String transactionId;
    
    @Column(name = "business_id")
    private String businessId;
    
    @Column(name = "reason")
    private String reason;
    
    @Column(name = "before_status")
    private String beforeStatus;
    
    @Column(name = "after_status")
    private String afterStatus;
    
    @Column(name = "operation_data", columnDefinition = "JSON")
    private String operationData;
    
    @Column(name = "remark")
    private String remark;
    
    @CreationTimestamp
    private LocalDateTime createTime;
}

/**
 * 勋章操作类型枚举
 */
public enum BadgeOperationType {
    AWARD("发放"),
    REVOKE("撤销"),
    EXPIRE("过期"),
    DISPLAY("展示"),
    HIDE("隐藏"),
    UPDATE("更新");
    
    private final String description;
    
    BadgeOperationType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```
#### 2.2.5 勋章进度实体

**实体名称**：勋章进度  
**实体类**：BadgeProgress  
**表名**：badge_progress  
**用途**：跟踪用户获得勋章的进度信息，用于展示进度和自动发放  
**核心字段**：userId, badgeId, currentProgress, targetProgress, progressData

```java
@MappedEntity("badge_progress")
public class BadgeProgress {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "badge_id", nullable = false)
    private Long badgeId;
    
    @Column(name = "account_code", nullable = false)
    private String accountCode;
    
    @Column(name = "current_progress", nullable = false)
    private Long currentProgress = 0L;
    
    @Column(name = "target_progress", nullable = false)
    private Long targetProgress;
    
    @Column(name = "progress_data", columnDefinition = "JSON")
    private String progressData;
    
    @Column(name = "last_update_time", nullable = false)
    private LocalDateTime lastUpdateTime;
    
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    // 计算进度百分比
    public Double getProgressPercentage() {
        if (targetProgress == null || targetProgress == 0) {
            return 0.0;
        }
        return Math.min(100.0, (currentProgress.doubleValue() / targetProgress.doubleValue()) * 100);
    }
    
    // 判断是否已完成
    public Boolean isCompleted() {
        return currentProgress != null && targetProgress != null && currentProgress >= targetProgress;
    }
}
```

## 3. 勋章事务管理

### 3.1 勋章事务实体

**实体名称**：勋章事务  
**实体类**：BadgeTransaction  
**表名**：badge_transaction  
**用途**：记录所有勋章相关事务的详细信息，包括发放、撤销、过期等操作的事务记录，确保勋章操作的可追溯性和一致性  
**核心字段**：transactionId, userId, badgeId, transactionType, status, processedTime

```java
@MappedEntity("badge_transaction")
public class BadgeTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "transaction_id", unique = true, nullable = false)
    private String transactionId;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "badge_id", nullable = false)
    private Long badgeId;
    
    @Column(name = "account_code", nullable = false)
    private String accountCode;
    
    @Column(name = "business_id")
    private String businessId;
    
    @Column(name = "business_type", nullable = false)
    private String businessType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private BadgeTransactionType transactionType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TransactionStatus status = TransactionStatus.PENDING;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "source", nullable = false)
    private String source;
    
    @Column(name = "effective_time")
    private LocalDateTime effectiveTime;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Column(name = "processed_time")
    private LocalDateTime processedTime;
    
    @Column(name = "error_message")
    private String errorMessage;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;
    
    @Column(name = "operator_id")
    private Long operatorId;
    
    @Column(name = "reward_point")
    private Long rewardPoint;
    
    @Column(name = "extra_data", columnDefinition = "JSON")
    private String extraData;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    @Version
    private Integer version = 0;
}

/**
 * 勋章事务类型枚举
 */
public enum BadgeTransactionType {
    BADGE_AWARD("勋章发放"),
    BADGE_REVOKE("勋章撤销"),
    BADGE_EXPIRE("勋章过期"),
    BADGE_DISPLAY_UPDATE("勋章展示更新"),
    BADGE_BATCH_AWARD("勋章批量发放"),
    BADGE_MANUAL_ADJUST("勋章手动调整");
    
    private final String description;
    
    BadgeTransactionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

/**
 * 事务状态枚举
 */
public enum TransactionStatus {
    PENDING("待处理"),
    PROCESSING("处理中"),
    SUCCESS("成功"),
    FAILED("失败"),
    CANCELLED("已取消"),
    EXPIRED("已过期");
    
    private final String description;
    
    TransactionStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

### 3.2 勋章事务服务

```java
@Service
@Transactional
@Slf4j
public class BadgeTransactionService {
    
    @Autowired
    private BadgeTransactionRepository badgeTransactionRepository;
    
    @Autowired
    private BadgeService badgeService;
    
    @Autowired
    private UserBadgeRepository userBadgeRepository;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 创建勋章事务
     */
    public BadgeTransaction createBadgeTransaction(CreateBadgeTransactionRequest request) {
        // 1. 生成事务ID
        String transactionId = generateTransactionId();
        
        // 2. 创建事务记录
        BadgeTransaction transaction = new BadgeTransaction();
        transaction.setTransactionId(transactionId);
        transaction.setUserId(request.getUserId());
        transaction.setBadgeId(request.getBadgeId());
        transaction.setAccountCode(request.getAccountCode());
        transaction.setBusinessId(request.getBusinessId());
        transaction.setBusinessType(request.getBusinessType());
        transaction.setTransactionType(request.getTransactionType());
        transaction.setDescription(request.getDescription());
        transaction.setSource(request.getSource());
        transaction.setEffectiveTime(request.getEffectiveTime());
        transaction.setExpireTime(request.getExpireTime());
        transaction.setOperatorId(request.getOperatorId());
        transaction.setRewardPoint(request.getRewardPoint());
        transaction.setStatus(TransactionStatus.PENDING);
        transaction.setExtraData(request.getExtraData());
        
        BadgeTransaction savedTransaction = badgeTransactionRepository.save(transaction);
        
        // 3. 异步处理事务
        processBadgeTransactionAsync(savedTransaction.getId());
        
        return savedTransaction;
    }
    
    /**
     * 异步处理勋章事务
     */
    @Async("badgeTransactionExecutor")
    public void processBadgeTransactionAsync(Long transactionId) {
        try {
            processBadgeTransaction(transactionId);
        } catch (Exception e) {
            log.error("处理勋章事务失败: transactionId={}", transactionId, e);
            handleTransactionError(transactionId, e.getMessage());
        }
    }
    
    /**
     * 同步处理勋章事务
     */
    @Transactional
    public void processBadgeTransaction(Long transactionId) {
        BadgeTransaction transaction = badgeTransactionRepository.findById(transactionId)
            .orElseThrow(() -> new BusinessException("勋章事务不存在: " + transactionId));
        
        // 检查事务状态
        if (transaction.getStatus() != TransactionStatus.PENDING) {
            log.warn("勋章事务状态不正确: transactionId={}, status={}", transactionId, transaction.getStatus());
            return;
        }
        
        // 检查是否过期
        if (transaction.getExpireTime() != null && LocalDateTime.now().isAfter(transaction.getExpireTime())) {
            transaction.setStatus(TransactionStatus.EXPIRED);
            badgeTransactionRepository.save(transaction);
            return;
        }
        
        try {
            // 更新状态为处理中
            transaction.setStatus(TransactionStatus.PROCESSING);
            transaction.setProcessedTime(LocalDateTime.now());
            badgeTransactionRepository.save(transaction);
            
            // 执行勋章事务处理
            processTransactionByType(transaction);
            
            // 更新状态为成功
            transaction.setStatus(TransactionStatus.SUCCESS);
            badgeTransactionRepository.save(transaction);
            
            // 发布事务完成事件
            eventPublisher.publishEvent(new BadgeTransactionCompletedEvent(transaction));
            
        } catch (Exception e) {
            log.error("处理勋章事务失败: transactionId={}", transactionId, e);
            handleTransactionError(transactionId, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 根据事务类型处理事务
     */
    private void processTransactionByType(BadgeTransaction transaction) {
        switch (transaction.getTransactionType()) {
            case BADGE_AWARD:
                processBadgeAward(transaction);
                break;
            case BADGE_REVOKE:
                processBadgeRevoke(transaction);
                break;
            case BADGE_EXPIRE:
                processBadgeExpire(transaction);
                break;
            case BADGE_DISPLAY_UPDATE:
                processBadgeDisplayUpdate(transaction);
                break;
            case BADGE_BATCH_AWARD:
                processBadgeBatchAward(transaction);
                break;
            case BADGE_MANUAL_ADJUST:
                processBadgeManualAdjust(transaction);
                break;
            default:
                throw new BusinessException("不支持的事务类型: " + transaction.getTransactionType());
        }
    }
    
    /**
     * 处理勋章发放事务
     */
    private void processBadgeAward(BadgeTransaction transaction) {
        Long userId = transaction.getUserId();
        Long badgeId = transaction.getBadgeId();
        
        // 检查用户是否已获得该勋章
        boolean hasObtained = userBadgeRepository.existsByUserIdAndBadgeIdAndStatus(
            userId, badgeId, UserBadgeStatus.VALID);
        
        if (hasObtained) {
            throw new BusinessException("用户已获得该勋章，不可重复发放");
        }
        
        // 构建发放请求
        AwardBadgeRequest awardRequest = AwardBadgeRequest.builder()
            .userId(userId)
            .badgeId(badgeId)
            .accountCode(transaction.getAccountCode())
            .source(transaction.getSource())
            .businessId(transaction.getBusinessId())
            .extraData(parseExtraData(transaction.getExtraData()))
            .build();
        
        // 执行勋章发放
        BadgeAwardResult result = badgeService.awardBadge(awardRequest);
        
        // 更新事务结果
        transaction.setRewardPoint(result.getRewardPoint());
    }
    
    /**
     * 处理勋章撤销事务
     */
    private void processBadgeRevoke(BadgeTransaction transaction) {
        Long userId = transaction.getUserId();
        Long badgeId = transaction.getBadgeId();
        
        // 查找用户勋章
        UserBadge userBadge = userBadgeRepository.findByUserIdAndBadgeIdAndStatus(
            userId, badgeId, UserBadgeStatus.VALID)
            .orElseThrow(() -> new BusinessException("用户勋章不存在或已失效"));
        
        // 撤销勋章
        userBadge.setStatus(UserBadgeStatus.REVOKED);
        userBadgeRepository.save(userBadge);
        
        // 记录撤销原因
        BadgeRecord record = new BadgeRecord();
        record.setUserId(userId);
        record.setBadgeId(badgeId);
        record.setUserBadgeId(userBadge.getId());
        record.setAccountCode(transaction.getAccountCode());
        record.setOperationType(BadgeOperationType.REVOKE);
        record.setOperationTime(LocalDateTime.now());
        record.setOperatorId(transaction.getOperatorId());
        record.setSource(transaction.getSource());
        record.setTransactionId(transaction.getTransactionId());
        record.setReason(transaction.getDescription());
        record.setBeforeStatus(UserBadgeStatus.VALID.name());
        record.setAfterStatus(UserBadgeStatus.REVOKED.name());
        badgeRecordRepository.save(record);
    }
    
    /**
     * 处理勋章过期事务
     */
    private void processBadgeExpire(BadgeTransaction transaction) {
        Long userId = transaction.getUserId();
        Long badgeId = transaction.getBadgeId();
        
        // 查找用户勋章
        UserBadge userBadge = userBadgeRepository.findByUserIdAndBadgeIdAndStatus(
            userId, badgeId, UserBadgeStatus.VALID)
            .orElseThrow(() -> new BusinessException("用户勋章不存在或已失效"));
        
        // 设置勋章过期
        userBadge.setStatus(UserBadgeStatus.EXPIRED);
        userBadgeRepository.save(userBadge);
        
        // 记录过期操作
        BadgeRecord record = new BadgeRecord();
        record.setUserId(userId);
        record.setBadgeId(badgeId);
        record.setUserBadgeId(userBadge.getId());
        record.setAccountCode(transaction.getAccountCode());
        record.setOperationType(BadgeOperationType.EXPIRE);
        record.setOperationTime(LocalDateTime.now());
        record.setSource("SYSTEM_AUTO");
        record.setTransactionId(transaction.getTransactionId());
        record.setReason("勋章已过期");
        record.setBeforeStatus(UserBadgeStatus.VALID.name());
        record.setAfterStatus(UserBadgeStatus.EXPIRED.name());
        badgeRecordRepository.save(record);
    }
    
    /**
     * 处理勋章展示更新事务
     */
    private void processBadgeDisplayUpdate(BadgeTransaction transaction) {
        Map<String, Object> extraData = parseExtraData(transaction.getExtraData());
        Boolean isDisplayed = (Boolean) extraData.get("isDisplayed");
        Integer displayOrder = (Integer) extraData.get("displayOrder");
        
        badgeService.updateBadgeDisplay(
            transaction.getUserId(),
            (Long) extraData.get("userBadgeId"),
            isDisplayed,
            displayOrder
        );
    }
    
    /**
     * 处理勋章批量发放事务
     */
    private void processBadgeBatchAward(BadgeTransaction transaction) {
        Map<String, Object> extraData = parseExtraData(transaction.getExtraData());
        List<Long> userIds = (List<Long>) extraData.get("userIds");
        
        for (Long userId : userIds) {
            try {
                AwardBadgeRequest awardRequest = AwardBadgeRequest.builder()
                    .userId(userId)
                    .badgeId(transaction.getBadgeId())
                    .accountCode(transaction.getAccountCode())
                    .source(transaction.getSource())
                    .businessId(transaction.getBusinessId())
                    .build();
                
                badgeService.awardBadge(awardRequest);
            } catch (Exception e) {
                log.error("批量发放勋章失败: userId={}, badgeId={}", userId, transaction.getBadgeId(), e);
            }
        }
    }
    
    /**
     * 处理勋章手动调整事务
     */
    private void processBadgeManualAdjust(BadgeTransaction transaction) {
        Map<String, Object> extraData = parseExtraData(transaction.getExtraData());
        String adjustType = (String) extraData.get("adjustType");
        
        switch (adjustType) {
            case "FORCE_AWARD":
                // 强制发放勋章（忽略条件检查）
                forceAwardBadge(transaction);
                break;
            case "RESET_PROGRESS":
                // 重置勋章进度
                resetBadgeProgress(transaction);
                break;
            case "UPDATE_EXPIRE_TIME":
                // 更新过期时间
                updateBadgeExpireTime(transaction, extraData);
                break;
            default:
                throw new BusinessException("不支持的调整类型: " + adjustType);
        }
    }
    
    /**
     * 处理事务错误
     */
    private void handleTransactionError(Long transactionId, String errorMessage) {
        BadgeTransaction transaction = badgeTransactionRepository.findById(transactionId)
            .orElse(null);
        
        if (transaction != null) {
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setErrorMessage(errorMessage);
            transaction.setRetryCount(transaction.getRetryCount() + 1);
            
            // 检查是否需要重试
            if (transaction.getRetryCount() < transaction.getMaxRetryCount()) {
                // 延迟重试
                scheduleRetry(transactionId);
            }
            
            badgeTransactionRepository.save(transaction);
        }
    }
    
    /**
     * 调度重试
     */
    @Async("badgeTransactionExecutor")
    public void scheduleRetry(Long transactionId) {
        try {
            // 延迟5分钟后重试
            Thread.sleep(5 * 60 * 1000);
            processBadgeTransaction(transactionId);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("重试处理勋章事务失败: transactionId={}", transactionId, e);
        }
    }
    
    /**
     * 查询勋章事务
     */
    public BadgeTransaction getBadgeTransaction(String transactionId) {
        return badgeTransactionRepository.findByTransactionId(transactionId)
            .orElseThrow(() -> new BusinessException("勋章事务不存在: " + transactionId));
    }
    
    /**
     * 查询用户勋章事务列表
     */
    public Page<BadgeTransaction> getUserBadgeTransactions(Long userId, Pageable pageable) {
        return badgeTransactionRepository.findByUserIdOrderByCreateTimeDesc(userId, pageable);
    }
    
    /**
     * 取消勋章事务
     */
    public void cancelBadgeTransaction(String transactionId, String reason) {
        BadgeTransaction transaction = getBadgeTransaction(transactionId);
        
        if (transaction.getStatus() != TransactionStatus.PENDING) {
            throw new BusinessException("只能取消待处理状态的事务");
        }
        
        transaction.setStatus(TransactionStatus.CANCELLED);
        transaction.setErrorMessage(reason);
        badgeTransactionRepository.save(transaction);
    }
    
    /**
     * 生成事务ID
     */
    private String generateTransactionId() {
        return "BT" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(6);
    }
    
    /**
     * 解析额外数据
     */
    private Map<String, Object> parseExtraData(String extraData) {
        if (StringUtils.isBlank(extraData)) {
            return new HashMap<>();
        }
        try {
            return JSON.parseObject(extraData, Map.class);
        } catch (Exception e) {
            log.warn("解析额外数据失败: {}", extraData, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 强制发放勋章
     */
    private void forceAwardBadge(BadgeTransaction transaction) {
        // 实现强制发放逻辑
    }
    
    /**
     * 重置勋章进度
     */
    private void resetBadgeProgress(BadgeTransaction transaction) {
        badgeProgressRepository.deleteByUserIdAndBadgeId(
            transaction.getUserId(), transaction.getBadgeId());
    }
    
    /**
     * 更新勋章过期时间
     */
    private void updateBadgeExpireTime(BadgeTransaction transaction, Map<String, Object> extraData) {
        // 实现更新过期时间逻辑
    }
}
```

### 3.3 勋章事务仓储

```java
@Repository
public interface BadgeTransactionRepository extends JpaRepository<BadgeTransaction, Long> {
    
    Optional<BadgeTransaction> findByTransactionId(String transactionId);
    
    Page<BadgeTransaction> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);
    
    List<BadgeTransaction> findByStatusAndCreateTimeBefore(TransactionStatus status, LocalDateTime createTime);
    
    List<BadgeTransaction> findByStatusAndRetryCountLessThan(TransactionStatus status, Integer maxRetryCount);
    
    @Query("SELECT COUNT(t) FROM BadgeTransaction t WHERE t.userId = :userId AND t.status = :status AND t.createTime >= :startTime")
    Long countByUserIdAndStatusAndCreateTimeAfter(@Param("userId") Long userId, 
                                                  @Param("status") TransactionStatus status, 
                                                  @Param("startTime") LocalDateTime startTime);
    
    @Query("SELECT t FROM BadgeTransaction t WHERE t.badgeId = :badgeId AND t.status = :status ORDER BY t.createTime DESC")
    List<BadgeTransaction> findByBadgeIdAndStatus(@Param("badgeId") Long badgeId, @Param("status") TransactionStatus status);
    
    @Query("SELECT t FROM BadgeTransaction t WHERE t.transactionType = :transactionType AND t.createTime BETWEEN :startTime AND :endTime")
    List<BadgeTransaction> findByTransactionTypeAndCreateTimeBetween(@Param("transactionType") BadgeTransactionType transactionType, 
                                                                     @Param("startTime") LocalDateTime startTime, 
                                                                     @Param("endTime") LocalDateTime endTime);
}
```

### 3.4 勋章事务事件

```java
/**
 * 勋章事务完成事件
 */
public class BadgeTransactionCompletedEvent extends ApplicationEvent {
    
    private final BadgeTransaction transaction;
    
    public BadgeTransactionCompletedEvent(BadgeTransaction transaction) {
        super(transaction);
        this.transaction = transaction;
    }
    
    public BadgeTransaction getTransaction() {
        return transaction;
    }
}

/**
 * 勋章事务事件监听器
 */
@Component
@EventListener
@Slf4j
public class BadgeTransactionEventListener {
    
    @Autowired
    private BadgeNotificationService notificationService;
    
    @Autowired
    private BadgeMetrics badgeMetrics;
    
    /**
     * 处理勋章事务完成事件
     */
    @EventListener
    @Async("badgeEventExecutor")
    public void handleBadgeTransactionCompleted(BadgeTransactionCompletedEvent event) {
        BadgeTransaction transaction = event.getTransaction();
        
        try {
            // 发送通知
            if (transaction.getTransactionType() == BadgeTransactionType.BADGE_AWARD) {
                notificationService.sendBadgeAwardNotification(
                    transaction.getUserId(),
                    transaction.getBadgeId(),
                    transaction.getAccountCode()
                );
                
                // 记录发放指标
                badgeMetrics.recordBadgeAwarded(transaction.getAccountCode(), transaction.getSource());
                
            } else if (transaction.getTransactionType() == BadgeTransactionType.BADGE_REVOKE) {
                notificationService.sendBadgeRevokeNotification(
                    transaction.getUserId(),
                    transaction.getBadgeId(),
                    transaction.getDescription()
                );
                
                // 记录撤销指标
                badgeMetrics.recordBadgeRevoked(transaction.getAccountCode(), transaction.getSource());
            }
            
        } catch (Exception e) {
            log.error("处理勋章事务完成事件失败: transactionId={}", transaction.getTransactionId(), e);
        }
    }
}
```

## 4. 核心业务逻辑

### 4.1 勋章检测引擎
```java
@Component
@Slf4j
public class BadgeDetectionEngine {
    
    @Autowired
    private BadgeAccountRepository badgeAccountRepository;
    
    @Autowired
    private UserBadgeRepository userBadgeRepository;
    
    @Autowired
    private BadgeProgressRepository badgeProgressRepository;
    
    @Autowired
    private BadgeConditionEvaluator conditionEvaluator;
    
    public List<BadgeDetectionResult> detectBadges(Long userId, String accountCode, String eventType, Map<String, Object> eventData) {
        List<BadgeDetectionResult> results = new ArrayList<>();
        
        // 1. 获取相关勋章账户配置
        List<BadgeAccount> candidateBadges = getCandidateBadges(accountCode, eventType);
        
        for (BadgeAccount badge : candidateBadges) {
            try {
                // 2. 检查用户是否已获得该勋章
                if (hasUserObtainedBadge(userId, badge.getId())) {
                    continue;
                }
                
                // 3. 评估获得条件
                BadgeConditionResult conditionResult = conditionEvaluator.evaluate(
                    userId, badge, eventType, eventData);
                
                if (conditionResult.isMet()) {
                    results.add(BadgeDetectionResult.builder()
                        .badgeId(badge.getId())
                        .userId(userId)
                        .accountCode(accountCode)
                        .eventType(eventType)
                        .eventData(eventData)
                        .conditionResult(conditionResult)
                        .build());
                } else if (conditionResult.hasProgress()) {
                    // 4. 更新进度
                    updateBadgeProgress(userId, badge.getId(), accountCode, conditionResult);
                }
                
            } catch (Exception e) {
                log.error("勋章检测异常: userId={}, badgeId={}", userId, badge.getId(), e);
            }
        }
        
        return results;
    }
    
    private List<BadgeAccount> getCandidateBadges(String accountCode, String eventType) {
        return badgeAccountRepository.findByAccountCodeAndStatus(accountCode, AccountStatus.ACTIVE)
            .stream()
            .filter(badge -> isEventTypeMatched(badge, eventType))
            .collect(Collectors.toList());
    }
    
    private boolean isEventTypeMatched(BadgeAccount badge, String eventType) {
        // 解析获得条件中的事件类型匹配逻辑
        try {
            BadgeGainCondition condition = JSON.parseObject(badge.getGainCondition(), BadgeGainCondition.class);
            return condition.getEventTypes().contains(eventType);
        } catch (Exception e) {
            log.warn("解析勋章获得条件失败: badgeId={}", badge.getId(), e);
            return false;
        }
    }
    
    private boolean hasUserObtainedBadge(Long userId, Long badgeId) {
        return userBadgeRepository.existsByUserIdAndBadgeIdAndStatus(
            userId, badgeId, UserBadgeStatus.VALID);
    }
    
    private void updateBadgeProgress(Long userId, Long badgeId, String accountCode, BadgeConditionResult conditionResult) {
        BadgeProgress progress = badgeProgressRepository.findByUserIdAndBadgeId(userId, badgeId)
            .orElseGet(() -> {
                BadgeProgress newProgress = new BadgeProgress();
                newProgress.setUserId(userId);
                newProgress.setBadgeId(badgeId);
                newProgress.setAccountCode(accountCode);
                newProgress.setTargetProgress(conditionResult.getTargetProgress());
                newProgress.setStartTime(LocalDateTime.now());
                return newProgress;
            });
        
        progress.setCurrentProgress(conditionResult.getCurrentProgress());
        progress.setProgressData(conditionResult.getProgressDataJson());
        progress.setLastUpdateTime(LocalDateTime.now());
        
        badgeProgressRepository.save(progress);
    }
}
```

### 3.2 勋章条件评估器
```java
@Component
@Slf4j
public class BadgeConditionEvaluator {
    
    @Autowired
    private PointService pointService;
    
    @Autowired
    private GrowthService growthService;
    
    @Autowired
    private OrderService orderService;
    
    public BadgeConditionResult evaluate(Long userId, BadgeAccount badge, String eventType, Map<String, Object> eventData) {
        try {
            BadgeGainCondition condition = parseGainCondition(badge.getGainCondition());
            
            switch (condition.getType()) {
                case PURCHASE_AMOUNT:
                    return evaluatePurchaseAmount(userId, condition, eventData);
                case PURCHASE_COUNT:
                    return evaluatePurchaseCount(userId, condition, eventData);
                case POINTS_EARNED:
                    return evaluatePointEarned(userId, condition, eventData);
                case GROWTH_VALUE:
                    return evaluateGrowthValue(userId, condition, eventData);
                case CONSECUTIVE_DAYS:
                    return evaluateConsecutiveDays(userId, condition, eventData);
                case SOCIAL_SHARE:
                    return evaluateSocialShare(userId, condition, eventData);
                case TASK_COMPLETION:
                    return evaluateTaskCompletion(userId, condition, eventData);
                default:
                    return BadgeConditionResult.notMet();
            }
        } catch (Exception e) {
            log.error("勋章条件评估异常: userId={}, badgeId={}", userId, badge.getId(), e);
            return BadgeConditionResult.notMet();
        }
    }
    
    private BadgeGainCondition parseGainCondition(String gainConditionJson) {
        try {
            return JSON.parseObject(gainConditionJson, BadgeGainCondition.class);
        } catch (Exception e) {
            log.error("解析勋章获得条件失败: {}", gainConditionJson, e);
            throw new BadgeException("勋章获得条件配置错误");
        }
    }
    
    private BadgeConditionResult evaluatePurchaseAmount(Long userId, BadgeGainCondition condition, Map<String, Object> eventData) {
        BigDecimal targetAmount = condition.getTargetValue();
        LocalDateTime startTime = getConditionStartTime(condition);
        
        BigDecimal currentAmount = orderService.getTotalPurchaseAmount(userId, startTime, LocalDateTime.now());
        
        if (currentAmount.compareTo(targetAmount) >= 0) {
            return BadgeConditionResult.met(currentAmount.longValue(), targetAmount.longValue());
        } else {
            return BadgeConditionResult.progress(currentAmount.longValue(), targetAmount.longValue());
        }
    }
    
    private BadgeConditionResult evaluatePurchaseCount(Long userId, BadgeGainCondition condition, Map<String, Object> eventData) {
        Long targetCount = condition.getTargetValue().longValue();
        LocalDateTime startTime = getConditionStartTime(condition);
        
        Long currentCount = orderService.getPurchaseCount(userId, startTime, LocalDateTime.now());
        
        if (currentCount >= targetCount) {
            return BadgeConditionResult.met(currentCount, targetCount);
        } else {
            return BadgeConditionResult.progress(currentCount, targetCount);
        }
    }
    
    private BadgeConditionResult evaluatePointEarned(Long userId, BadgeGainCondition condition, Map<String, Object> eventData) {
        Long targetPoint = condition.getTargetValue().longValue();
        LocalDateTime startTime = getConditionStartTime(condition);
        
        Long currentPoint = pointService.getEarnedPoint(userId, startTime, LocalDateTime.now());
        
        if (currentPoint >= targetPoint) {
            return BadgeConditionResult.met(currentPoint, targetPoint);
        } else {
            return BadgeConditionResult.progress(currentPoint, targetPoint);
        }
    }
    
    private BadgeConditionResult evaluateGrowthValue(Long userId, BadgeGainCondition condition, Map<String, Object> eventData) {
        Long targetGrowth = condition.getTargetValue().longValue();
        
        GrowthAccount account = growthService.getGrowthAccount(userId);
        Long currentGrowth = account.getTotalGrowth();
        
        if (currentGrowth >= targetGrowth) {
            return BadgeConditionResult.met(currentGrowth, targetGrowth);
        } else {
            return BadgeConditionResult.progress(currentGrowth, targetGrowth);
        }
    }
    
    private BadgeConditionResult evaluateConsecutiveDays(Long userId, BadgeGainCondition condition, Map<String, Object> eventData) {
        Integer targetDays = condition.getTargetValue().intValue();
        String actionType = condition.getActionType();
        
        Integer currentDays = getConsecutiveDays(userId, actionType);
        
        if (currentDays >= targetDays) {
            return BadgeConditionResult.met(currentDays.longValue(), targetDays.longValue());
        } else {
            return BadgeConditionResult.progress(currentDays.longValue(), targetDays.longValue());
        }
    }
    
    private LocalDateTime getConditionStartTime(BadgeGainCondition condition) {
        if (condition.getTimeRange() != null) {
            return LocalDateTime.now().minusDays(condition.getTimeRange());
        }
        return LocalDateTime.of(1970, 1, 1, 0, 0);
    }
    
    private Integer getConsecutiveDays(Long userId, String actionType) {
        // 实现连续天数计算逻辑
        return 0;
    }
}
```

### 3.3 勋章服务层
```java
@Service
@Transactional
@Slf4j
public class BadgeService {
    
    @Autowired
    private BadgeAccountRepository badgeAccountRepository;
    
    @Autowired
    private UserBadgeRepository userBadgeRepository;
    
    @Autowired
    private BadgeProgressRepository badgeProgressRepository;
    
    @Autowired
    private BadgeRecordRepository badgeRecordRepository;
    
    @Autowired
    private BadgeDetectionEngine detectionEngine;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Autowired
    private PointService pointService;
    
    public BadgeAwardResult awardBadge(AwardBadgeRequest request) {
        // 1. 参数校验
        validateAwardBadgeRequest(request);
        
        // 2. 获取勋章账户配置
        BadgeAccount badgeAccount = badgeAccountRepository.findById(request.getBadgeId())
            .orElseThrow(() -> new BadgeException("勋章账户不存在"));
        
        // 3. 检查勋章状态
        if (badgeAccount.getStatus() != AccountStatus.ACTIVE) {
            throw new BadgeException("勋章账户已停用");
        }
        
        // 4. 检查重复获得限制
        if (hasUserObtainedBadge(request.getUserId(), request.getBadgeId())) {
            throw new BadgeException("该勋章不可重复获得");
        }
        
        // 5. 生成交易ID
        String transactionId = generateTransactionId();
        
        // 6. 创建用户勋章记录
        UserBadge userBadge = createUserBadge(request, badgeAccount, transactionId);
        userBadgeRepository.save(userBadge);
        
        // 7. 创建勋章记录
        BadgeRecord badgeRecord = createBadgeRecord(request, badgeAccount, userBadge, transactionId);
        badgeRecordRepository.save(badgeRecord);
        
        // 8. 发放勋章奖励
        awardBadgeRewards(request.getUserId(), badgeAccount);
        
        // 9. 清理进度记录
        badgeProgressRepository.deleteByUserIdAndBadgeId(request.getUserId(), request.getBadgeId());
        
        // 10. 发布事件
        eventPublisher.publishEvent(new BadgeAwardedEvent(
            request.getUserId(),
            request.getBadgeId(),
            badgeAccount.getAccountCode(),
            userBadge.getId()
        ));
        
        return BadgeAwardResult.builder()
            .userBadgeId(userBadge.getId())
            .badgeCode(badgeAccount.getAccountCode())
            .badgeName(badgeAccount.getAccountName())
            .obtainTime(userBadge.getObtainTime())
            .build();
    }
    
    public void detectAndAwardBadges(Long userId, String accountCode, String eventType, Map<String, Object> eventData) {
        try {
            // 1. 检测可获得的勋章
            List<BadgeDetectionResult> detectionResults = detectionEngine.detectBadges(userId, accountCode, eventType, eventData);
            
            // 2. 批量发放勋章
            for (BadgeDetectionResult result : detectionResults) {
                try {
                    AwardBadgeRequest awardRequest = AwardBadgeRequest.builder()
                        .userId(result.getUserId())
                        .badgeId(result.getBadgeId())
                        .accountCode(result.getAccountCode())
                        .source(eventType)
                        .businessId(extractBusinessId(eventData))
                        .extraData(result.getEventData())
                        .build();
                    
                    awardBadge(awardRequest);
                } catch (Exception e) {
                    log.error("勋章发放失败: userId={}, badgeId={}", userId, result.getBadgeId(), e);
                }
            }
        } catch (Exception e) {
            log.error("勋章检测和发放异常: userId={}, eventType={}", userId, eventType, e);
        }
    }
    
    @Cacheable(value = "userBadges", key = "#userId + ':' + #accountCode")
    public List<UserBadge> getUserBadges(Long userId, String accountCode) {
        return userBadgeRepository.findByUserIdAndAccountCodeAndStatusOrderByObtainTimeDesc(
            userId, accountCode, UserBadgeStatus.VALID);
    }
    
    public List<BadgeProgress> getBadgeProgress(Long userId, String accountCode) {
        return badgeProgressRepository.findByUserIdAndAccountCode(userId, accountCode);
    }
    
    public void updateBadgeDisplay(Long userId, Long userBadgeId, boolean isDisplayed, Integer displayOrder) {
        UserBadge userBadge = userBadgeRepository.findByIdAndUserId(userBadgeId, userId)
            .orElseThrow(() -> new BadgeException("用户勋章不存在"));
        
        UserBadgeStatus beforeStatus = userBadge.getStatus();
        userBadge.setIsDisplayed(isDisplayed);
        if (displayOrder != null) {
            userBadge.setDisplayOrder(displayOrder);
        }
        
        userBadgeRepository.save(userBadge);
        
        // 记录操作日志
        BadgeRecord record = new BadgeRecord();
        record.setUserId(userId);
        record.setBadgeId(userBadge.getBadgeId());
        record.setUserBadgeId(userBadgeId);
        record.setAccountCode(userBadge.getAccountCode());
        record.setAccountName(userBadge.getAccountName());
        record.setOperationType(isDisplayed ? BadgeOperationType.DISPLAY : BadgeOperationType.HIDE);
        record.setOperationTime(LocalDateTime.now());
        record.setOperatorId(userId);
        record.setSource("USER_OPERATION");
        record.setTransactionId(generateTransactionId());
        record.setBeforeStatus(beforeStatus.name());
        record.setAfterStatus(userBadge.getStatus().name());
        badgeRecordRepository.save(record);
        
        // 清除缓存
        clearUserBadgesCache(userId, userBadge.getAccountCode());
    }
    
    private UserBadge createUserBadge(AwardBadgeRequest request, BadgeAccount badgeAccount, String transactionId) {
        UserBadge userBadge = new UserBadge();
        userBadge.setUserId(request.getUserId());
        userBadge.setBadgeId(request.getBadgeId());
        userBadge.setAccountCode(badgeAccount.getAccountCode());
        userBadge.setAccountName(badgeAccount.getAccountName());
        userBadge.setObtainTime(LocalDateTime.now());
        userBadge.setSource(request.getSource());
        userBadge.setTransactionId(transactionId);
        userBadge.setBusinessId(request.getBusinessId());
        userBadge.setStatus(UserBadgeStatus.VALID);
        userBadge.setObtainCount(1);
        userBadge.setIsDisplayed(true);
        
        // 设置额外数据
        if (request.getExtraData() != null) {
            userBadge.setExtraData(JSON.toJSONString(request.getExtraData()));
        }
        
        return userBadge;
    }
    
    private BadgeRecord createBadgeRecord(AwardBadgeRequest request, BadgeAccount badgeAccount, UserBadge userBadge, String transactionId) {
        BadgeRecord record = new BadgeRecord();
        record.setUserId(request.getUserId());
        record.setBadgeId(request.getBadgeId());
        record.setUserBadgeId(userBadge.getId());
        record.setAccountCode(badgeAccount.getAccountCode());
        record.setAccountName(badgeAccount.getAccountName());
        record.setOperationType(BadgeOperationType.AWARD);
        record.setOperationTime(LocalDateTime.now());
        record.setSource(request.getSource());
        record.setTransactionId(transactionId);
        record.setBusinessId(request.getBusinessId());
        record.setAfterStatus(UserBadgeStatus.VALID.name());
        record.setReason("系统自动发放");
        
        if (request.getExtraData() != null) {
            record.setOperationData(JSON.toJSONString(request.getExtraData()));
        }
        
        return record;
    }
    
    private void awardBadgeRewards(Long userId, BadgeAccount badgeAccount) {
        // 解析奖励配置
        if (StringUtils.isNotBlank(badgeAccount.getRewardConfig())) {
            try {
                BadgeRewardConfig rewardConfig = JSON.parseObject(badgeAccount.getRewardConfig(), BadgeRewardConfig.class);
                
                // 发放积分奖励
                if (rewardConfig.getPointReward() != null && rewardConfig.getPointReward() > 0) {
                    pointService.earnPoint(EarnPointRequest.builder()
                        .userId(userId)
                        .point(rewardConfig.getPointReward())
                        .businessType("BADGE_REWARD")
                        .businessId(badgeAccount.getAccountCode())
                        .description("勋章奖励: " + badgeAccount.getAccountName())
                        .build());
                }
                
                // 发放其他奖励
                processOtherRewards(userId, rewardConfig);
                
            } catch (Exception e) {
                log.error("处理勋章奖励失败: userId={}, badgeId={}", userId, badgeAccount.getId(), e);
            }
        }
    }
    
    private void processOtherRewards(Long userId, BadgeRewardConfig rewardConfig) {
        // 实现其他奖励类型的处理逻辑
    }
    
    private boolean hasUserObtainedBadge(Long userId, Long badgeId) {
        return userBadgeRepository.existsByUserIdAndBadgeIdAndStatus(
            userId, badgeId, UserBadgeStatus.VALID);
    }
    
    private String generateTransactionId() {
        return "BADGE_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    private String extractBusinessId(Map<String, Object> eventData) {
        return eventData != null ? (String) eventData.get("businessId") : null;
    }
    
    private void clearUserBadgesCache(Long userId, String accountCode) {
        // 实现缓存清理逻辑
    }
    
    private void validateAwardBadgeRequest(AwardBadgeRequest request) {
        if (request.getUserId() == null) {
            throw new BadgeException("用户ID不能为空");
        }
        if (request.getBadgeId() == null) {
            throw new BadgeException("勋章ID不能为空");
        }
        if (StringUtils.isBlank(request.getSource())) {
            throw new BadgeException("勋章来源不能为空");
        }
    }
}
```

### 3.4 勋章控制器
```java
@RestController
@RequestMapping("/api/v1/badge")
@Validated
@Slf4j
public class BadgeController {
    
    @Autowired
    private BadgeService badgeService;
    
    @PostMapping("/award")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<BadgeAwardResult> awardBadge(@Valid @RequestBody AwardBadgeRequest request) {
        try {
            BadgeAwardResult result = badgeService.awardBadge(request);
            return Result.success(result);
        } catch (BadgeException e) {
            log.warn("勋章发放失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("勋章发放异常: userId={}, badgeId={}", request.getUserId(), request.getBadgeId(), e);
            return Result.error("勋章发放失败");
        }
    }
    
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('USER')")
    public Result<List<UserBadgeVO>> getUserBadges(@PathVariable Long userId) {
        try {
            List<UserBadge> badges = badgeService.getUserBadges(userId);
            List<UserBadgeVO> badgeVOs = badges.stream()
                .map(UserBadgeVO::from)
                .collect(Collectors.toList());
            return Result.success(badgeVOs);
        } catch (Exception e) {
            log.error("获取用户勋章异常: userId={}", userId, e);
            return Result.error("获取用户勋章失败");
        }
    }
    
    @GetMapping("/progress/{userId}")
    @PreAuthorize("hasRole('USER')")
    public Result<List<BadgeProgressVO>> getBadgeProgress(@PathVariable Long userId) {
        try {
            List<BadgeProgress> progressList = badgeService.getBadgeProgress(userId);
            List<BadgeProgressVO> progressVOs = progressList.stream()
                .map(BadgeProgressVO::from)
                .collect(Collectors.toList());
            return Result.success(progressVOs);
        } catch (Exception e) {
            log.error("获取勋章进度异常: userId={}", userId, e);
            return Result.error("获取勋章进度失败");
        }
    }
    
    @GetMapping("/config")
    @PreAuthorize("hasRole('USER')")
    public Result<List<BadgeConfigVO>> getBadgeConfigs() {
        try {
            List<BadgeConfig> configs = badgeService.getAllActiveBadgeConfigs();
            List<BadgeConfigVO> configVOs = configs.stream()
                .map(BadgeConfigVO::from)
                .collect(Collectors.toList());
            return Result.success(configVOs);
        } catch (Exception e) {
            log.error("获取勋章配置异常", e);
            return Result.error("获取勋章配置失败");
        }
    }
    
    @PutMapping("/display")
    @PreAuthorize("hasRole('USER')")
    public Result<Void> updateBadgeDisplay(@Valid @RequestBody UpdateBadgeDisplayRequest request) {
        try {
            badgeService.updateBadgeDisplay(
                request.getUserId(),
                request.getUserBadgeId(),
                request.getIsDisplayed(),
                request.getDisplayOrder()
            );
            return Result.success();
        } catch (BadgeException e) {
            log.warn("更新勋章展示失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新勋章展示异常: userId={}, userBadgeId={}", 
                request.getUserId(), request.getUserBadgeId(), e);
            return Result.error("更新勋章展示失败");
        }
    }
}
```

## 4. 数据访问层

### 4.1 Repository接口
```java
@Repository
public interface BadgeAccountRepository extends JpaRepository<BadgeAccount, Long> {
    
    List<BadgeAccount> findByStatus(AccountStatus status);
    
    List<BadgeAccount> findByAccountCodeAndStatus(String accountCode, AccountStatus status);
    
    List<BadgeAccount> findByBadgeTypeAndStatus(BadgeType badgeType, AccountStatus status);
    
    List<BadgeAccount> findByBadgeCategoryAndStatus(BadgeCategory badgeCategory, AccountStatus status);
    
    Optional<BadgeAccount> findByAccountCode(String accountCode);
    
    List<BadgeAccount> findByBrandCodeAndStatus(String brandCode, AccountStatus status);
    
    @Query("SELECT ba FROM BadgeAccount ba WHERE ba.status = :status AND ba.brandCode = :brandCode")
    List<BadgeAccount> findActiveBadgesByBrand(@Param("brandCode") String brandCode, @Param("status") AccountStatus status);
    
    @Query("SELECT COUNT(*) FROM BadgeAccount ba WHERE ba.status = :status")
    Long countByStatus(@Param("status") AccountStatus status);
}

@Repository
public interface UserBadgeRepository extends JpaRepository<UserBadge, Long> {
    
    List<UserBadge> findByUserIdAndStatusOrderByObtainTimeDesc(Long userId, UserBadgeStatus status);
    
    List<UserBadge> findByUserIdAndAccountCodeAndStatusOrderByObtainTimeDesc(Long userId, String accountCode, UserBadgeStatus status);
    
    Optional<UserBadge> findByIdAndUserId(Long id, Long userId);
    
    boolean existsByUserIdAndBadgeIdAndStatus(Long userId, Long badgeId, UserBadgeStatus status);
    
    @Query("SELECT COALESCE(SUM(ub.obtainCount), 0) FROM UserBadge ub WHERE ub.userId = :userId AND ub.badgeId = :badgeId")
    Integer sumObtainCountByUserIdAndBadgeId(@Param("userId") Long userId, @Param("badgeId") Long badgeId);
    
    @Query("SELECT ub FROM UserBadge ub WHERE ub.expireTime IS NOT NULL AND ub.expireTime <= :now AND ub.status = :status")
    List<UserBadge> findExpiredBadges(@Param("now") LocalDateTime now, @Param("status") UserBadgeStatus status);
    
    @Query("SELECT COUNT(DISTINCT ub.badgeId) FROM UserBadge ub WHERE ub.userId = :userId AND ub.status = :status")
    Long countDistinctBadgesByUserId(@Param("userId") Long userId, @Param("status") UserBadgeStatus status);
    
    @Query("SELECT COUNT(*) FROM UserBadge ub WHERE ub.userId = :userId AND ub.accountCode = :accountCode AND ub.status = :status")
    Long countByUserIdAndAccountCodeAndStatus(@Param("userId") Long userId, @Param("accountCode") String accountCode, @Param("status") UserBadgeStatus status);
    
    @Query("SELECT ub FROM UserBadge ub WHERE ub.userId = :userId AND ub.isDisplayed = true AND ub.status = :status ORDER BY ub.displayOrder ASC, ub.obtainTime DESC")
    List<UserBadge> findDisplayedBadgesByUserId(@Param("userId") Long userId, @Param("status") UserBadgeStatus status);
    
    Optional<UserBadge> findByTransactionId(String transactionId);
}

@Repository
public interface BadgeProgressRepository extends JpaRepository<BadgeProgress, Long> {
    
    List<BadgeProgress> findByUserId(Long userId);
    
    List<BadgeProgress> findByUserIdAndAccountCode(Long userId, String accountCode);
    
    Optional<BadgeProgress> findByUserIdAndBadgeId(Long userId, Long badgeId);
    
    void deleteByUserIdAndBadgeId(Long userId, Long badgeId);
    
    @Query("SELECT bp FROM BadgeProgress bp WHERE bp.currentProgress >= bp.targetProgress")
    List<BadgeProgress> findCompletedProgress();
    
    @Query("SELECT bp FROM BadgeProgress bp WHERE bp.userId = :userId AND bp.accountCode = :accountCode AND bp.currentProgress >= bp.targetProgress")
    List<BadgeProgress> findCompletedProgressByUserAndAccount(@Param("userId") Long userId, @Param("accountCode") String accountCode);
    
    @Query("SELECT COUNT(*) FROM BadgeProgress bp WHERE bp.userId = :userId AND bp.accountCode = :accountCode")
    Long countByUserIdAndAccountCode(@Param("userId") Long userId, @Param("accountCode") String accountCode);
}

@Repository
public interface BadgeRecordRepository extends JpaRepository<BadgeRecord, Long> {
    
    List<BadgeRecord> findByUserIdOrderByOperationTimeDesc(Long userId);
    
    List<BadgeRecord> findByUserIdAndAccountCodeOrderByOperationTimeDesc(Long userId, String accountCode);
    
    List<BadgeRecord> findByUserIdAndBadgeIdOrderByOperationTimeDesc(Long userId, Long badgeId);
    
    List<BadgeRecord> findByOperationTypeAndOperationTimeBetween(BadgeOperationType operationType, LocalDateTime startTime, LocalDateTime endTime);
    
    Optional<BadgeRecord> findByTransactionId(String transactionId);
    
    @Query("SELECT br FROM BadgeRecord br WHERE br.userId = :userId AND br.operationType = :operationType ORDER BY br.operationTime DESC")
    List<BadgeRecord> findByUserIdAndOperationType(@Param("userId") Long userId, @Param("operationType") BadgeOperationType operationType);
    
    @Query("SELECT COUNT(*) FROM BadgeRecord br WHERE br.operationType = :operationType AND br.operationTime BETWEEN :startTime AND :endTime")
    Long countByOperationTypeAndTimeBetween(@Param("operationType") BadgeOperationType operationType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT br FROM BadgeRecord br WHERE br.accountCode = :accountCode AND br.operationTime BETWEEN :startTime AND :endTime ORDER BY br.operationTime DESC")
    List<BadgeRecord> findByAccountCodeAndTimeBetween(@Param("accountCode") String accountCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
```

### 4.2 数据传输对象
```java
/**
 * 勋章获得条件配置
 */
@Data
public class BadgeGainCondition {
    private String type;
    private BigDecimal targetValue;
    private String actionType;
    private Integer timeRange;
    private List<String> eventTypes;
    private Map<String, Object> extraConditions;
}

/**
 * 勋章奖励配置
 */
@Data
public class BadgeRewardConfig {
    private Long pointReward;
    private List<String> itemRewards;
    private Map<String, Object> customRewards;
}

/**
 * 勋章检测结果
 */
@Data
@Builder
public class BadgeDetectionResult {
    private Long userId;
    private Long badgeId;
    private String accountCode;
    private String eventType;
    private Map<String, Object> eventData;
    private BadgeConditionResult conditionResult;
}

/**
 * 勋章条件评估结果
 */
@Data
@Builder
public class BadgeConditionResult {
    private boolean met;
    private boolean hasProgress;
    private Long currentProgress;
    private Long targetProgress;
    private String progressDataJson;
    
    public static BadgeConditionResult met(Long currentProgress, Long targetProgress) {
        return BadgeConditionResult.builder()
            .met(true)
            .hasProgress(true)
            .currentProgress(currentProgress)
            .targetProgress(targetProgress)
            .build();
    }
    
    public static BadgeConditionResult progress(Long currentProgress, Long targetProgress) {
        return BadgeConditionResult.builder()
            .met(false)
            .hasProgress(true)
            .currentProgress(currentProgress)
            .targetProgress(targetProgress)
            .build();
    }
    
    public static BadgeConditionResult notMet() {
        return BadgeConditionResult.builder()
            .met(false)
            .hasProgress(false)
            .build();
    }
}

/**
 * 发放勋章请求
 */
@Data
@Builder
public class AwardBadgeRequest {
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @NotNull(message = "勋章ID不能为空")
    private Long badgeId;
    
    @NotBlank(message = "账户代码不能为空")
    private String accountCode;
    
    @NotBlank(message = "来源不能为空")
    private String source;
    
    private String businessId;
    private Map<String, Object> extraData;
}

/**
 * 勋章发放结果
 */
@Data
@Builder
public class BadgeAwardResult {
    private Long userBadgeId;
    private String badgeCode;
    private String badgeName;
    private LocalDateTime obtainTime;
}

/**
 * 更新勋章展示请求
 */
@Data
public class UpdateBadgeDisplayRequest {
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @NotNull(message = "用户勋章ID不能为空")
    private Long userBadgeId;
    
    @NotNull(message = "是否展示不能为空")
    private Boolean isDisplayed;
    
    private Integer displayOrder;
}
```

## 5. 缓存策略

### 5.1 缓存配置
```java
@Configuration
@EnableCaching
public class BadgeCacheConfig {
    
    @Bean
    public CacheManager badgeCacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .build();
    }
}
```

### 5.2 缓存服务
```java
@Service
public class BadgeCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String USER_BADGES_KEY = "badge:user:%d";
    private static final String BADGE_CONFIG_KEY = "badge:config:%d";
    private static final String BADGE_PROGRESS_KEY = "badge:progress:%d";
    
    public void cacheUserBadges(Long userId, List<UserBadge> badges) {
        String key = String.format(USER_BADGES_KEY, userId);
        redisTemplate.opsForValue().set(key, badges, Duration.ofHours(1));
    }
    
    public List<UserBadge> getUserBadgesFromCache(Long userId) {
        String key = String.format(USER_BADGES_KEY, userId);
        return (List<UserBadge>) redisTemplate.opsForValue().get(key);
    }
    
    public void clearUserBadgesCache(Long userId) {
        String key = String.format(USER_BADGES_KEY, userId);
        redisTemplate.delete(key);
    }
    
    public void cacheBadgeConfig(BadgeConfig config) {
        String key = String.format(BADGE_CONFIG_KEY, config.getId());
        redisTemplate.opsForValue().set(key, config, Duration.ofHours(24));
    }
    
    public BadgeConfig getBadgeConfigFromCache(Long badgeId) {
        String key = String.format(BADGE_CONFIG_KEY, badgeId);
        return (BadgeConfig) redisTemplate.opsForValue().get(key);
    }
}
```

## 6. 异常处理

### 6.1 异常定义
```java
public class BadgeException extends RuntimeException {
    private final String errorCode;
    
    public BadgeException(String message) {
        super(message);
        this.errorCode = "BADGE_ERROR";
    }
    
    public BadgeException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public static class BadgeConfigNotFoundException extends BadgeException {
        public BadgeConfigNotFoundException(String message) {
            super("BADGE_CONFIG_NOT_FOUND", message);
        }
    }
    
    public static class BadgeAlreadyObtainedException extends BadgeException {
        public BadgeAlreadyObtainedException(String message) {
            super("BADGE_ALREADY_OBTAINED", message);
        }
    }
    
    public static class BadgeObtainLimitExceededException extends BadgeException {
        public BadgeObtainLimitExceededException(String message) {
            super("BADGE_OBTAIN_LIMIT_EXCEEDED", message);
        }
    }
    
    public static class BadgeConditionNotMetException extends BadgeException {
        public BadgeConditionNotMetException(String message) {
            super("BADGE_CONDITION_NOT_MET", message);
        }
    }
}
```

### 6.2 全局异常处理
```java
@RestControllerAdvice
public class BadgeExceptionHandler {
    
    @ExceptionHandler(BadgeException.class)
    public Result<Void> handleBadgeException(BadgeException e) {
        log.warn("勋章业务异常: {}", e.getMessage());
        return Result.error(e.getErrorCode(), e.getMessage());
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));
        return Result.error("VALIDATION_ERROR", "参数校验失败: " + message);
    }
}
```

## 7. 监控和日志

### 7.1 监控指标
```java
@Component
public class BadgeMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter badgeAwardedCounter;
    private final Counter badgeDetectionCounter;
    private final Timer badgeDetectionTimer;
    private final Gauge totalBadgesGauge;
    
    public BadgeMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.badgeAwardedCounter = Counter.builder("badge.awarded.total")
            .description("Total badges awarded")
            .register(meterRegistry);
        this.badgeDetectionCounter = Counter.builder("badge.detection.total")
            .description("Total badge detections")
            .register(meterRegistry);
        this.badgeDetectionTimer = Timer.builder("badge.detection.time")
            .description("Badge detection time")
            .register(meterRegistry);
        this.totalBadgesGauge = Gauge.builder("badge.total.current")
            .description("Current total badges")
            .register(meterRegistry, this, BadgeMetrics::getTotalBadges);
    }
    
    public void recordBadgeAwarded(String badgeCode, String source) {
        badgeAwardedCounter.increment(Tags.of("badge_code", badgeCode, "source", source));
    }
    
    public void recordBadgeDetection(String eventType, int detectedCount) {
        badgeDetectionCounter.increment(Tags.of("event_type", eventType, "detected_count", String.valueOf(detectedCount)));
    }
    
    public void recordDetectionTime(Duration duration) {
        badgeDetectionTimer.record(duration);
    }
    
    private double getTotalBadges() {
        // 实现获取总勋章数的逻辑
        return 0.0;
    }
}
```

### 7.2 业务日志
```java
@Component
@Slf4j
public class BadgeLogger {
    
    public void logBadgeAwarded(BadgeAwardResult result) {
        log.info("勋章发放: userId={}, badgeCode={}, badgeName={}, rewardPoint={}", 
            result.getUserId(),
            result.getBadgeCode(),
            result.getBadgeName(),
            result.getRewardPoint());
    }
    
    public void logBadgeDetection(Long userId, String eventType, int detectedCount) {
        log.info("勋章检测: userId={}, eventType={}, detectedCount={}", 
            userId, eventType, detectedCount);
    }
    
    public void logBadgeProgressUpdate(Long userId, Long badgeId, long currentProgress, long targetProgress) {
        log.info("勋章进度更新: userId={}, badgeId={}, progress={}/{}", 
            userId, badgeId, currentProgress, targetProgress);
    }
    
    public void logBadgeConditionEvaluation(Long userId, Long badgeId, String conditionType, boolean result) {
        log.debug("勋章条件评估: userId={}, badgeId={}, conditionType={}, result={}", 
            userId, badgeId, conditionType, result);
    }
}
```

## 8. 定时任务

### 8.1 勋章过期处理
```java
@Component
public class BadgeScheduledTasks {
    
    @Autowired
    private UserBadgeRepository userBadgeRepository;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void processExpiredBadges() {
        log.info("开始处理过期勋章");
        
        LocalDateTime now = LocalDateTime.now();
        List<UserBadge> expiredBadges = userBadgeRepository.findExpiredBadges(now, UserBadgeStatus.VALID);
        
        for (UserBadge badge : expiredBadges) {
            try {
                badge.setStatus(UserBadgeStatus.EXPIRED);
                userBadgeRepository.save(badge);
                
                // 发布勋章过期事件
                eventPublisher.publishEvent(new BadgeExpiredEvent(
                    badge.getUserId(),
                    badge.getBadgeId(),
                    badge.getId()
                ));
                
                log.info("勋章已过期: userId={}, badgeId={}, userBadgeId={}", 
                    badge.getUserId(), badge.getBadgeId(), badge.getId());
                
            } catch (Exception e) {
                log.error("处理过期勋章失败: userBadgeId={}", badge.getId(), e);
            }
        }
        
        log.info("过期勋章处理完成，共处理{}个", expiredBadges.size());
    }
    
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void processCompletedProgress() {
        log.debug("开始处理已完成的勋章进度");
        
        List<BadgeProgress> completedProgress = badgeProgressRepository.findCompletedProgress();
        
        for (BadgeProgress progress : completedProgress) {
            try {
                // 自动发放勋章
                AwardBadgeRequest request = AwardBadgeRequest.builder()
                    .userId(progress.getUserId())
                    .badgeId(progress.getBadgeId())
                    .source("AUTO_PROGRESS")
                    .build();
                
                badgeService.awardBadge(request);
                
                log.info("基于进度自动发放勋章: userId={}, badgeId={}", 
                    progress.getUserId(), progress.getBadgeId());
                
            } catch (Exception e) {
                log.error("自动发放勋章失败: userId={}, badgeId={}", 
                    progress.getUserId(), progress.getBadgeId(), e);
            }
        }
    }
}
```

## 9. 测试用例

### 9.1 单元测试
```java
@ExtendWith(MockitoExtension.class)
class BadgeServiceTest {
    
    @Mock
    private BadgeConfigRepository badgeConfigRepository;
    
    @Mock
    private UserBadgeRepository userBadgeRepository;
    
    @Mock
    private BadgeDetectionEngine detectionEngine;
    
    @InjectMocks
    private BadgeService badgeService;
    
    @Test
    void testAwardBadge_Success() {
        // Given
        Long userId = 1L;
        Long badgeId = 1L;
        AwardBadgeRequest request = AwardBadgeRequest.builder()
            .userId(userId)
            .badgeId(badgeId)
            .source("TEST")
            .build();
        
        BadgeConfig badgeConfig = new BadgeConfig();
        badgeConfig.setId(badgeId);
        badgeConfig.setBadgeCode("TEST_BADGE");
        badgeConfig.setBadgeName("测试勋章");
        badgeConfig.setStatus(BadgeStatus.ACTIVE);
        badgeConfig.setIsRepeatable(false);
        badgeConfig.setRewardPoint(100L);
        
        when(badgeConfigRepository.findById(badgeId)).thenReturn(Optional.of(badgeConfig));
        when(userBadgeRepository.existsByUserIdAndBadgeIdAndStatus(userId, badgeId, UserBadgeStatus.VALID))
            .thenReturn(false);
        
        // When
        BadgeAwardResult result = badgeService.awardBadge(request);
        
        // Then
        assertNotNull(result);
        assertEquals("TEST_BADGE", result.getBadgeCode());
        assertEquals("测试勋章", result.getBadgeName());
        assertEquals(100L, result.getRewardPoint());
        verify(userBadgeRepository).save(any(UserBadge.class));
    }
    
    @Test
    void testAwardBadge_AlreadyObtained() {
        // Given
        Long userId = 1L;
        Long badgeId = 1L;
        AwardBadgeRequest request = AwardBadgeRequest.builder()
            .userId(userId)
            .badgeId(badgeId)
            .source("TEST")
            .build();
        
        BadgeConfig badgeConfig = new BadgeConfig();
        badgeConfig.setId(badgeId);
        badgeConfig.setStatus(BadgeStatus.ACTIVE);
        badgeConfig.setIsRepeatable(false);
        
        when(badgeConfigRepository.findById(badgeId)).thenReturn(Optional.of(badgeConfig));
        when(userBadgeRepository.existsByUserIdAndBadgeIdAndStatus(userId, badgeId, UserBadgeStatus.VALID))
            .thenReturn(true);
        
        // When & Then
        assertThrows(BadgeException.class, () -> badgeService.awardBadge(request));
    }
}
```

### 9.2 集成测试
```java
@MicronautTest
@Transactional
class BadgeServiceIntegrationTest {
    
    @Autowired
    private BadgeService badgeService;
    
    @Autowired
    private BadgeConfigRepository badgeConfigRepository;
    
    @Autowired
    private UserBadgeRepository userBadgeRepository;
    
    @Test
    void testAwardBadge_Integration() {
        // Given
        BadgeConfig badgeConfig = new BadgeConfig();
        badgeConfig.setBadgeCode("INTEGRATION_TEST");
        badgeConfig.setBadgeName("集成测试勋章");
        badgeConfig.setBadgeType(BadgeType.ACHIEVEMENT);
        badgeConfig.setBadgeCategory(BadgeCategory.PURCHASE);
        badgeConfig.setStatus(BadgeStatus.ACTIVE);
        badgeConfig.setIsRepeatable(false);
        badgeConfig.setRewardPoint(50L);
        badgeConfig = badgeConfigRepository.save(badgeConfig);
        
        Long userId = 999L;
        AwardBadgeRequest request = AwardBadgeRequest.builder()
            .userId(userId)
            .badgeId(badgeConfig.getId())
            .source("INTEGRATION_TEST")
            .build();
        
        // When
        BadgeAwardResult result = badgeService.awardBadge(request);
        
        // Then
        assertNotNull(result);
        assertEquals("INTEGRATION_TEST", result.getBadgeCode());
        
        List<UserBadge> userBadges = userBadgeRepository.findByUserIdAndStatusOrderByObtainTimeDesc(
            userId, UserBadgeStatus.VALID);
        assertEquals(1, userBadges.size());
        assertEquals(badgeConfig.getId(), userBadges.get(0).getBadgeId());
    }
}
```

## 10. 性能优化

### 10.1 数据库优化
```sql
-- 用户勋章表索引
CREATE INDEX idx_user_badge_user_id ON user_badge(user_id, status);
CREATE INDEX idx_user_badge_badge_id ON user_badge(badge_id);
CREATE INDEX idx_user_badge_obtain_time ON user_badge(obtain_time);
CREATE INDEX idx_user_badge_expire_time ON user_badge(expire_time, status);

-- 勋章进度表索引
CREATE INDEX idx_badge_progress_user_id ON badge_progress(user_id);
CREATE INDEX idx_badge_progress_badge_id ON badge_progress(badge_id);
CREATE INDEX idx_badge_progress_completed ON badge_progress(current_progress, target_progress);

-- 勋章配置表索引
CREATE INDEX idx_badge_config_type ON badge_config(badge_type, status);
CREATE INDEX idx_badge_config_category ON badge_config(badge_category, status);
CREATE INDEX idx_badge_config_time ON badge_config(start_time, end_time, status);
```

### 10.2 批量处理优化
```java
@Component
public class BadgeBatchProcessor {
    
    @Autowired
    private BadgeService badgeService;
    
    public void batchDetectAndAwardBadges(List<BadgeDetectionTask> tasks) {
        // 按用户ID分组
        Map<Long, List<BadgeDetectionTask>> tasksByUser = tasks.stream()
            .collect(Collectors.groupingBy(BadgeDetectionTask::getUserId));
        
        // 并行处理
        tasksByUser.entrySet().parallelStream().forEach(entry -> {
            Long userId = entry.getKey();
            List<BadgeDetectionTask> userTasks = entry.getValue();
            
            try {
                for (BadgeDetectionTask task : userTasks) {
                    badgeService.detectAndAwardBadges(
                        userId, 
                        task.getEventType(), 
                        task.getEventData()
                    );
                }
            } catch (Exception e) {
                log.error("批量处理用户勋章失败: userId={}", userId, e);
            }
        });
    }
}
```

## 11. 配置管理

### 11.1 应用配置
```yaml
# application.yml
badge:
  detection:
    enabled: true
    batch-size: 50
    thread-pool-size: 5
  cache:
    ttl: 3600 # 1小时
    max-size: 5000
  award:
    auto-award-enabled: true
    max-retry-count: 3
  progress:
    update-batch-size: 100
    cleanup-days: 90
```

### 11.2 勋章配置管理
```java
@Component
public class BadgeConfigManager {
    
    @Autowired
    private BadgeConfigRepository configRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CONFIG_CACHE_KEY = "badge:configs:active";
    
    @Cacheable(value = "badgeConfigs", key = "'active'")
    public List<BadgeConfig> getAllActiveConfigs() {
        return configRepository.findByStatus(BadgeStatus.ACTIVE);
    }
    
    @Scheduled(fixedRate = 600000) // 10分钟刷新一次
    public void refreshConfigCache() {
        redisTemplate.delete(CONFIG_CACHE_KEY);
        getAllActiveConfigs(); // 重新加载到缓存
    }
    
    public void updateConfig(BadgeConfig config) {
        configRepository.save(config);
        refreshConfigCache();
    }
}
```

## 12. 总结

勋章服务作为账户体系的激励模块，具有以下特点：

### 12.1 技术特点
- **智能检测引擎**：支持复杂的成就条件检测和进度跟踪
- **灵活配置管理**：支持多种勋章类型和获得条件的配置
- **高效批量处理**：通过异步处理和批量操作保证高性能
- **完善的缓存策略**：多级缓存提升系统响应速度

### 12.2 业务价值
- **用户激励**：通过勋章系统激励用户完成各种行为
- **成就展示**：为用户提供荣誉展示和社交分享功能
- **行为引导**：通过勋章设计引导用户进行期望的行为
- **数据分析**：提供用户行为数据用于运营分析

### 12.3 扩展性
- **条件扩展**：支持新的成就条件类型和评估逻辑
- **奖励扩展**：可以轻松添加新的奖励类型
- **集成扩展**：提供标准接口便于与其他系统集成
- **性能扩展**：支持水平扩展和分布式部署

勋章服务为整个账户体系提供了丰富的激励机制和用户价值体现方式。
