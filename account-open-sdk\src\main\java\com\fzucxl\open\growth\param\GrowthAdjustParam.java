﻿package com.fzucxl.open.growth.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值调整参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAdjustParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 调整类型：INCREASE(增加), DECREASE(减少)
     */
    private String adjustType;    
    /**
     * 调整数量
     */
    private Long amount;    
    /**
     * 调整原因
     */
    private String adjustReason;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
