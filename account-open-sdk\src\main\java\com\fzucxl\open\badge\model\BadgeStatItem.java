﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章统计项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeStatItem extends Extensible {    
    /**
     * 统计日期
     */
    private String date;    
    /**
     * 发放数量
     */
    private Long awardCount;    
    /**
     * 撤销数量
     */
    private Long revokeCount;    
    /**
     * 过期数量
     */
    private Long expireCount;    
    /**
     * 活跃数量
     */
    private Long activeCount;}
