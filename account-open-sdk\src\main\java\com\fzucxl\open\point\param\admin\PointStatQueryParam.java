﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分统计参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointStatQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 统计类型
     */
    private String statType;    
    /**
     * 分组方式
     */
    private String groupBy;}
