﻿package com.fzucxl.open.growth.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值账户详情结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAccountDetailQueryResult extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 总成长值
     */
    private Long totalGrowth;    
    /**
     * 可用成长值
     */
    private Long availableGrowth;    
    /**
     * 冻结成长值
     */
    private Long frozenGrowth;    
    /**
     * 账户状态
     */
    private String status;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;    
    /**
     * 最近交易记录
     */
    private java.util.List<GrowthTransaction> recentTransactionList;}
