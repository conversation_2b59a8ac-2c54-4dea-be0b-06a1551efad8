﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章详情参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeDetailQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 交易ID
     */
    private String transactionId;}
