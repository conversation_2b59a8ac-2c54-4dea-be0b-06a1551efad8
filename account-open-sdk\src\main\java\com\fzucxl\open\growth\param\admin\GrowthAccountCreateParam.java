﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建成长值账户参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAccountCreateParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 初始成长值，默认0
     */
    private Long initialGrowth;    
    /**
     * 备注信息
     */
    private String remark;}
