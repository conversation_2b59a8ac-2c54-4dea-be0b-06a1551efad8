﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级计算项
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelCalculateItem extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 计算得出的等级
     */
    private Integer calculatedLevel;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 当前计算值
     */
    private Long currentValue;    
    /**
     * 下一等级所需值
     */
    private Long nextLevelValue;    
    /**
     * 升级进度百分比
     */
    private Double progress;    
    /**
     * 计算时间
     */
    private String calculateTime;}
