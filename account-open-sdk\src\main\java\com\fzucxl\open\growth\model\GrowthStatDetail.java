﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值统计明细模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthStatDetail extends Extensible {    
    /**
     * 日期
     */
    private String date;    
    /**
     * 总成长值
     */
    private Long totalGrowth;    
    /**
     * 获得成长值
     */
    private Long earnGrowth;    
    /**
     * 调整成长值
     */
    private Long adjustGrowth;    
    /**
     * 用户数量
     */
    private Long userCount;}
