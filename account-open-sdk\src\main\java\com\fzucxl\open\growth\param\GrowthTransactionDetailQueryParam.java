﻿package com.fzucxl.open.growth.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值交易详情参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthTransactionDetailQueryParam extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 账户代码
     */
    private String accountCode;}
