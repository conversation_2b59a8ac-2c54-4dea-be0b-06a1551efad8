package com.fzucxl.entity.point;

import com.fzucxl.open.base.point.FreezeRecordStatus;
import com.fzucxl.open.base.point.PointTransactionType;
import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分冻结记录实体
 * 
 * <AUTHOR>
 */
@MappedEntity("point_freeze_record")
@Data
public class PointFreezeRecord {
    
    @Id
    @GeneratedValue
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */
    private String brandCode;
    /**
     * 冻结积分
     */
    private Long frozenPoint;
    /**
     * 冻结时间
     */
    private LocalDateTime freezeTime;

    /**
     * 解冻时间
     */
    private LocalDateTime unfreezeTime;
    /**
     * 冻结截止时间
     */
    private LocalDateTime freezeDeadline;
    /**
     * 核销时间
     */
    private LocalDateTime writeoffTime;
    /**
     * 冻结交易ID
     */
    private String freezeTransactionId;
    /**
     * 解冻交易ID
     */
    private String unfreezeTransactionId;
    /**
     * 核销交易ID
     */
    private String writeoffTransactionId;
    /**
     * 冻结状态
     */
    private FreezeRecordStatus status = FreezeRecordStatus.FROZEN;

    /**
     * 冻结原因
     */
    private String freezeReason;
    /**
     * 交易类型
     */
    private PointTransactionType transactionType;
    /**
     * 业务ID
     */
    private String businessId;
    /**
     * 业务类型
     */
    private Long operatorId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 版本号
     */
    private Integer version = 0;
}