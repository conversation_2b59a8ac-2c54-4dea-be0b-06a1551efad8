﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分消费统计详情模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointConsumeStatDetail extends Extensible {    
    /**
     * 统计日期
     */
    private String statDate;    
    /**
     * 消费来源
     */
    private String source;    
    /**
     * 来源名称
     */
    private String sourceName;    
    /**
     * 消费积分
     */
    private Long totalConsumed;    
    /**
     * 用户数
     */
    private Integer totalUser;    
    /**
     * 平均消费积分
     */
    private Double avgConsumed;}
