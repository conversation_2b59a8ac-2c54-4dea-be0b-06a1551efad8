# 成长值模块PRD产品设计文档

## 1. 核心概念

### 1.1 成长值定义
成长值是衡量用户在平台成长历程的综合指标，反映用户的活跃度、忠诚度和贡献度。成长值是等级提升的基础，同时也是用户价值评估的重要依据。

### 1.2 成长值特性
- **累积性**：成长值只增不减，体现用户历史贡献
- **多维度**：从消费、活跃、社交等多个维度综合计算
- **时效性**：部分成长值获取有时间窗口限制
- **权重性**：不同行为的成长值权重不同

## 2. 模型结构设计

### 2.1 成长值账户模型
```
GrowthAccount {
    userId: Long              // 用户ID
    totalGrowth: Long         // 总成长值
    consumeGrowth: Long       // 消费成长值
    activityGrowth: Long      // 活跃成长值
    socialGrowth: Long        // 社交成长值
    taskGrowth: Long          // 任务成长值
    lastUpdateTime: DateTime  // 最后更新时间
    createTime: DateTime      // 创建时间
}
```

### 2.2 成长值明细模型
```
GrowthDetail {
    id: Long              // 明细ID
    userId: Long          // 用户ID
    growthType: String    // 成长值类型
    growthValue: Long     // 成长值数量
    source: String        // 来源
    sourceId: String      // 来源ID
    description: String   // 描述
    earnTime: DateTime    // 获得时间
    createTime: DateTime  // 创建时间
}
```

### 2.3 成长值规则模型
```
GrowthRule {
    id: Long              // 规则ID
    ruleName: String      // 规则名称
    ruleType: String      // 规则类型
    growthValue: Long     // 成长值数量
    maxDaily: Long        // 每日上限
    maxMonthly: Long      // 每月上限
    weight: Double        // 权重系数
    validPeriod: String   // 有效期类型
    status: String        // 状态
    startTime: DateTime   // 开始时间
    endTime: DateTime     // 结束时间
}
```

### 2.4 成长值统计模型
```
GrowthStatistics {
    userId: Long          // 用户ID
    statDate: Date        // 统计日期
    dailyGrowth: Long     // 当日成长值
    weeklyGrowth: Long    // 本周成长值
    monthlyGrowth: Long   // 本月成长值
    yearlyGrowth: Long    // 本年成长值
    createTime: DateTime  // 创建时间
}
```

## 3. 业务场景

### 3.1 成长值获取场景

#### 3.1.1 消费类成长值
- **订单完成**：完成订单后按金额比例获得成长值
- **首次消费**：新用户首次消费获得额外成长值奖励
- **大额消费**：单笔消费达到一定金额获得成长值加成
- **连续消费**：连续多天消费获得成长值奖励

#### 3.1.2 活跃类成长值
- **每日签到**：连续签到获得递增成长值
- **浏览商品**：每日浏览商品获得成长值（有上限）
- **搜索行为**：进行商品搜索获得成长值
- **收藏商品**：收藏商品获得成长值

#### 3.1.3 社交类成长值
- **发表评价**：对商品进行评价获得成长值
- **分享商品**：分享商品到社交平台获得成长值
- **邀请好友**：成功邀请好友注册获得成长值
- **参与讨论**：在社区参与讨论获得成长值

#### 3.1.4 任务类成长值
- **完成任务**：完成平台发布的各类任务获得成长值
- **参与活动**：参与平台活动获得成长值
- **问卷调研**：参与问卷调研获得成长值

### 3.2 成长值应用场景
- **等级计算**：成长值作为用户等级提升的依据
- **用户画像**：基于成长值构建用户价值画像
- **个性化推荐**：根据成长值类型优化推荐策略
- **营销策略**：针对不同成长值用户制定差异化营销

### 3.3 成长值分析场景
- **成长轨迹**：分析用户成长值变化趋势
- **行为偏好**：基于成长值类型分析用户偏好
- **价值评估**：综合成长值评估用户价值
- **流失预警**：基于成长值变化预测用户流失风险

## 4. API列表设计

### 4.1 成长值查询接口
```
GET /api/v1/growth/account
功能：查询用户成长值账户
参数：userId
返回：成长值账户信息
```

```
GET /api/v1/growth/detail
功能：查询成长值明细
参数：userId, growthType, pageNum, pageSize, startTime, endTime
返回：成长值明细列表
```

```
GET /api/v1/growth/statistics
功能：查询成长值统计
参数：userId, statType, startTime, endTime
返回：统计数据
```

### 4.2 成长值操作接口
```
POST /api/v1/growth/earn
功能：用户获得成长值
参数：userId, growthType, growthValue, source, description
返回：操作结果
```

```
POST /api/v1/growth/batch-earn
功能：批量发放成长值
参数：用户列表, 成长值信息
返回：批量操作结果
```

### 4.3 成长值规则接口
```
GET /api/v1/growth/rule
功能：查询成长值规则
参数：ruleType, status
返回：规则列表
```

```
POST /api/v1/growth/rule
功能：创建成长值规则
参数：规则信息
返回：创建结果
```

```
PUT /api/v1/growth/rule/{ruleId}
功能：更新成长值规则
参数：ruleId, 规则信息
返回：更新结果
```

### 4.4 成长值分析接口
```
GET /api/v1/growth/trend
功能：查询成长值趋势
参数：userId, period
返回：趋势数据
```

```
GET /api/v1/growth/ranking
功能：查询成长值排行榜
参数：rankType, period, limit
返回：排行榜数据
```

```
GET /api/v1/growth/distribution
功能：查询成长值分布
参数：startTime, endTime, groupBy
返回：分布数据
```

## 5. 成长值计算规则

### 5.1 消费成长值计算
```
基础公式：消费成长值 = 消费金额 × 基础系数 × 等级系数 × 活动系数

基础系数：1成长值/1元
等级系数：
- 普通用户：1.0
- 银卡用户：1.1
- 金卡用户：1.2
- 钻石用户：1.5

活动系数：根据促销活动动态调整
```

### 5.2 活跃成长值计算
```
签到成长值：
- 连续签到1-7天：每天10成长值
- 连续签到8-15天：每天15成长值
- 连续签到16-30天：每天20成长值
- 连续签到30天以上：每天25成长值

浏览成长值：
- 每浏览1个商品：1成长值
- 每日上限：50成长值
```

### 5.3 社交成长值计算
```
评价成长值：
- 文字评价：20成长值
- 图片评价：30成长值
- 视频评价：50成长值

分享成长值：
- 分享商品：5成长值/次
- 分享被点击：额外2成长值/次
- 每日上限：100成长值
```

## 6. 技术实现要点

### 6.1 成长值计算引擎
- 支持多维度成长值计算
- 实现规则引擎动态配置
- 支持成长值的实时和批量计算

### 6.2 数据统计分析
- 实时统计用户成长值变化
- 支持多维度数据分析
- 提供成长值趋势预测

### 6.3 性能优化
- 成长值账户信息缓存
- 异步处理成长值计算
- 分表存储成长值明细数据
- 定时任务处理统计数据

### 6.4 数据一致性
- 使用事务保证成长值操作原子性
- 建立成长值变更的审计日志
- 实现成长值数据的定期校验