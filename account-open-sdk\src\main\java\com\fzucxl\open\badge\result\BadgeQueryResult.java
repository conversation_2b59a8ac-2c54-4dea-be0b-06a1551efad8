﻿package com.fzucxl.open.badge.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户勋章结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeQueryResult extends Extensible {    
    /**
     * 用户勋章列表
     */
    private java.util.List<UserBadge> badgeList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
