﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级配置
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelConfig extends Extensible {    
    /**
     * 等级
     */
    private Integer level;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 等级图标
     */
    private String levelIcon;    
    /**
     * 最小值
     */
    private Long minValue;    
    /**
     * 最大值
     */
    private Long maxValue;    
    /**
     * 等级描述
     */
    private String description;}
