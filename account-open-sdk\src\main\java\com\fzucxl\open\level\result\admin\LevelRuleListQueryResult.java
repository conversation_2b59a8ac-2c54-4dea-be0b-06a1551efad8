﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级规则列表结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleListQueryResult extends Extensible {    
    /**
     * 等级规则列表
     */
    private java.util.List<LevelRule> ruleList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
