﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级变更记录
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRecord extends Extensible {    
    /**
     * 记录ID
     */
    private String recordId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 变更类型：UPGRADE(升级), DOWNGRADE(降级)
     */
    private String changeType;    
    /**
     * 变更前等级
     */
    private Integer beforeLevel;    
    /**
     * 变更后等级
     */
    private Integer afterLevel;    
    /**
     * 变更原因
     */
    private String changeReason;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 创建时间
     */
    private String createTime;}
