﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 冻结积分参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointFreezeParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 冻结积分数量
     */
    private Long point;    
    /**
     * 冻结类型
     */
    private String freezeType;    
    /**
     * 冻结原因
     */
    private String reason;    
    /**
     * 冻结过期时间
     */
    private String expireTime;    
    /**
     * 是否自动解冻
     */
    private Boolean autoUnfreeze;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
