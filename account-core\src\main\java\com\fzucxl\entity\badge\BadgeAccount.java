package com.fzucxl.entity.badge;

import com.fzucxl.open.base.AccountStatus;
import com.fzucxl.open.base.badge.BadgeCategory;
import com.fzucxl.open.base.badge.BadgeType;
import io.micronaut.data.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("badge_account")
@Data
public class BadgeAccount {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 账户代码，唯一标识
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 勋章类型
     */
    private BadgeType badgeType;

    /**
     * 勋章类别
     */
    private BadgeCategory badgeCategory;

    /**
     * 账户状态
     */
    private AccountStatus status = AccountStatus.ACTIVE;

    /**
     * 账户描述
     */
    private String description;

    /**
     * 账户配置
     */
    private String basicConfig;

    /**
     * 获取条件
     */
    private String gainCondition;

    /**
     * 奖励配置
     */
    private String rewardConfig;

    /**
     * 设计配置
     */
    private String designConfig;

    /**
     * 显示配置
     */
    private String displayConfig;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
