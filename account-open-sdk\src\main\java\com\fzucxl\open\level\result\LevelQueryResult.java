﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户等级结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelQueryResult extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 当前等级
     */
    private Integer currentLevel;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 等级图标
     */
    private String levelIcon;    
    /**
     * 当前计算值（积分或成长值）
     */
    private Long currentValue;    
    /**
     * 下一等级所需值
     */
    private Long nextLevelValue;    
    /**
     * 升级进度百分比
     */
    private Double progress;    
    /**
     * 计算方式：POINT(积分), GROWTH(成长值)
     */
    private String calculationType;    
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;}
