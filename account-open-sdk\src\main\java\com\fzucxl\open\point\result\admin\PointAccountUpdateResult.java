﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新积分账户结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccountUpdateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 账户ID
     */
    private String accountId;    
    /**
     * 更新时间
     */
    private String updateTime;}
