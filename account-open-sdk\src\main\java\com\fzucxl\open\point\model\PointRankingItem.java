package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分排行榜项目模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRankingItem extends Extensible {
    
    /**
     * 排名
     */
    private Integer rank;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 账户代码
     */
    private String accountCode;
    
    /**
     * 积分数量
     */
    private Long point;
    
    /**
     * 排行榜类型
     */
    private String rankingType;
    
    /**
     * 统计时间
     */
    private String statTime;
}