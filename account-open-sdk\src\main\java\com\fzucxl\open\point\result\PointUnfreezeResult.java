﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 解冻积分结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointUnfreezeResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;    
    /**
     * 解冻记录ID
     */
    private String unfreezeId;    
    /**
     * 实际解冻的积分数量
     */
    private Long point;    
    /**
     * 解冻前可用余额
     */
    private Long beforeBalance;    
    /**
     * 解冻后可用余额
     */
    private Long afterBalance;    
    /**
     * 解冻后冻结余额
     */
    private Long frozenBalance;    
    /**
     * 处理状态
     */
    private String status;}
