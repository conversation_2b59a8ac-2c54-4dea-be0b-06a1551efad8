﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级分布
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelDistribution extends Extensible {    
    /**
     * 等级
     */
    private Integer level;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 用户数量
     */
    private Long userCount;    
    /**
     * 占比百分比
     */
    private Double percentage;}
