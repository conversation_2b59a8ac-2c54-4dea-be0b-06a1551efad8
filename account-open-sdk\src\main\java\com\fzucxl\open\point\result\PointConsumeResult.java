﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 消费积分结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointConsumeResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;    
    /**
     * 积分明细记录ID
     */
    private String detailId;    
    /**
     * 实际消费的积分数量
     */
    private Long point;    
    /**
     * 消费前余额
     */
    private Long beforeBalance;    
    /**
     * 消费后余额
     */
    private Long afterBalance;    
    /**
     * 消费明细（按过期时间优先扣减）
     */
    private java.util.List<ConsumedDetail> consumedDetail;    
    /**
     * 处理状态
     */
    private String status;}
