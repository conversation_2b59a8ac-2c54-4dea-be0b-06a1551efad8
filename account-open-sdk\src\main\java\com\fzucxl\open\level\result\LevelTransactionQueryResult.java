﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级交易记录结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelTransactionQueryResult extends Extensible {    
    /**
     * 等级交易记录列表
     */
    private java.util.List<LevelTransaction> transactionList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
