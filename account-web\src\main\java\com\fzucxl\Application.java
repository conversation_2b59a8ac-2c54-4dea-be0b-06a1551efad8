package com.fzucxl;

import com.fzucxl.point.rule.resource.AttributeResource;
import com.fzucxl.point.rule.resource.PointRuleResource;
import io.micronaut.openapi.annotation.OpenAPIInclude;
import io.micronaut.runtime.Micronaut;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;

@OpenAPIDefinition(
        info = @Info(
                title = "account-web apis",
                version = "1.0",
                description = "©2025 Copyright. Powered By fzucxl.",
                license = @License(name = "Apache 2.0"),
                contact = @Contact(name = "account-web apis", email = "<EMAIL>")
        )
)
@OpenAPIInclude(classes = {AttributeResource.class,
        PointRuleResource.class})
public class Application {

    public static void main(String[] args) {
        Micronaut.run(Application.class, args);
    }
}