﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 批量查询用户等级参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelBatchQueryParam extends Extensible {    
    /**
     * 用户ID列表
     */
    private java.util.List<String> userIdList;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易ID
     */
    private String transactionId;}
