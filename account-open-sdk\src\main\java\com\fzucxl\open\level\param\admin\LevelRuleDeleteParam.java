﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 删除等级规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleDeleteParam extends Extensible {    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 操作员ID
     */
    private String operatorId;    
    /**
     * 交易ID
     */
    private String transactionId;}
