﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级变更统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelChangeStatQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 分组方式：DAY(按天), WEEK(按周), MONTH(按月)
     */
    private String groupBy;    
    /**
     * 交易ID
     */
    private String transactionId;}
