package com.fzucxl.entity.level;

import com.fzucxl.open.base.level.LevelTransactionType;
import io.micronaut.data.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("level_record")
@Data
public class LevelRecord {
    @Id
    @GeneratedValue
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 交易类型
     */
    private LevelTransactionType transactionType;
    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 源等级
     */
    private Integer fromLevel;

    /**
     * 目标等级
     */
    private Integer toLevel;
    /**
     * 来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则
     */
    private String source;
    /**
     * 渠道类型
     */
    private String channelType;
    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
    /**
     * 失效时间
     */
    private LocalDateTime expireTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 扩展数据
     */
    private String extraData;
}
