﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章进度统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeProgressStatQueryParam extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 进度范围：0-25, 26-50, 51-75, 76-99, 100
     */
    private String progressRange;}
