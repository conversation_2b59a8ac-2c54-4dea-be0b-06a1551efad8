﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章交易记录参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeTransactionQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易类型：AWARD(颁发), REVOKE(撤销)
     */
    private String transactionType;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 交易ID
     */
    private String transactionId;}
