package com.fzucxl.entity.event;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.sql.JoinColumn;
import io.micronaut.serde.annotation.Serdeable;

import java.time.LocalDateTime;

/**
 * 业务事件字段实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedEntity("business_event_field")
@Introspected
@Serdeable
public class BusinessEventField {
    
    @Id
    @GeneratedValue(GeneratedValue.Type.IDENTITY)
    private Long id;
    
    /**
     * 关联的业务事件ID
     */
    private Long eventId;
    
    /**
     * 字段编码
     */
    private String fieldCode;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 字段类型：STRING, INTEGER, LONG, DOUBLE, BOOLEAN, DATE, DATETIME, LIST, JSON
     */
    private String fieldType;
    
    /**
     * 字段描述
     */
    private String description;
    
    /**
     * 是否必填
     */
    private Boolean required = false;
    
    /**
     * 默认值
     */
    private String defaultValue;
    
    /**
     * 字段验证规则（JSON格式）
     */
    private String validationRule;
    
    /**
     * 字段排序
     */
    private Integer sortOrder = 0;
    
    /**
     * 字段状态：ACTIVE-启用，INACTIVE-禁用
     */
    private String status = "ACTIVE";
    
    /**
     * 创建时间
     */
    @DateCreated
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @DateUpdated
    private LocalDateTime updatedAt;
    
    /**
     * 关联的业务事件
     */
    @Relation(value = Relation.Kind.MANY_TO_ONE)
    @JoinColumn(name = "event_id")
    private BusinessEvent businessEvent;
    
    // Constructors
    public BusinessEventField() {}
    
    public BusinessEventField(String fieldCode, String fieldName, String fieldType) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getEventId() {
        return eventId;
    }
    
    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }
    
    public String getFieldCode() {
        return fieldCode;
    }
    
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }
    
    public String getFieldName() {
        return fieldName;
    }
    
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public String getFieldType() {
        return fieldType;
    }
    
    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getRequired() {
        return required;
    }
    
    public void setRequired(Boolean required) {
        this.required = required;
    }
    
    public String getDefaultValue() {
        return defaultValue;
    }
    
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
    
    public String getValidationRule() {
        return validationRule;
    }
    
    public void setValidationRule(String validationRule) {
        this.validationRule = validationRule;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public BusinessEvent getBusinessEvent() {
        return businessEvent;
    }
    
    public void setBusinessEvent(BusinessEvent businessEvent) {
        this.businessEvent = businessEvent;
    }
}