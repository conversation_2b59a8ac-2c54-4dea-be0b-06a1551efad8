﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建等级规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleCreateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 交易ID
     */
    private String transactionId;}
