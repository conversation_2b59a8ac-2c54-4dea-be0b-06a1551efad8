﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户勋章记录参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeUserRecordQueryParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 操作类型：AWARD/REVOKE/EXPIRE/DISPLAY/HIDE
     */
    private String operationType;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页大小
     */
    private Integer pageSize;}
