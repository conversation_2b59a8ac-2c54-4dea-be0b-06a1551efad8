﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值分布统计查询参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthDistributionStatQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 统计类型：RANGE-区间分布，BUSINESS_TYPE-业务类型分布
     */
    private String type;    
    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    private String startDate;    
    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    private String endDate;}
