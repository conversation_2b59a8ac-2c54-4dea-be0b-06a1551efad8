-- 勋章账户表
CREATE TABLE IF NOT EXISTS `badge_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码，唯一标识',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
  `account_name` varchar(200) NOT NULL COMMENT '账户名称',
  `badge_type` varchar(50) NOT NULL COMMENT '勋章类型',
  `badge_category` varchar(50) NOT NULL COMMENT '勋章类别',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态',
  `description` varchar(500) DEFAULT NULL COMMENT '账户描述',
  `basic_config` text DEFAULT NULL COMMENT '账户配置',
  `gain_condition` text DEFAULT NULL COMMENT '获取条件',
  `reward_config` text DEFAULT NULL COMMENT '奖励配置',
  `design_config` text DEFAULT NULL COMMENT '设计配置',
  `display_config` text DEFAULT NULL COMMENT '显示配置',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_code` (`account_code`),
  UNIQUE KEY `uk_account_brand` (`account_code`, `brand_code`),
  KEY `idx_brand_code` (`brand_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='勋章账户表';
-- 勋章进度表
CREATE TABLE IF NOT EXISTS `badge_progress` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `badge_id` bigint(20) NOT NULL COMMENT '勋章ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
  `current_progress` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前进度',
  `target_progress` bigint(20) DEFAULT NULL COMMENT '目标进度',
  `progress_data` text DEFAULT NULL COMMENT '进度数据',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_badge` (`user_id`, `badge_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='勋章进度表';

-- 勋章记录表
CREATE TABLE IF NOT EXISTS `badge_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `badge_id` bigint(20) NOT NULL COMMENT '勋章ID',
    `user_badge_id` bigint(20) DEFAULT NULL COMMENT '勋章账户ID',
    `account_code` varchar(100) NOT NULL COMMENT '账户代码',
    `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
    `account_name` varchar(200) DEFAULT NULL COMMENT '账户名称',
    `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
    `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
    `source` varchar(50) NOT NULL COMMENT '来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则',
    `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
    `transaction_id` varchar(100) NOT NULL COMMENT '事务ID',
    `business_id` varchar(100) NOT NULL COMMENT '业务ID',
    `reason` varchar(500) DEFAULT NULL COMMENT '原因',
    `before_status` varchar(50) DEFAULT NULL COMMENT '变更前状态',
    `after_status` varchar(50) DEFAULT NULL COMMENT '变更后状态',
    `extra_data` text DEFAULT NULL COMMENT '扩展数据（JSON格式）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `record_time` datetime DEFAULT NULL COMMENT '记录时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_badge_id` (`badge_id`),
    KEY `idx_transaction_id` (`transaction_id`),
    KEY `idx_account_brand` (`account_code`, `brand_code`),
    KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='勋章记录表';

-- 勋章交易表
CREATE TABLE IF NOT EXISTS `badge_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `badge_id` bigint(20) NOT NULL COMMENT '勋章ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '交易状态',
  `description` varchar(500) DEFAULT NULL COMMENT '交易描述',
  `source` varchar(50) DEFAULT NULL COMMENT '交易来源',
  `expire_time` datetime DEFAULT NULL COMMENT '交易过期时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `input_data` text DEFAULT NULL COMMENT '业务上下文（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_badge_id` (`badge_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='勋章交易表';
-- 用户勋章表
CREATE TABLE IF NOT EXISTS `user_badge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `badge_id` bigint(20) NOT NULL COMMENT '勋章ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
  `account_name` varchar(200) DEFAULT NULL COMMENT '账户名称',
  `obtain_time` datetime DEFAULT NULL COMMENT '获取时间',
  `expire_time` datetime DEFAULT NULL COMMENT '失效时间',
  `obtainType` varchar(50) DEFAULT NULL COMMENT '获取方式',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态：ACTIVE-正常，INACTIVE-停用，FROZEN-冻结，DELETED-已删除',
  `obtain_count` int(11) NOT NULL DEFAULT 1 COMMENT '获取数量',
  `is_displayed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `display_order` int(11) DEFAULT NULL COMMENT '显示顺序',
  `extra_data` text DEFAULT NULL COMMENT '扩展数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_badge` (`user_id`, `badge_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户勋章表';
