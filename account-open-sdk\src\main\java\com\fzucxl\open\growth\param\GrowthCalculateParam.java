﻿package com.fzucxl.open.growth.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 计算成长值参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthCalculateParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 业务数据，根据不同场景有不同结构
     */
    private java.util.Map<String, Object> businessData;}
