﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 撤销积分结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointReverseResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;    
    /**
     * 撤销记录ID
     */
    private String reverseId;    
    /**
     * 原交易ID
     */
    private String originalTransactionId;    
    /**
     * 撤销的积分数量
     */
    private Long point;    
    /**
     * 撤销前余额
     */
    private Long beforeBalance;    
    /**
     * 撤销后余额
     */
    private Long afterBalance;    
    /**
     * 处理状态
     */
    private String status;}
