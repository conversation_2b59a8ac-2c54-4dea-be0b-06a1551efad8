﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值交易详情结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthTransactionDetailQueryResult extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 交易数量
     */
    private Long amount;    
    /**
     * 交易前成长值
     */
    private Long beforeGrowth;    
    /**
     * 交易后成长值
     */
    private Long afterGrowth;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 处理状态
     */
    private String status;}
