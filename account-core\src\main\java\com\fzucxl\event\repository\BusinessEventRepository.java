package com.fzucxl.event.repository;

import com.fzucxl.entity.event.BusinessEvent;
import io.micronaut.data.annotation.Repository;
import io.micronaut.data.jdbc.annotation.JdbcRepository;
import io.micronaut.data.model.query.builder.sql.Dialect;
import io.micronaut.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;

/**
 * 业务事件Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@JdbcRepository(dialect = Dialect.MYSQL)
public interface BusinessEventRepository extends CrudRepository<BusinessEvent, Long> {
    
    /**
     * 根据事件编码查找事件
     */
    Optional<BusinessEvent> findByEventCode(String eventCode);
    
    /**
     * 根据事件编码和状态查找事件
     */
    Optional<BusinessEvent> findByEventCodeAndStatus(String eventCode, String status);
    
    /**
     * 根据分类查找事件列表
     */
    List<BusinessEvent> findByCategoryAndStatus(String category, String status);
    
    /**
     * 根据状态查找事件列表
     */
    List<BusinessEvent> findByStatus(String status);
    
    /**
     * 检查事件编码是否存在
     */
    boolean existsByEventCode(String eventCode);
    
    /**
     * 根据创建人查找事件列表
     */
    List<BusinessEvent> findByCreatedByAndStatus(String createdBy, String status);
}