﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户积分明细参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointDetailQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 积分类型
     */
    private String type;    
    /**
     * 积分来源
     */
    private String source;    
    /**
     * 最小积分数
     */
    private Long minPoint;    
    /**
     * 最大积分数
     */
    private Long maxPoint;    
    /**
     * 交易ID
     */
    private String transactionId;}
