﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值规则项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleItem extends Extensible {    
    /**
     * 规则ID
     */
    private Long ruleId;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 成长值
     */
    private Long growthValue;    
    /**
     * 是否匹配
     */
    private Boolean matched;}
