﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 撤销用户勋章参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRevokeParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 用户勋章ID
     */
    private Long userBadgeId;    
    /**
     * 撤销原因
     */
    private String reason;    
    /**
     * 操作人ID
     */
    private Long operatorId;}
