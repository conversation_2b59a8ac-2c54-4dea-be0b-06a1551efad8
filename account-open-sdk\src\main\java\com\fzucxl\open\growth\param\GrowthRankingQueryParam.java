﻿package com.fzucxl.open.growth.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值排行查询参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRankingQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 排行类型：TOTAL-总成长值，CONSUME-消费成长值，ACTIVITY-活跃成长值，默认TOTAL
     */
    private String type;    
    /**
     * 统计周期：ALL-全部，MONTH-本月，WEEK-本周，默认ALL
     */
    private String period;    
    /**
     * 返回记录数，默认100，最大1000
     */
    private Integer limit;}
