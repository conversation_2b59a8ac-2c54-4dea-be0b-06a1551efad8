﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建等级规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleCreateParam extends Extensible {    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 计算方式：POINT(积分), GROWTH(成长值)
     */
    private String calculationType;    
    /**
     * 等级配置列表
     */
    private java.util.List<LevelConfig> levelConfigList;    
    /**
     * 特权配置列表
     */
    private java.util.List<PrivilegeConfig> privilegeConfigList;    
    /**
     * 倍率配置列表
     */
    private java.util.List<MultiplierConfig> multiplierConfigList;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 操作员ID
     */
    private String operatorId;    
    /**
     * 交易ID
     */
    private String transactionId;}
