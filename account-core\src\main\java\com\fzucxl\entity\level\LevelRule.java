package com.fzucxl.entity.level;

import io.micronaut.data.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@MappedEntity("level_rule")
@Data
public class LevelRule {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 等级编码
     */
    private Integer level;
    /**
     * 等级名称
     */
    private String levelName;
    /**
     * 等级图标
     */
    private String levelIcon;
    /**
     * 等级描述
     */
    private String description;

    /**
     * 积分倍数
     */
    private BigDecimal pointMultiplier = BigDecimal.ONE;

    /**
     * 等级折扣率
     */
    private BigDecimal discountRate;

    /**
     * 免运费额度
     */
    private BigDecimal freeShippingThreshold;

    /**
     * 升级奖励积分
     */
    private Long upgradeRewardPoint = 0L;

    /**
     * 升级规则
     */
    private String upgradeRule;
    /**
     * 重新计算规则
     */
    private String recalculateRule;
    /**
     * 权益
     */
    private String privileges;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
