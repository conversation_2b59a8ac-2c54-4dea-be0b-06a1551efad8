package com.fzucxl.entity.growth;

import com.fzucxl.open.base.growth.GrowthTraceType;
import com.fzucxl.open.base.growth.GrowthTransactionType;
import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.event.PrePersist;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("growth_record_trace")
@Data
public class GrowthRecordTrace {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */
    private String brandCode;
    /**
     * 源交易id
     */
    private String sourceTransactionId;

    /**
     * 交易id
     */
    private String transactionId;

    /**
     * 源明细ID
     */
    private Long sourceRecordId;

    /**
     * 目标明细ID
     */
    private Long targetRecordId;

    /**
     * 成长值数量
     */
    private Long growthValue;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 追踪类型
     */
    private GrowthTraceType traceType;
    /**
     * 事务类型：EARN-获得，EXPIRE-过期，ADJUST-调整，TRANSFER-转移，REVOKE-回收
     */
    private GrowthTransactionType transactionType;
    /**
     * 追踪原因
     */
    private String traceReason;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    @PrePersist
    protected void onCreate() {
        this.createTime = LocalDateTime.now();
    }
}
