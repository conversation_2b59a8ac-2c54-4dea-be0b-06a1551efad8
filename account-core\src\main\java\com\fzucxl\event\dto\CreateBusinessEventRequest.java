package com.fzucxl.event.dto;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.serde.annotation.Serdeable;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * 创建业务事件请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Serdeable
public class CreateBusinessEventRequest {
    
    @NotBlank(message = "事件编码不能为空")
    private String eventCode;
    
    @NotBlank(message = "事件名称不能为空")
    private String eventName;
    
    private String description;
    
    private String category;
    
    private List<CreateEventFieldRequest> eventFields;
    
    // Constructors
    public CreateBusinessEventRequest() {}
    
    public CreateBusinessEventRequest(String eventCode, String eventName) {
        this.eventCode = eventCode;
        this.eventName = eventName;
    }
    
    // Getters and Setters
    public String getEventCode() {
        return eventCode;
    }
    
    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }
    
    public String getEventName() {
        return eventName;
    }
    
    public void setEventName(String eventName) {
        this.eventName = eventName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public List<CreateEventFieldRequest> getEventFields() {
        return eventFields;
    }
    
    public void setEventFields(List<CreateEventFieldRequest> eventFields) {
        this.eventFields = eventFields;
    }
    
    /**
     * 创建事件字段请求内部类
     */
    @Introspected
    @Serdeable
    public static class CreateEventFieldRequest {
        
        @NotBlank(message = "字段编码不能为空")
        private String fieldCode;
        
        @NotBlank(message = "字段名称不能为空")
        private String fieldName;
        
        @NotBlank(message = "字段类型不能为空")
        private String fieldType;
        
        private String description;
        
        private Boolean required = false;
        
        private String defaultValue;
        
        private String validationRule;
        
        private Integer sortOrder = 0;
        
        // Constructors
        public CreateEventFieldRequest() {}
        
        public CreateEventFieldRequest(String fieldCode, String fieldName, String fieldType) {
            this.fieldCode = fieldCode;
            this.fieldName = fieldName;
            this.fieldType = fieldType;
        }
        
        // Getters and Setters
        public String getFieldCode() {
            return fieldCode;
        }
        
        public void setFieldCode(String fieldCode) {
            this.fieldCode = fieldCode;
        }
        
        public String getFieldName() {
            return fieldName;
        }
        
        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }
        
        public String getFieldType() {
            return fieldType;
        }
        
        public void setFieldType(String fieldType) {
            this.fieldType = fieldType;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public Boolean getRequired() {
            return required;
        }
        
        public void setRequired(Boolean required) {
            this.required = required;
        }
        
        public String getDefaultValue() {
            return defaultValue;
        }
        
        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }
        
        public String getValidationRule() {
            return validationRule;
        }
        
        public void setValidationRule(String validationRule) {
            this.validationRule = validationRule;
        }
        
        public Integer getSortOrder() {
            return sortOrder;
        }
        
        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }
    }
}