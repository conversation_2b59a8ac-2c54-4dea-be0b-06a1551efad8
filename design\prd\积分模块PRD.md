# 积分模块PRD产品设计文档

## 1. 核心概念

### 1.1 积分定义
积分是用户在平台上进行各种行为活动后获得的虚拟奖励，用于衡量用户对平台的贡献度和活跃度。积分可用于兑换商品、服务或特权。

### 1.2 积分特性
- **可累积性**：用户通过完成任务、消费等行为持续获得积分
- **可消费性**：积分可用于兑换商品、服务或享受折扣
- **时效性**：部分积分可能设置有效期，过期自动清零
- **不可转让性**：积分仅限本人使用，不可转让给他人

## 2. 模型结构设计

### 2.1 积分账户模型
```
UserPoint {
    userId: Long           // 用户ID
    totalPoint: Long      // 总积分
    availablePoint: Long  // 可用积分
    frozenPoint: Long     // 冻结积分
    expiredPoint: Long    // 已过期积分
    createTime: DateTime   // 创建时间
    updateTime: DateTime   // 更新时间
}
```

### 2.2 积分明细模型
```
PointDetail {
    id: Long              // 明细ID
    userId: Long          // 用户ID
    pointType: String    // 积分类型（获得/消费）
    point: Long          // 积分数量
    source: String        // 积分来源
    description: String   // 描述
    expireTime: DateTime  // 过期时间
    status: String        // 状态（有效/已使用/已过期）
    createTime: DateTime  // 创建时间
}
```

### 2.3 积分规则模型
```
PointRule {
    id: Long              // 规则ID
    ruleName: String      // 规则名称
    ruleType: String      // 规则类型
    point: Long          // 积分数量
    maxDaily: Long        // 每日上限
    maxTotal: Long        // 总上限
    validDays: Integer    // 有效天数
    status: String        // 状态
    startTime: DateTime   // 开始时间
    endTime: DateTime     // 结束时间
}
```

## 3. 业务场景

### 3.1 积分获取场景
- **注册奖励**：新用户注册完成后获得初始积分
- **签到奖励**：用户每日签到获得积分，连续签到有额外奖励
- **消费奖励**：用户消费后按比例获得积分回馈
- **任务完成**：完成平台指定任务获得积分奖励
- **邀请奖励**：成功邀请新用户注册获得积分
- **评价奖励**：对商品或服务进行评价获得积分
- **分享奖励**：分享商品或内容到社交平台获得积分

### 3.2 积分消费场景
- **商品兑换**：使用积分兑换平台商品
- **优惠券兑换**：使用积分兑换各类优惠券
- **会员特权**：使用积分开通或延长会员服务
- **抵扣消费**：在购买商品时使用积分抵扣部分金额
- **抽奖活动**：使用积分参与各类抽奖活动

### 3.3 积分管理场景
- **积分查询**：用户查看积分余额和明细记录
- **积分冻结**：因违规行为冻结用户积分
- **积分解冻**：解除积分冻结状态
- **积分过期**：定期清理过期积分
- **积分调整**：管理员手动调整用户积分

## 4. API列表设计

### 4.1 积分查询接口
```
GET /api/point/balance
功能：查询用户积分余额
参数：userId
返回：积分账户信息
```

```
GET /api/point/detail
功能：查询积分明细记录
参数：userId, pageNum, pageSize, startTime, endTime
返回：积分明细列表
```

### 4.2 积分操作接口
```
POST /api/point/earn
功能：用户获得积分
参数：userId, point, source, description
返回：操作结果
```

```
POST /api/point/consume
功能：用户消费积分
参数：userId, point, source, description
返回：操作结果
```

```
POST /api/point/freeze
功能：冻结用户积分
参数：userId, point, reason
返回：操作结果
```

```
POST /api/point/unfreeze
功能：解冻用户积分
参数：userId, point
返回：操作结果
```

### 4.3 积分规则接口
```
GET /api/point/rule
功能：查询积分规则列表
参数：ruleType, status
返回：积分规则列表
```

```
POST /api/point/rule
功能：创建积分规则
参数：规则信息
返回：创建结果
```

```
PUT /api/point/rule/{ruleId}
功能：更新积分规则
参数：ruleId, 规则信息
返回：更新结果
```

### 4.4 积分统计接口
```
GET /api/point/statistics
功能：查询积分统计数据
参数：userId, startTime, endTime
返回：统计数据
```

```
GET /api/point/ranking
功能：查询积分排行榜
参数：rankType, limit
返回：排行榜数据
```

## 5. 技术实现要点

### 5.1 积分计算
- 支持多种积分计算规则（固定值、比例、阶梯等）
- 实现积分获取上限控制
- 支持积分有效期管理

### 5.2 数据一致性
- 使用事务保证积分操作的原子性
- 实现积分账户余额的实时更新
- 建立积分明细的完整审计链

### 5.3 性能优化
- 积分账户信息缓存
- 积分明细分表存储
- 异步处理积分统计任务