﻿package com.fzucxl.open.growth.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建成长值账户结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAccountCreateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 创建时间
     */
    private String createTime;}
