# 通用API设计

## 1. 概述
本文档定义了账户体系中通用的API接口设计，包括公共的数据结构、错误码定义以及各模块共用的接口。

## 2. 接口列表

### 2.1 属性元数据管理接口汇总

| 功能模块 | 接口名称 | 请求方式 | 接口路径 | 接口描述 |
| -------- | -------- | -------- | -------- | -------- |
| **属性元数据管理** | 创建属性元数据 | POST | `/admin/api/v1/attribute` | 创建新的属性元数据配置 |
| | 更新属性元数据 | PUT | `/admin/api/v1/attribute/{id}` | 更新现有属性元数据配置 |
| | 删除属性元数据 | DELETE | `/admin/api/v1/attribute/{id}` | 删除属性元数据配置 |
| | 查询属性元数据列表 | GET | `/admin/api/v1/attribute` | 分页查询属性元数据列表 |
| | 测试属性元数据 | POST | `/admin/api/v1/attribute/test` | 测试属性元数据的查询结果 |

### 2.2 接口特性说明

**属性元数据管理特性：**
- 支持多种数据类型：INTEGER、DECIMAL、STRING、BOOLEAN
- 支持多种数据源：DATABASE、API、CACHE
- 提供聚合函数：SUM、COUNT、AVG、MAX、MIN
- 支持灵活的过滤条件和时间范围配置
- 提供缓存机制，支持自定义TTL
- 支持属性测试功能，验证配置正确性

**通用特性：**
- 统一的响应格式和错误码体系
- 完善的参数校验和异常处理
- 支持链路追踪和日志记录
- 提供详细的接口文档和示例

## 3. 属性元数据管理

### 3.1 创建属性元数据

- **接口路径**：`/admin/api/v1/attribute`
- **请求方式**：POST
- **接口描述**：创建新的属性元数据
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| attributeCode | String | 是 | 属性代码，唯一标识 |
| attributeName | String | 是 | 属性名称 |
| description | String | 否 | 属性描述 |
| dataType | String | 是 | 数据类型：INTEGER, DECIMAL, STRING, BOOLEAN |
| dataSource | String | 是 | 数据源：DATABASE, API, CACHE |
| targetTable | String | 否 | 目标表名，dataSource为DATABASE时必填 |
| aggregateFunction | String | 否 | 聚合函数：SUM, COUNT, AVG, MAX, MIN |
| aggregateField | String | 否 | 聚合字段 |
| filterConditions | Object | 否 | 过滤条件，JSON格式 |
| timeRangeConfig | Object | 否 | 时间范围配置，JSON格式 |
| cacheTtl | Integer | 否 | 缓存TTL，单位秒，默认300 |
| defaultValue | Number | 否 | 默认值，默认0 |
| status | String | 否 | 状态：ACTIVE-生效，INACTIVE-未生效，默认ACTIVE |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | Long | 属性ID |
| attributeCode | String | 属性代码 |
| attributeName | String | 属性名称 |
| createdTime | String | 创建时间 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2001,
    "attributeCode": "user.totalOrderAmount",
    "attributeName": "用户总订单金额",
    "createdTime": "2023-01-01 10:00:00"
  }
}
```

### 3.2 更新属性元数据

- **接口路径**：`/admin/api/v1/attribute/{id}`
- **请求方式**：PUT
- **接口描述**：更新现有属性元数据
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| id | Long | 是 | 属性ID |
| attributeName | String | 否 | 属性名称 |
| description | String | 否 | 属性描述 |
| dataType | String | 否 | 数据类型 |
| dataSource | String | 否 | 数据源 |
| targetTable | String | 否 | 目标表名 |
| aggregateFunction | String | 否 | 聚合函数 |
| aggregateField | String | 否 | 聚合字段 |
| filterConditions | Object | 否 | 过滤条件，JSON格式 |
| timeRangeConfig | Object | 否 | 时间范围配置，JSON格式 |
| cacheTtl | Integer | 否 | 缓存TTL |
| defaultValue | Number | 否 | 默认值 |
| status | String | 否 | 状态 |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |
| updatedTime | String | 更新时间 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "updatedTime": "2023-01-02 15:30:00"
  }
}
```

### 3.3 删除属性元数据

- **接口路径**：`/admin/api/v1/attribute/{id}`
- **请求方式**：DELETE
- **接口描述**：删除属性元数据
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| id | Long | 是 | 属性ID |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true
  }
}
```

### 3.4 查询属性元数据列表

- **接口路径**：`/admin/api/v1/attribute`
- **请求方式**：GET
- **接口描述**：查询属性元数据列表
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| attributeCode | String | 否 | 属性代码 |
| attributeName | String | 否 | 属性名称，支持模糊查询 |
| dataType | String | 否 | 数据类型 |
| status | String | 否 | 状态：ACTIVE-生效，INACTIVE-未生效 |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页记录数，默认10 |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Integer | 总记录数 |
| list | Array | 属性列表 |
| list[].id | Long | 属性ID |
| list[].attributeCode | String | 属性代码 |
| list[].attributeName | String | 属性名称 |
| list[].description | String | 属性描述 |
| list[].dataType | String | 数据类型 |
| list[].dataSource | String | 数据源 |
| list[].targetTable | String | 目标表名 |
| list[].aggregateFunction | String | 聚合函数 |
| list[].aggregateField | String | 聚合字段 |
| list[].filterConditions | Object | 过滤条件 |
| list[].timeRangeConfig | Object | 时间范围配置 |
| list[].cacheTtl | Integer | 缓存TTL |
| list[].defaultValue | Number | 默认值 |
| list[].status | String | 状态 |
| list[].createdBy | String | 创建人 |
| list[].createdTime | String | 创建时间 |
| list[].updatedTime | String | 更新时间 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 50,
    "list": [
      {
        "id": 2001,
        "attributeCode": "user.totalOrderAmount",
        "attributeName": "用户总订单金额",
        "description": "用户历史订单总金额",
        "dataType": "DECIMAL",
        "dataSource": "DATABASE",
        "targetTable": "t_order",
        "aggregateFunction": "SUM",
        "aggregateField": "amount",
        "filterConditions": {
          "status": "COMPLETED"
        },
        "timeRangeConfig": null,
        "cacheTtl": 3600,
        "defaultValue": 0,
        "status": "ACTIVE",
        "createdBy": "admin",
        "createdTime": "2023-01-01 10:00:00",
        "updatedTime": "2023-01-01 10:00:00"
      }
    ]
  }
}
```

### 3.5 测试属性元数据

- **接口路径**：`/admin/api/v1/attribute/test`
- **请求方式**：POST
- **接口描述**：测试属性元数据的查询结果
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| attributeCode | String | 是 | 属性代码 |
| testParams | Object | 是 | 测试参数，如：{"userId": "12345"} |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |
| result | Object | 查询结果 |
| result.value | Number | 属性值 |
| result.sql | String | 生成的SQL（仅管理员可见） |
| result.executionTime | Long | 执行时间（毫秒） |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": {
      "value": 1500.50,
      "sql": "SELECT SUM(amount) FROM t_order WHERE status = 'COMPLETED' AND user_id = '12345'",
      "executionTime": 15
    }
  }
}
```

## 4. 通用数据结构

### 4.1 通用响应格式

所有API接口均采用统一的响应格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": 1641024000000,
  "traceId": "trace-123456789"
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | Integer | 响应码，0表示成功，非0表示失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| timestamp | Long | 响应时间戳 |
| traceId | String | 链路追踪ID |

### 4.2 分页响应格式

对于分页查询接口，data字段结构如下：

```json
{
  "total": 100,
  "pageNum": 1,
  "pageSize": 10,
  "pages": 10,
  "list": []
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Integer | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页记录数 |
| pages | Integer | 总页数 |
| list | Array | 数据列表 |

## 5. 错误码定义

| 错误码 | 描述 | 处理建议 |
| ------ | ---- | -------- |
| 0 | 成功 | - |
| 1001 | 参数错误 | 检查请求参数是否符合要求 |
| 1002 | 用户不存在 | 确认用户ID是否正确 |
| 2001 | 积分不足 | 检查用户积分余额 |
| 2002 | 积分已过期 | 提示用户积分已过期 |
| 2003 | 积分已冻结 | 提示用户积分已被冻结 |
| 3001 | 规则不存在 | 检查规则代码是否正确 |
| 3002 | 规则已失效 | 检查规则状态和有效期 |
| 4001 | 业务单号重复 | 检查业务单号是否已使用 |
| 5001 | 系统内部错误 | 联系系统管理员 |