﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章规则列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRuleListQueryParam extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 规则名称（模糊查询）
     */
    private String ruleName;    
    /**
     * 规则类型
     */
    private String ruleType;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页大小
     */
    private Integer pageSize;}
