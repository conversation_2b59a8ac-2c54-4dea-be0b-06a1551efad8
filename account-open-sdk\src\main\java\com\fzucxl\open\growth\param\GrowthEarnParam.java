﻿package com.fzucxl.open.growth.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值获得参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthEarnParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 获得数量
     */
    private Long amount;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
