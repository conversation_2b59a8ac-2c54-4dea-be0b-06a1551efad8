{"ast": null, "code": "import { parseColor } from '@antv/g';\nexport function parseToRGB(c) {\n  if (typeof c === 'object') return c;\n  return parseColor(c);\n}", "map": {"version": 3, "names": ["parseColor", "parseToRGB", "c"], "sources": ["D:\\code\\private\\fast-account\\loyalty-system-admin\\node_modules\\@antv\\g2\\src\\utils\\color.ts"], "sourcesContent": ["import { parseColor } from '@antv/g';\n\nexport type Color = {\n  r: number;\n  g: number;\n  b: number;\n};\n\nexport function parseToRGB(c: string | Color): Color {\n  if (typeof c === 'object') return c;\n  return parseColor(c) as Color;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAS;AAQpC,OAAM,SAAUC,UAAUA,CAACC,CAAiB;EAC1C,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOA,CAAC;EACnC,OAAOF,UAAU,CAACE,CAAC,CAAU;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}