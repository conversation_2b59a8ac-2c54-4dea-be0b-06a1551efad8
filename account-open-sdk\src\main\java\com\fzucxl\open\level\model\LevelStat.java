﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级统计
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelStat extends Extensible {    
    /**
     * 统计日期
     */
    private String statDate;    
    /**
     * 用户数量
     */
    private Long userCount;    
    /**
     * 升级次数
     */
    private Long upgradeCount;    
    /**
     * 降级次数
     */
    private Long downgradeCount;    
    /**
     * 平均等级
     */
    private Double avgLevel;}
