﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分账户列表参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccountListQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户名称（模糊查询）
     */
    private String accountName;    
    /**
     * 账户状态
     */
    private String status;    
    /**
     * 最小积分
     */
    private Long minPoint;    
    /**
     * 最大积分
     */
    private Long maxPoint;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;}
