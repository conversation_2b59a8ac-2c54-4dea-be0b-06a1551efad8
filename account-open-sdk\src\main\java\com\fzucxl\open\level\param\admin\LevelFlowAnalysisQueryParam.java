﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级流转分析参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelFlowAnalysisQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 起始等级
     */
    private Integer fromLevel;    
    /**
     * 目标等级
     */
    private Integer toLevel;    
    /**
     * 交易ID
     */
    private String transactionId;}
