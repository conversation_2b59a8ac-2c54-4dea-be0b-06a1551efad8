package com.fzucxl.point.rule.engine;

import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表达式变量解析器
 * 解析表达式中的动态变量，如 ${user.totalOrderAmount}、${user.orderCount_30d} 等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
@Slf4j
public class ExpressionVariableParser {
    
    /**
     * 动态变量的正则表达式模式
     * 匹配格式：${变量名} 或 #{变量名}
     */
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}|#\\{([^}]+)\\}");
    
    /**
     * 解析表达式中的所有动态变量
     * 
     * @param expression 表达式字符串
     * @return 动态变量列表
     */
    public Set<String> parseVariables(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return Collections.emptySet();
        }
        
        Set<String> variables = new HashSet<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        
        while (matcher.find()) {
            // group(1) 对应 ${...} 中的内容
            // group(2) 对应 #{...} 中的内容
            String variable = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            if (variable != null && !variable.trim().isEmpty()) {
                variables.add(variable.trim());
            }
        }
        
        log.debug("解析表达式变量: expression={}, variables={}", expression, variables);
        return variables;
    }
    
    /**
     * 替换表达式中的动态变量为实际值
     * 
     * @param expression 原始表达式
     * @param variableValues 变量值映射
     * @return 替换后的表达式
     */
    public String replaceVariables(String expression, Map<String, Object> variableValues) {
        if (expression == null || expression.trim().isEmpty()) {
            return expression;
        }
        
        if (variableValues == null || variableValues.isEmpty()) {
            return expression;
        }
        
        String result = expression;
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        
        while (matcher.find()) {
            String fullMatch = matcher.group(0); // 完整匹配，如 ${user.totalOrderAmount}
            String variable = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            
            if (variable != null && variableValues.containsKey(variable)) {
                Object value = variableValues.get(variable);
                String replacement = formatValue(value);
                result = result.replace(fullMatch, replacement);
                
                log.debug("替换变量: {} -> {}", fullMatch, replacement);
            }
        }
        
        return result;
    }
    
    /**
     * 格式化变量值为表达式中可用的字符串
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        
        if (value instanceof String) {
            // 字符串需要加引号
            return "\"" + value.toString().replace("\"", "\\\"") + "\"";
        }
        
        if (value instanceof Number || value instanceof Boolean) {
            return value.toString();
        }
        
        if (value instanceof Date) {
            // 日期转换为时间戳
            return String.valueOf(((Date) value).getTime());
        }
        
        // 其他类型转换为字符串并加引号
        return "\"" + value.toString().replace("\"", "\\\"") + "\"";
    }
    
    /**
     * 验证变量名格式是否正确
     * 
     * @param variableName 变量名
     * @return 是否有效
     */
    public boolean isValidVariableName(String variableName) {
        if (variableName == null || variableName.trim().isEmpty()) {
            return false;
        }
        
        // 变量名格式：字母开头，可包含字母、数字、点号、下划线
        Pattern namePattern = Pattern.compile("^[a-zA-Z][a-zA-Z0-9._]*$");
        return namePattern.matcher(variableName.trim()).matches();
    }
    
    /**
     * 从变量名中提取属性键
     * 例如：user.totalOrderAmount -> user.totalOrderAmount
     *      order.count_30d -> order.count_30d
     * 
     * @param variableName 变量名
     * @return 属性键
     */
    public String extractAttributeKey(String variableName) {
        if (variableName == null) {
            return null;
        }
        
        // 直接返回变量名作为属性键
        return variableName.trim();
    }
    
    /**
     * 检查表达式是否包含动态变量
     * 
     * @param expression 表达式
     * @return 是否包含动态变量
     */
    public boolean containsVariables(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return false;
        }
        
        return VARIABLE_PATTERN.matcher(expression).find();
    }
    
    /**
     * 获取表达式中的变量统计信息
     * 
     * @param expression 表达式
     * @return 变量统计信息
     */
    public VariableStatistics getVariableStatistics(String expression) {
        Set<String> variables = parseVariables(expression);
        
        Map<String, Integer> prefixCount = new HashMap<>();
        for (String variable : variables) {
            String prefix = extractPrefix(variable);
            prefixCount.put(prefix, prefixCount.getOrDefault(prefix, 0) + 1);
        }
        
        return new VariableStatistics(variables.size(), prefixCount, variables);
    }
    
    /**
     * 从变量名中提取前缀
     * 例如：user.totalOrderAmount -> user
     */
    private String extractPrefix(String variableName) {
        if (variableName == null) {
            return "";
        }
        
        int dotIndex = variableName.indexOf('.');
        return dotIndex > 0 ? variableName.substring(0, dotIndex) : variableName;
    }
    
    /**
     * 变量统计信息
     */
    public static class VariableStatistics {
        private final int totalCount;
        private final Map<String, Integer> prefixCount;
        private final Set<String> allVariables;
        
        public VariableStatistics(int totalCount, Map<String, Integer> prefixCount, Set<String> allVariables) {
            this.totalCount = totalCount;
            this.prefixCount = prefixCount;
            this.allVariables = allVariables;
        }
        
        public int getTotalCount() {
            return totalCount;
        }
        
        public Map<String, Integer> getPrefixCount() {
            return prefixCount;
        }
        
        public Set<String> getAllVariables() {
            return allVariables;
        }
        
        @Override
        public String toString() {
            return String.format("VariableStatistics{totalCount=%d, prefixCount=%s, variables=%s}", 
                totalCount, prefixCount, allVariables);
        }
    }
}