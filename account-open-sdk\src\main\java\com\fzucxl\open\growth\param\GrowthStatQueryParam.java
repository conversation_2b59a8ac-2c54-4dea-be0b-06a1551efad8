﻿package com.fzucxl.open.growth.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值统计查询参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthStatQueryParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    private String startDate;    
    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    private String endDate;    
    /**
     * 分组方式：DAY, WEEK, MONTH，默认DAY
     */
    private String groupBy;}
