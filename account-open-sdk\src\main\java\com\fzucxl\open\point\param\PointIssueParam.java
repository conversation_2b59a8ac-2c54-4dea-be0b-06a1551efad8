﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 发放积分参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointIssueParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 积分数量
     */
    private Long point;    
    /**
     * 积分来源代码
     */
    private String source;    
    /**
     * 来源名称
     */
    private String sourceName;    
    /**
     * 详细描述
     */
    private String description;    
    /**
     * 有效期天数
     */
    private Integer expireDays;    
    /**
     * 生效时间
     */
    private String effectiveTime;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
