﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章获得统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeObtainStatQueryParam extends Extensible {    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 稀有度
     */
    private String rarity;}
