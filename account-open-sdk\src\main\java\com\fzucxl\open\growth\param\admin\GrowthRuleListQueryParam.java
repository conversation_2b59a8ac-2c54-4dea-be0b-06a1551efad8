﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值规则列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleListQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 规则名称，支持模糊查询
     */
    private String ruleName;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 状态：ACTIVE-生效，INACTIVE-未生效
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;}
