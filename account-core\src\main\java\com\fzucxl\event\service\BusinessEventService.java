package com.fzucxl.event.service;

import com.fzucxl.common.utils.JsonUtils;
import com.fzucxl.entity.event.BusinessEvent;
import com.fzucxl.entity.event.BusinessEventField;
import com.fzucxl.entity.event.BusinessEventInstance;
import com.fzucxl.event.model.BusinessEventContext;
import com.fzucxl.event.repository.BusinessEventFieldRepository;
import com.fzucxl.event.repository.BusinessEventInstanceRepository;
import com.fzucxl.event.repository.BusinessEventRepository;
import io.micronaut.transaction.annotation.Transactional;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 业务事件服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class BusinessEventService {
    
    private static final Logger log = LoggerFactory.getLogger(BusinessEventService.class);
    
    @Inject
    private BusinessEventRepository eventRepository;
    
    @Inject
    private BusinessEventFieldRepository fieldRepository;
    
    @Inject
    private BusinessEventInstanceRepository instanceRepository;
    
    @Inject
    private BusinessEventContextBuilder contextBuilder;
    
    /**
     * 创建业务事件
     */
    @Transactional
    public BusinessEvent createEvent(BusinessEvent event) {
        // 检查事件编码是否已存在
        if (eventRepository.existsByEventCode(event.getEventCode())) {
            throw new RuntimeException("事件编码已存在: " + event.getEventCode());
        }
        
        return eventRepository.save(event);
    }
    
    /**
     * 更新业务事件
     */
    @Transactional
    public BusinessEvent updateEvent(BusinessEvent event) {
        Optional<BusinessEvent> existingEvent = eventRepository.findById(event.getId());
        if (existingEvent.isEmpty()) {
            throw new RuntimeException("事件不存在: " + event.getId());
        }
        
        return eventRepository.update(event);
    }
    
    /**
     * 删除业务事件
     */
    @Transactional
    public void deleteEvent(Long eventId) {
        // 删除关联的字段
        fieldRepository.deleteByEventId(eventId);
        
        // 删除事件
        eventRepository.deleteById(eventId);
    }
    
    /**
     * 根据事件编码获取事件
     */
    public Optional<BusinessEvent> getEventByCode(String eventCode) {
        return eventRepository.findByEventCodeAndStatus(eventCode, "ACTIVE");
    }
    
    /**
     * 获取事件的字段列表
     */
    public List<BusinessEventField> getEventFields(Long eventId) {
        return fieldRepository.findByEventIdAndStatusOrderBySortOrder(eventId, "ACTIVE");
    }
    
    /**
     * 添加事件字段
     */
    @Transactional
    public BusinessEventField addEventField(BusinessEventField field) {
        // 检查字段编码是否已存在
        if (fieldRepository.existsByEventIdAndFieldCode(field.getEventId(), field.getFieldCode())) {
            throw new RuntimeException("字段编码已存在: " + field.getFieldCode());
        }
        
        return fieldRepository.save(field);
    }
    
    /**
     * 更新事件字段
     */
    @Transactional
    public BusinessEventField updateEventField(BusinessEventField field) {
        return fieldRepository.update(field);
    }
    
    /**
     * 删除事件字段
     */
    @Transactional
    public void deleteEventField(Long fieldId) {
        fieldRepository.deleteById(fieldId);
    }
    
    /**
     * 触发业务事件
     */
    @Transactional
    public BusinessEventInstance triggerEvent(String eventCode, String userId, 
                                            Map<String, Object> eventData, String source) {
        // 获取事件定义
        Optional<BusinessEvent> eventOpt = getEventByCode(eventCode);
        if (eventOpt.isEmpty()) {
            throw new RuntimeException("事件不存在或已禁用: " + eventCode);
        }
        
        BusinessEvent event = eventOpt.get();
        List<BusinessEventField> fields = getEventFields(event.getId());
        
        // 验证事件数据
        if (!contextBuilder.validateEventData(eventData, fields)) {
            throw new RuntimeException("事件数据验证失败: " + eventCode);
        }
        
        // 创建事件实例
        BusinessEventInstance instance = new BusinessEventInstance();
        instance.setEventId(event.getId());
        instance.setEventCode(eventCode);
        instance.setUserId(userId);
        instance.setEventData(JsonUtils.toJson(eventData));
        instance.setSource(source);
        instance.setEventTime(LocalDateTime.now());
        instance.setStatus("PENDING");
        
        return instanceRepository.save(instance);
    }
    
    /**
     * 构建事件上下文
     */
    public BusinessEventContext buildEventContext(Long instanceId) {
        Optional<BusinessEventInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (instanceOpt.isEmpty()) {
            throw new RuntimeException("事件实例不存在: " + instanceId);
        }
        
        BusinessEventInstance instance = instanceOpt.get();
        Optional<BusinessEvent> eventOpt = eventRepository.findById(instance.getEventId());
        if (eventOpt.isEmpty()) {
            throw new RuntimeException("事件定义不存在: " + instance.getEventId());
        }
        
        BusinessEvent event = eventOpt.get();
        List<BusinessEventField> fields = getEventFields(event.getId());
        return contextBuilder.buildContext(instance, event, fields);
    }
    
    /**
     * 构建事件上下文（从原始数据）
     */
    public BusinessEventContext buildEventContext(String eventCode, String userId, Map<String, Object> eventData) {
        Optional<BusinessEvent> eventOpt = getEventByCode(eventCode);
        if (eventOpt.isEmpty()) {
            throw new RuntimeException("事件不存在或已禁用: " + eventCode);
        }
        
        BusinessEvent event = eventOpt.get();
        List<BusinessEventField> fields = getEventFields(event.getId());
        
        return contextBuilder.buildContext(eventCode, userId, eventData, fields);
    }
    
    /**
     * 更新事件实例状态
     */
    @Transactional
    public void updateInstanceStatus(Long instanceId, String status, String processResult, String errorMessage) {
        Optional<BusinessEventInstance> instanceOpt = instanceRepository.findById(instanceId);
        if (instanceOpt.isEmpty()) {
            throw new RuntimeException("事件实例不存在: " + instanceId);
        }
        
        BusinessEventInstance instance = instanceOpt.get();
        instance.setStatus(status);
        instance.setProcessResult(processResult);
        instance.setErrorMessage(errorMessage);
        
        instanceRepository.update(instance);
    }
    
    /**
     * 获取用户事件统计
     */
    public long getUserEventCount(String userId, String eventCode, LocalDateTime startTime, LocalDateTime endTime) {
        return instanceRepository.countByUserIdAndEventCodeAndEventTimeBetween(userId, eventCode, startTime, endTime);
    }
    
    /**
     * 获取用户最近的事件实例
     */
    public Optional<BusinessEventInstance> getUserLatestEvent(String userId, String eventCode) {
        return instanceRepository.findFirstByEventCodeAndUserIdOrderByEventTimeDesc(eventCode, userId);
    }
    
    /**
     * 获取待处理的事件实例
     */
    public List<BusinessEventInstance> getPendingInstances() {
        return instanceRepository.findByStatusOrderByCreatedAtAsc("PENDING");
    }
    
    /**
     * 批量处理事件实例
     */
    @Transactional
    public void batchProcessInstances(List<Long> instanceIds, String status) {
        for (Long instanceId : instanceIds) {
            updateInstanceStatus(instanceId, status, null, null);
        }
    }
    
    /**
     * 获取所有启用的事件
     */
    public List<BusinessEvent> getActiveEvents() {
        return eventRepository.findByStatus("ACTIVE");
    }
    
    /**
     * 获取事件实例（按条件查询）
     */
    public List<BusinessEventInstance> getEventInstances(String eventCode, String userId, String status) {
        if (eventCode != null && userId != null) {
            return instanceRepository.findByEventCodeAndUserIdOrderByEventTimeDesc(eventCode, userId);
        } else if (eventCode != null) {
            return instanceRepository.findByEventCodeOrderByEventTimeDesc(eventCode);
        } else if (userId != null) {
            return instanceRepository.findByUserIdOrderByEventTimeDesc(userId);
        } else if (status != null) {
            return instanceRepository.findByStatusOrderByCreatedAtDesc(status);
        } else {
            return (List<BusinessEventInstance>) instanceRepository.findAll();
        }
    }
    
    // 暴露repository访问方法（用于向后兼容）
    public BusinessEventRepository getEventRepository() {
        return eventRepository;
    }
    
    public BusinessEventInstanceRepository getInstanceRepository() {
        return instanceRepository;
    }
    
    public BusinessEventFieldRepository getFieldRepository() {
        return fieldRepository;
    }
}
