﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级账户
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelAccount extends Extensible {    
    /**
     * 账户ID
     */
    private String accountId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 账户描述
     */
    private String description;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;}
