﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建勋章规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRuleCreateParam extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 规则类型：ACHIEVEMENT(成就), ACTIVITY(活动), LEVEL(等级), SPECIAL(特殊)
     */
    private String ruleType;    
    /**
     * 触发条件
     */
    private String triggerCondition;    
    /**
     * 条件表达式
     */
    private String conditionExpression;    
    /**
     * 优先级
     */
    private Integer priority;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 操作人ID
     */
    private Long operatorId;}
