﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新等级规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleUpdateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 更新时间
     */
    private String updateTime;    
    /**
     * 交易ID
     */
    private String transactionId;}
