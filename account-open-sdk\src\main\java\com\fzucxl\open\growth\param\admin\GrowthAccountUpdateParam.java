﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 更新成长值账户参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAccountUpdateParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户状态：ACTIVE-正常，FROZEN-冻结，CLOSED-关闭
     */
    private String status;    
    /**
     * 备注信息
     */
    private String remark;}
