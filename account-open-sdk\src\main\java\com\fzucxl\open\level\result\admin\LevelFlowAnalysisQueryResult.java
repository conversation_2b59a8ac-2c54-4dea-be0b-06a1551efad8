﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级流转分析结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelFlowAnalysisQueryResult extends Extensible {    
    /**
     * 等级流转列表
     */
    private java.util.List<LevelFlow> flowList;    
    /**
     * 总流转次数
     */
    private Long totalFlow;    
    /**
     * 分析时间
     */
    private String analysisTime;}
