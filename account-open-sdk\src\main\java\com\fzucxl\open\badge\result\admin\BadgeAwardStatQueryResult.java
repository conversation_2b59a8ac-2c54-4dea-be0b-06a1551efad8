﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章发放统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAwardStatQueryResult extends Extensible {    
    /**
     * 总发放数量
     */
    private Long totalAwardCount;    
    /**
     * 获得用户数
     */
    private Long uniqueUserCount;    
    /**
     * 发放统计明细
     */
    private java.util.List<BadgeAwardStatItem> awardStatList;}
