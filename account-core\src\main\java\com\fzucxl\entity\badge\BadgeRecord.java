package com.fzucxl.entity.badge;

import com.fzucxl.open.base.badge.BadgeTransactionType;
import io.micronaut.data.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("badge_record")
@Data
public class BadgeRecord {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 勋章ID
     */
    private Long badgeId;
    /**
     * 勋章账户ID
     */
    private Long userBadgeId;

    /**
     * 账户代码
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;
    /**
     * 账户名称
     */
    private String accountName;
    /**
     * 交易类型
     */
    private BadgeTransactionType transactionType;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则
     */
    private String source;
    /**
     * 渠道类型
     */
    private String channelType;
    /**
     * 事务ID
     */
    private String transactionId;
    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 原因
     */
    private String reason;
    /**
     * 变更前状态
     */
    private String beforeStatus;
    /**
     * 变更后状态
     */
    private String afterStatus;
    /**
     * 扩展数据（JSON格式）
     */
    private String extraData;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
}
