# 勋章模块PRD产品设计文档

## 1. 核心概念

### 1.1 勋章定义
勋章是用户在平台上完成特定成就或达到特定条件后获得的荣誉标识，用于激励用户参与平台活动，提升用户粘性和成就感。勋章具有收集性和展示性，是用户身份和能力的象征。

### 1.2 勋章特性
- **成就性**：代表用户在某个领域的成就或里程碑
- **稀有性**：不同勋章有不同的获得难度和稀有度
- **展示性**：用户可以展示获得的勋章，彰显个人成就
- **收集性**：激发用户收集不同类型勋章的欲望
- **时效性**：部分勋章有获得时间限制或失效机制

## 2. 模型结构设计

### 2.1 勋章配置模型
```
BadgeConfig {
    id: Long              // 勋章ID
    badgeName: String     // 勋章名称
    badgeIcon: String     // 勋章图标
    badgeType: String     // 勋章类型
    rarity: String        // 稀有度
    description: String   // 勋章描述
    conditions: String    // 获得条件（JSON格式）
    rewards: String       // 奖励内容（JSON格式）
    validDays: Integer    // 有效天数（0表示永久）
    maxCount: Integer     // 最大发放数量（0表示无限制）
    issuedCount: Integer  // 已发放数量
    status: String        // 状态
    startTime: DateTime   // 开始时间
    endTime: DateTime     // 结束时间
    createTime: DateTime  // 创建时间
}
```

### 2.2 用户勋章模型
```
UserBadge {
    id: Long              // 记录ID
    userId: Long          // 用户ID
    badgeId: Long         // 勋章ID
    earnTime: DateTime    // 获得时间
    expireTime: DateTime  // 过期时间
    status: String        // 状态（有效/已过期）
    isDisplayed: Boolean  // 是否展示
    displayOrder: Integer // 展示顺序
    source: String        // 获得来源
    createTime: DateTime  // 创建时间
}
```

### 2.3 勋章进度模型
```
BadgeProgress {
    id: Long              // 进度ID
    userId: Long          // 用户ID
    badgeId: Long         // 勋章ID
    currentValue: Long    // 当前进度值
    targetValue: Long     // 目标值
    progressRate: Double  // 进度百分比
    lastUpdateTime: DateTime // 最后更新时间
    createTime: DateTime  // 创建时间
}
```

### 2.4 勋章获得记录模型
```
BadgeEarnRecord {
    id: Long              // 记录ID
    userId: Long          // 用户ID
    badgeId: Long         // 勋章ID
    earnReason: String    // 获得原因
    earnTime: DateTime    // 获得时间
    triggerEvent: String  // 触发事件
    eventData: String     // 事件数据
    createTime: DateTime  // 创建时间
}
```

## 3. 业务场景

### 3.1 勋章获得场景

#### 3.1.1 成就类勋章
- **新手上路**：完成首次注册和基础信息填写
- **购物达人**：累计消费达到指定金额
- **忠实粉丝**：连续登录达到指定天数
- **评价专家**：发表评价数量达到指定数量
- **分享大使**：分享商品数量达到指定数量

#### 3.1.2 活动类勋章
- **节日纪念**：在特定节日期间参与活动
- **限时挑战**：在限定时间内完成特定任务
- **抢购王者**：成功抢购限量商品
- **签到冠军**：连续签到打卡创纪录

#### 3.1.3 社交类勋章
- **人气王**：获得点赞数达到指定数量
- **意见领袖**：发表的内容被大量转发
- **热心助手**：帮助其他用户解决问题
- **社区贡献**：在社区发帖或回复达到指定数量

#### 3.1.4 特殊类勋章
- **内测用户**：参与产品内测的用户
- **首批用户**：平台早期注册用户
- **VIP会员**：开通VIP会员服务
- **周年庆典**：参与平台周年庆活动

### 3.2 勋章展示场景
- **个人主页**：在用户个人主页展示获得的勋章
- **勋章墙**：专门的勋章展示页面，展示所有勋章收集情况
- **社交互动**：在评论、发帖时显示用户的代表性勋章
- **排行榜**：展示勋章获得数量排行榜

### 3.3 勋章管理场景
- **勋章配置**：管理员配置新的勋章类型和获得条件
- **勋章发放**：系统自动或手动发放勋章给符合条件的用户
- **勋章回收**：对于违规用户回收已获得的勋章
- **勋章统计**：统计各类勋章的发放情况和用户获得情况

## 4. API列表设计

### 4.1 勋章查询接口
```
GET /api/v1/badge/list
功能：查询勋章列表
参数：badgeType, rarity, status
返回：勋章配置列表
```

```
GET /api/v1/badge/user/{userId}
功能：查询用户获得的勋章
参数：userId, status, isDisplayed
返回：用户勋章列表
```

```
GET /api/v1/badge/progress/{userId}
功能：查询用户勋章进度
参数：userId, badgeId
返回：勋章进度信息
```

### 4.2 勋章操作接口
```
POST /api/v1/badge/award
功能：发放勋章给用户
参数：userId, badgeId, source, reason
返回：发放结果
```

```
POST /api/v1/badge/batch-award
功能：批量发放勋章
参数：用户列表, 勋章信息
返回：批量发放结果
```

```
POST /api/v1/badge/revoke
功能：回收用户勋章
参数：userId, badgeId, reason
返回：回收结果
```

### 4.3 勋章展示接口
```
PUT /api/v1/badge/display
功能：设置勋章展示状态
参数：userId, badgeId, isDisplayed, displayOrder
返回：设置结果
```

```
GET /api/v1/badge/showcase/{userId}
功能：查询用户勋章展示
参数：userId
返回：展示的勋章列表
```

### 4.4 勋章管理接口
```
POST /api/v1/badge/config
功能：创建勋章配置
参数：勋章配置信息
返回：创建结果
```

```
PUT /api/v1/badge/config/{badgeId}
功能：更新勋章配置
参数：badgeId, 配置信息
返回：更新结果
```

```
GET /api/v1/badge/statistics
功能：查询勋章统计数据
参数：startTime, endTime, badgeType
返回：统计数据
```

```
GET /api/v1/badge/ranking
功能：查询勋章排行榜
参数：rankType, limit
返回：排行榜数据
```

## 5. 勋章体系设计

### 5.1 勋章分类
```
成就类勋章：
- 新手上路、购物达人、忠实粉丝等

活动类勋章：
- 节日纪念、限时挑战、抢购王者等

社交类勋章：
- 人气王、意见领袖、热心助手等

特殊类勋章：
- 内测用户、首批用户、VIP会员等
```

### 5.2 稀有度等级
```
普通（Common）：容易获得的基础勋章
稀有（Rare）：需要一定努力才能获得的勋章
史诗（Epic）：需要较大投入才能获得的勋章
传说（Legendary）：极难获得的顶级勋章
```

### 5.3 勋章奖励
```
积分奖励：获得勋章时给予积分奖励
成长值奖励：获得勋章时增加成长值
特权奖励：某些勋章附带特殊权益
称号奖励：获得勋章时解锁专属称号
```

## 6. 技术实现要点

### 6.1 勋章触发机制
- 事件驱动的勋章检测系统
- 支持实时和批量勋章检测
- 可配置的勋章获得条件引擎

### 6.2 勋章进度跟踪
- 实时更新用户勋章进度
- 支持多维度进度计算
- 提供进度可视化展示

### 6.3 性能优化
- 勋章配置信息缓存
- 异步处理勋章发放
- 勋章进度数据的增量更新

### 6.4 数据一致性
- 确保勋章发放的唯一性
- 建立勋章操作的审计日志
- 实现勋章数据的定期校验