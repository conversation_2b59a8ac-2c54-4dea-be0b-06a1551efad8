package com.fzucxl.entity.point;

import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分规则实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedEntity("point_rule")
@Data
public class PointRule {
    
    @Id
    @GeneratedValue
    private Long id;
    private String accountCode;
    private String ruleCode;
    private String ruleName;
    private String description;
    /**
     * 条件规则表达式
     */
    private String conditionRule;

    /**
     * 动作规则表达式
     */
    private String actionRule;
    private Integer priority;
    private String status;
    private LocalDateTime effectiveTime;
    private LocalDateTime expireTime;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 构造函数
    public PointRule() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
        this.status = "ACTIVE";
        this.priority = 0;
    }
}