﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级计算结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelCalculateResult extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 计算得出的等级
     */
    private Integer calculatedLevel;    
    /**
     * 等级名称
     */
    private String levelName;    
    /**
     * 当前计算值
     */
    private Long currentValue;    
    /**
     * 下一等级所需值
     */
    private Long nextLevelValue;    
    /**
     * 升级进度百分比
     */
    private Double progress;    
    /**
     * 计算方式
     */
    private String calculationType;    
    /**
     * 计算时间
     */
    private String calculateTime;}
