﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值获得统计查询参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthEarnStatQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    private String startDate;    
    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    private String endDate;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 分组方式：DAY, WEEK, MONTH，默认DAY
     */
    private String groupBy;}
