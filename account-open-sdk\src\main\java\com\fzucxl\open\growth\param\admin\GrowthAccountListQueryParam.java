﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询成长值账户列表参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthAccountListQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 最小成长值
     */
    private Long minGrowth;    
    /**
     * 最大成长值
     */
    private Long maxGrowth;    
    /**
     * 账户状态：ACTIVE-正常，FROZEN-冻结，CLOSED-关闭
     */
    private String status;    
    /**
     * 创建开始时间
     */
    private String createStartTime;    
    /**
     * 创建结束时间
     */
    private String createEndTime;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;}
