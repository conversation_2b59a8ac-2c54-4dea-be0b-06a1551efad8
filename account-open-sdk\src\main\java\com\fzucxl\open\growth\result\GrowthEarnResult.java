﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值获得结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthEarnResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 获得数量
     */
    private Long amount;    
    /**
     * 获得前成长值
     */
    private Long beforeGrowth;    
    /**
     * 获得后成长值
     */
    private Long afterGrowth;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 获得时间
     */
    private String earnTime;    
    /**
     * 处理状态
     */
    private String status;}
