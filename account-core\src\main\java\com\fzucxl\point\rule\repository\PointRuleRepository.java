package com.fzucxl.point.rule.repository;

import com.fzucxl.entity.point.PointRule;
import io.micronaut.data.jdbc.annotation.JdbcRepository;
import io.micronaut.data.model.query.builder.sql.Dialect;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import java.util.List;
import java.util.Optional;

/**
 * 积分规则Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@JdbcRepository(dialect = Dialect.MYSQL)
public interface PointRuleRepository extends CrudRepository<PointRule, Long> {
    
    /**
     * 分页查找所有规则
     */
    Page<PointRule> findAll(Pageable pageable);
    
    /**
     * 根据账户代码和规则代码查找
     */
    Optional<PointRule> findByAccountCodeAndRuleCode(String accountCode, String ruleCode);
    
    /**
     * 根据账户代码、规则代码和状态查找
     */
    Optional<PointRule> findByAccountCodeAndRuleCodeAndStatus(String accountCode, String ruleCode, String status);
    
    /**
     * 检查账户规则是否存在
     */
    boolean existsByAccountCodeAndRuleCode(String accountCode, String ruleCode);
    
    /**
     * 根据账户代码查找所有规则
     */
    List<PointRule> findByAccountCode(String accountCode);
    
    /**
     * 根据账户代码分页查找规则
     */
    Page<PointRule> findByAccountCode(String accountCode, Pageable pageable);
    
    /**
     * 根据账户代码和状态查找规则
     */
    List<PointRule> findByAccountCodeAndStatus(String accountCode, String status);
    
    /**
     * 根据账户代码和状态分页查找规则
     */
    Page<PointRule> findByAccountCodeAndStatus(String accountCode, String status, Pageable pageable);
    
    /**
     * 根据状态查找所有规则，按优先级排序
     */
    List<PointRule> findByStatusOrderByPriorityDesc(String status);
    
    /**
     * 根据状态分页查找规则
     */
    Page<PointRule> findByStatus(String status, Pageable pageable);
    
    /**
     * 根据规则代码查找
     */
    List<PointRule> findByRuleCode(String ruleCode);
    
    /**
     * 根据优先级范围查找
     */
    List<PointRule> findByPriorityBetween(Integer minPriority, Integer maxPriority);
}