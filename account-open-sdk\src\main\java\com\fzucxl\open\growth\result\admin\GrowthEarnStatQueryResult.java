﻿package com.fzucxl.open.growth.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值获得统计查询结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthEarnStatQueryResult extends Extensible {    
    /**
     * 总成长值
     */
    private Long totalGrowth;    
    /**
     * 总用户数
     */
    private Long totalUsers;    
    /**
     * 人均成长值
     */
    private java.math.BigDecimal avgGrowthPerUser;    
    /**
     * 统计明细
     */
    private java.util.List<GrowthEarnStatDetail> detailList;}
