﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 特权配置
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrivilegeConfig extends Extensible {    
    /**
     * 等级
     */
    private Integer level;    
    /**
     * 特权代码
     */
    private String privilegeCode;    
    /**
     * 特权名称
     */
    private String privilegeName;    
    /**
     * 特权类型
     */
    private String privilegeType;    
    /**
     * 特权值
     */
    private String privilegeValue;    
    /**
     * 特权描述
     */
    private String description;}
