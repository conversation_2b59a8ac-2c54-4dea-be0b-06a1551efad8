﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章交易模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeTransaction extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 交易状态
     */
    private String status;    
    /**
     * 交易描述
     */
    private String description;    
    /**
     * 勋章来源
     */
    private String source;    
    /**
     * 处理时间
     */
    private String processedTime;    
    /**
     * 创建时间
     */
    private String createTime;}
