﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 发放积分结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointIssueResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;    
    /**
     * 积分明细记录ID
     */
    private String detailId;    
    /**
     * 实际发放的积分数量
     */
    private Long point;    
    /**
     * 发放前余额
     */
    private Long beforeBalance;    
    /**
     * 发放后余额
     */
    private Long afterBalance;    
    /**
     * 积分过期时间
     */
    private String expireTime;    
    /**
     * 积分生效时间
     */
    private String effectiveTime;    
    /**
     * 处理状态
     */
    private String status;}
