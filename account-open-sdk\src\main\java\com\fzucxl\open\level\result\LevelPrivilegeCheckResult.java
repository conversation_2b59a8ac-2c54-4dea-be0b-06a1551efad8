﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 检查等级特权结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelPrivilegeCheckResult extends Extensible {    
    /**
     * 是否拥有特权
     */
    private Boolean hasPrivilege;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 当前等级
     */
    private Integer level;    
    /**
     * 特权代码
     */
    private String privilegeCode;    
    /**
     * 特权名称
     */
    private String privilegeName;    
    /**
     * 特权值
     */
    private String privilegeValue;    
    /**
     * 检查时间
     */
    private String checkTime;}
