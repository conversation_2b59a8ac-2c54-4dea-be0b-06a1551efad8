package com.fzucxl.point.rule.dto;

import io.micronaut.core.annotation.Introspected;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;

/**
 * 更新规则请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
public class UpdateRuleRequest {
    
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;
    
    @NotBlank(message = "属性代码不能为空")
    private String attributeCode;
    
    @NotBlank(message = "操作符不能为空")
    private String operator;
    
    @NotNull(message = "阈值不能为空")
    private Object threshold;
    
    private Integer priority;
    private String description;
    private List<String> additionalConditions;
    private Map<String, Object> ruleParams;
    private String updaterId;
    
    // Getter and Setter methods
    public String getRuleName() {
        return ruleName;
    }
    
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
    
    public String getAttributeCode() {
        return attributeCode;
    }
    
    public void setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
    
    public Object getThreshold() {
        return threshold;
    }
    
    public void setThreshold(Object threshold) {
        this.threshold = threshold;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<String> getAdditionalConditions() {
        return additionalConditions;
    }
    
    public void setAdditionalConditions(List<String> additionalConditions) {
        this.additionalConditions = additionalConditions;
    }
    
    public Map<String, Object> getRuleParams() {
        return ruleParams;
    }
    
    public void setRuleParams(Map<String, Object> ruleParams) {
        this.ruleParams = ruleParams;
    }
    
    public String getUpdaterId() {
        return updaterId;
    }
    
    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }
}