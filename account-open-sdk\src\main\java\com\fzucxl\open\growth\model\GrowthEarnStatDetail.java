﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值获得统计明细模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthEarnStatDetail extends Extensible {    
    /**
     * 日期
     */
    private String date;    
    /**
     * 获得成长值
     */
    private Long earnGrowth;    
    /**
     * 用户数量
     */
    private Long userCount;    
    /**
     * 人均成长值
     */
    private java.math.BigDecimal avgGrowthPerUser;}
