package com.fzucxl.entity.growth;

import com.fzucxl.open.base.AccountStatus;
import com.fzucxl.open.base.AccountType;
import io.micronaut.data.annotation.*;
import io.micronaut.data.annotation.event.PrePersist;
import io.micronaut.data.annotation.event.PreUpdate;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("growth_account")
@Data
public class GrowthAccount {

    @Id
    @GeneratedValue
    private Long id;

    /**
     * 账户代码，唯一标识
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;
    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型 ：BUSINESS-业务账户，PERSONAL-个人账户，SYSTEM-系统账户
     */
    private AccountType accountType;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 账户状态 ：ACTIVE-激活，INACTIVE-停用，FROZEN-冻结，DELETED-已删除
     */
    private AccountStatus status;

    /**
     * 基础配置（JSON格式）
     */
    private String basicConfig;

    /**
     * 风控配置（JSON格式）
     */
    private String riskControlConfig;
    /**
     * 扩展配置（JSON格式）
     */
    private String extensionConfig;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        if (this.status == null) {
            this.status = AccountStatus.ACTIVE;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}
