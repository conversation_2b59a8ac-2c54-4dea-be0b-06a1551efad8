﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeStatQueryResult extends Extensible {    
    /**
     * 总发放数量
     */
    private Long totalCount;    
    /**
     * 活跃勋章数量
     */
    private Long activeCount;    
    /**
     * 过期勋章数量
     */
    private Long expiredCount;    
    /**
     * 统计明细
     */
    private java.util.List<BadgeStatItem> statList;}
