﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 删除等级规则结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRuleDeleteResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 删除时间
     */
    private String deleteTime;    
    /**
     * 交易ID
     */
    private String transactionId;}
