package com.fzucxl.point.rule.engine;

import com.alibaba.qlexpress4.Express4Runner;
import com.alibaba.qlexpress4.QLOptions;
import com.alibaba.qlexpress4.QLResult;
import com.fzucxl.entity.point.PointRule;
import com.fzucxl.point.rule.repository.PointRuleRepository;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 规则执行引擎
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class RuleExecutionEngine {
    
    private static final Logger log = LoggerFactory.getLogger(RuleExecutionEngine.class);
    
    @Inject
    private Express4Runner express4Runner;
    
    @Inject
    private PointRuleRepository ruleRepository;

    @Inject
    private ExpressionVariableParser variableParser;

    @Inject
    private AttributeExecutor attributeExecutor;

    private static final QLOptions.Builder optionsBuilder = QLOptions.builder()
        .traceExpression(false)
        .precise(true)
        .shortCircuitDisable(false);
    /**
     * 执行规则
     */
    public boolean executeRule(String accountCode,
                               String ruleCode,
                               Map<String, Object> context,
                               Map<String, Object> attachment) {

        // 获取规则
        PointRule rule = ruleRepository.findByAccountCodeAndRuleCodeAndStatus(
                accountCode, ruleCode, "ACTIVE").orElse(null);

        try {
            String expression = rule.getConditionRule();
            log.debug("开始执行规则: ruleCode={}, expression={}", rule.getRuleCode(), expression);

            // 1. 解析表达式中的动态变量
            Set<String> variables = variableParser.parseVariables(expression);
            log.debug("解析到动态变量: {}", variables);

            // 2. 获取动态变量的值
            Map<String, Object> variableValues = attributeExecutor.getVariableValues(variables, context);
            log.debug("获取变量值: {}", variableValues);

            context.putAll(variableValues);

            // 添加用户ID

            // 添加变量值到上下文（以防表达式中直接使用变量名）
            context.putAll(variableValues);
            QLOptions qlOptions = optionsBuilder.attachments(attachment).build();
            // 5. 执行表达式
            QLResult qlresult = express4Runner.execute(rule.getConditionRule(), context, qlOptions);
            Object result = qlresult.getResult();
            log.debug("规则执行完成: ruleCode={}, result={}", rule.getRuleCode(), result);
            // 转换结果为boolean
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else if (result instanceof Number) {
                return ((Number) result).doubleValue() > 0;
            } else if (result instanceof String) {
                return Boolean.parseBoolean((String) result);
            }

            return false;

        } catch (Exception e) {
            log.error("执行表达式失败: expression={}", rule.getConditionRule(), e);
            return false;
        }
    }
    
    /**
     * 执行表达式
     */
    public boolean executeExpression(String expression,
                                     Map<String, Object> context,
                                     Map<String, Object> attachment) {
        try {
            // 准备上下文
            Map<String, Object> expressContext = new HashMap<>(context);
            QLOptions qlOptions = optionsBuilder.attachments(attachment).build();
            // 执行表达式
            Object result = express4Runner.execute(expression, expressContext, qlOptions);
            
            // 转换结果为boolean
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else if (result instanceof Number) {
                return ((Number) result).doubleValue() > 0;
            } else if (result instanceof String) {
                return Boolean.parseBoolean((String) result);
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("执行表达式失败: expression={}", expression, e);
            return false;
        }
    }
    
    /**
     * 批量执行规则
     */
    public Map<String, Boolean> executeAccountRule(String accountCode,
                                                    Map<String, Object> context,
                                                    Map<String, Object> attachment) {
        Map<String, Boolean> results = new HashMap<>();
        
        try {
            // 获取账户的所有活跃规则
            List<PointRule> rules = ruleRepository.findByAccountCodeAndStatus(accountCode, "ACTIVE");
            
            // 按优先级排序执行
            rules.sort((r1, r2) -> r2.getPriority().compareTo(r1.getPriority()));
            
            for (PointRule rule : rules) {
                try {
                    boolean result = executeExpression(rule.getConditionRule(), context, attachment);
                    results.put(rule.getRuleCode(), result);
                    
                    log.debug("规则执行结果: ruleCode={}, result={}", rule.getRuleCode(), result);
                    
                } catch (Exception e) {
                    log.error("执行规则失败: ruleCode={}", rule.getRuleCode(), e);
                    results.put(rule.getRuleCode(), false);
                }
            }
            
        } catch (Exception e) {
            log.error("批量执行规则失败: accountCode={}", accountCode, e);
        }
        
        return results;
    }
}