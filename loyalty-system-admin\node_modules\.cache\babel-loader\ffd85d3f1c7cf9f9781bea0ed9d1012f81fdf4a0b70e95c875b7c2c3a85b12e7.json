{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { FormProvider as Rc<PERSON>ormProvider } from 'rc-field-form';\nimport omit from \"rc-util/es/omit\";\nexport const FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  layout: 'horizontal',\n  itemRef: () => {}\n});\nexport const NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexport const FormProvider = props => {\n  const providerProps = omit(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(RcFormProvider, Object.assign({}, providerProps));\n};\nexport const FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexport const FormItemInputContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  FormItemInputContext.displayName = 'FormItemInputContext';\n}\nexport const NoFormStyle = ({\n  children,\n  status,\n  override\n}) => {\n  const formItemInputContext = React.useContext(FormItemInputContext);\n  const newFormItemInputContext = React.useMemo(() => {\n    const newContext = Object.assign({}, formItemInputContext);\n    if (override) {\n      delete newContext.isFormItemInput;\n    }\n    if (status) {\n      delete newContext.status;\n      delete newContext.hasFeedback;\n      delete newContext.feedbackIcon;\n    }\n    return newContext;\n  }, [status, override, formItemInputContext]);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: newFormItemInputContext\n  }, children);\n};\nexport const VariantContext = /*#__PURE__*/React.createContext(undefined);", "map": {"version": 3, "names": ["React", "FormProvider", "RcFormProvider", "omit", "FormContext", "createContext", "labelAlign", "layout", "itemRef", "NoStyleItemContext", "props", "providerProps", "createElement", "Object", "assign", "FormItemPrefixContext", "prefixCls", "FormItemInputContext", "process", "env", "NODE_ENV", "displayName", "NoFormStyle", "children", "status", "override", "formItemInputContext", "useContext", "newFormItemInputContext", "useMemo", "newContext", "isFormItemInput", "hasFeedback", "feedbackIcon", "Provider", "value", "VariantContext", "undefined"], "sources": ["D:/code/private/fast-account/loyalty-system-admin/node_modules/antd/es/form/context.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { FormProvider as Rc<PERSON>ormProvider } from 'rc-field-form';\nimport omit from \"rc-util/es/omit\";\nexport const FormContext = /*#__PURE__*/React.createContext({\n  labelAlign: 'right',\n  layout: 'horizontal',\n  itemRef: () => {}\n});\nexport const NoStyleItemContext = /*#__PURE__*/React.createContext(null);\nexport const FormProvider = props => {\n  const providerProps = omit(props, ['prefixCls']);\n  return /*#__PURE__*/React.createElement(RcFormProvider, Object.assign({}, providerProps));\n};\nexport const FormItemPrefixContext = /*#__PURE__*/React.createContext({\n  prefixCls: ''\n});\nexport const FormItemInputContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  FormItemInputContext.displayName = 'FormItemInputContext';\n}\nexport const NoFormStyle = ({\n  children,\n  status,\n  override\n}) => {\n  const formItemInputContext = React.useContext(FormItemInputContext);\n  const newFormItemInputContext = React.useMemo(() => {\n    const newContext = Object.assign({}, formItemInputContext);\n    if (override) {\n      delete newContext.isFormItemInput;\n    }\n    if (status) {\n      delete newContext.status;\n      delete newContext.hasFeedback;\n      delete newContext.feedbackIcon;\n    }\n    return newContext;\n  }, [status, override, formItemInputContext]);\n  return /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: newFormItemInputContext\n  }, children);\n};\nexport const VariantContext = /*#__PURE__*/React.createContext(undefined);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,IAAIC,cAAc,QAAQ,eAAe;AAC9D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC;EAC1DC,UAAU,EAAE,OAAO;EACnBC,MAAM,EAAE,YAAY;EACpBC,OAAO,EAAEA,CAAA,KAAM,CAAC;AAClB,CAAC,CAAC;AACF,OAAO,MAAMC,kBAAkB,GAAG,aAAaT,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;AACxE,OAAO,MAAMJ,YAAY,GAAGS,KAAK,IAAI;EACnC,MAAMC,aAAa,GAAGR,IAAI,CAACO,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAChD,OAAO,aAAaV,KAAK,CAACY,aAAa,CAACV,cAAc,EAAEW,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,aAAa,CAAC,CAAC;AAC3F,CAAC;AACD,OAAO,MAAMI,qBAAqB,GAAG,aAAaf,KAAK,CAACK,aAAa,CAAC;EACpEW,SAAS,EAAE;AACb,CAAC,CAAC;AACF,OAAO,MAAMC,oBAAoB,GAAG,aAAajB,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC,CAAC;AACxE,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,oBAAoB,CAACI,WAAW,GAAG,sBAAsB;AAC3D;AACA,OAAO,MAAMC,WAAW,GAAGA,CAAC;EAC1BC,QAAQ;EACRC,MAAM;EACNC;AACF,CAAC,KAAK;EACJ,MAAMC,oBAAoB,GAAG1B,KAAK,CAAC2B,UAAU,CAACV,oBAAoB,CAAC;EACnE,MAAMW,uBAAuB,GAAG5B,KAAK,CAAC6B,OAAO,CAAC,MAAM;IAClD,MAAMC,UAAU,GAAGjB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEY,oBAAoB,CAAC;IAC1D,IAAID,QAAQ,EAAE;MACZ,OAAOK,UAAU,CAACC,eAAe;IACnC;IACA,IAAIP,MAAM,EAAE;MACV,OAAOM,UAAU,CAACN,MAAM;MACxB,OAAOM,UAAU,CAACE,WAAW;MAC7B,OAAOF,UAAU,CAACG,YAAY;IAChC;IACA,OAAOH,UAAU;EACnB,CAAC,EAAE,CAACN,MAAM,EAAEC,QAAQ,EAAEC,oBAAoB,CAAC,CAAC;EAC5C,OAAO,aAAa1B,KAAK,CAACY,aAAa,CAACK,oBAAoB,CAACiB,QAAQ,EAAE;IACrEC,KAAK,EAAEP;EACT,CAAC,EAAEL,QAAQ,CAAC;AACd,CAAC;AACD,OAAO,MAAMa,cAAc,GAAG,aAAapC,KAAK,CAACK,aAAa,CAACgC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}