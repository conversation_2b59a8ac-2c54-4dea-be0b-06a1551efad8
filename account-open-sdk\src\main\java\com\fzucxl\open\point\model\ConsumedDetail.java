﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 消费明细模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumedDetail extends Extensible {    
    /**
     * 明细ID
     */
    private String detailId;    
    /**
     * 消费积分数量
     */
    private Long point;    
    /**
     * 原过期时间
     */
    private String expireTime;    
    /**
     * 消费时间
     */
    private String consumeTime;}
