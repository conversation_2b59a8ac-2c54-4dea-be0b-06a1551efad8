﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询交易记录参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointTransactionQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 交易状态
     */
    private String status;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;}
