﻿package com.fzucxl.open.badge.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章详情结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeDetailQueryResult extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 勋章类型
     */
    private String badgeType;    
    /**
     * 勋章等级
     */
    private Integer badgeLevel;    
    /**
     * 勋章描述
     */
    private String description;    
    /**
     * 勋章图标URL
     */
    private String iconUrl;    
    /**
     * 获得条件
     */
    private String obtainCondition;    
    /**
     * 获得时间
     */
    private String obtainTime;    
    /**
     * 过期时间
     */
    private String expireTime;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 展示状态
     */
    private String displayStatus;    
    /**
     * 特权描述
     */
    private String privilege;    
    /**
     * 稀有度：COMMON(普通), RARE(稀有), EPIC(史诗), LEGENDARY(传说)
     */
    private String rarity;}
