﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级分布统计参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelDistributionStatQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 交易ID
     */
    private String transactionId;}
