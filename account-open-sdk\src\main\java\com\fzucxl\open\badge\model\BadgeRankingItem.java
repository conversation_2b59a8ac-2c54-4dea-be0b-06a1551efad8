﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章排行榜项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRankingItem extends Extensible {    
    /**
     * 排名
     */
    private Integer rank;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 用户名称
     */
    private String userName;    
    /**
     * 勋章数量
     */
    private Integer badgeCount;    
    /**
     * 得分
     */
    private java.math.BigDecimal score;}
