﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级账户列表结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelAccountListQueryResult extends Extensible {    
    /**
     * 等级账户列表
     */
    private java.util.List<LevelAccount> accountList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
