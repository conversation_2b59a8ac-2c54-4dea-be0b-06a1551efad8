package com.fzucxl.point.rule.resource;


import com.fzucxl.open.common.Result;
import com.fzucxl.point.rule.dto.CreateAttributeRequest;
import com.fzucxl.point.rule.dto.AttributeTestResult;
import com.fzucxl.point.rule.dto.UpdateAttributeRequest;
import com.fzucxl.entity.attribute.AttributeMeta;
import com.fzucxl.point.rule.repository.AttributeMetaRepository;
import com.fzucxl.point.rule.service.AttributeMetaService;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import io.micronaut.http.annotation.*;
import io.micronaut.validation.Validated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 属性元数据控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Controller("/point/attribute")
@Validated
public class AttributeResource {
    
    private static final Logger log = LoggerFactory.getLogger(AttributeResource.class);
    
    @Inject
    private AttributeMetaService configService;
    
    @Inject
    private AttributeMetaRepository metaRepository;
    
    /**
     * 创建属性表达式
     */
    @Post
    public Result<AttributeMeta> createAttribute(@Body @Valid CreateAttributeRequest request) {
        try {
            AttributeMeta result = configService.createAttribute(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建属性表达式失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新属性表达式
     */
    @Put("/{id}")
    public Result<AttributeMeta> updateAttribute(
            @PathVariable Long id,
            @Body @Valid UpdateAttributeRequest request) {
        try {
            AttributeMeta result = configService.updateAttribute(id, request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新属性表达式失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取属性表达式详情
     */
    @Get("/{id}")
    public Result<AttributeMeta> getExpression(@PathVariable Long id) {
        try {
            AttributeMeta result = metaRepository.findById(id).orElse(null);
            if (result == null) {
                return Result.error("属性表达式不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取属性表达式失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询属性表达式
     */
    @Get
    public Result<Page<AttributeMeta>> listExpressions(
            @QueryValue String status,
            @QueryValue String dataSource,
            Pageable pageable) {
        try {
            Page<AttributeMeta> result;
            if (status != null && dataSource != null) {
                result = metaRepository.findByDataSourceAndStatus(dataSource, status, pageable);
            } else if (status != null) {
                result = metaRepository.findByStatus(status, pageable);
            } else {
                result = metaRepository.findAll(pageable);
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询属性表达式失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有活跃的属性表达式
     */
    @Get("/active")
    public Result<List<AttributeMeta>> getActiveAttributes() {
        try {
            List<AttributeMeta> result = metaRepository.findByStatus("ACTIVE");
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取活跃属性表达式失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试属性表达式
     */
    @Post("/test")
    public Result<AttributeTestResult> testAttribute(@Body Map<String, Object> testRequest) {
        try {
            String expressionCode = (String) testRequest.get("expressionCode");
            Map<String, Object> testParams = (Map<String, Object>) testRequest.get("testParams");
            
            AttributeTestResult result = configService.testAttribute(expressionCode, testParams);
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试属性表达式失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除属性表达式
     */
    @Delete("/{id}")
    public Result<Void> deleteAttribute(@PathVariable Long id) {
        try {
            configService.deleteAttribute(id);
            return Result.success();
        } catch (Exception e) {
            log.error("删除属性表达式失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }
}