﻿package com.fzucxl.open.growth.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值统计查询结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthStatQueryResult extends Extensible {    
    /**
     * 总成长值
     */
    private Long totalGrowth;    
    /**
     * 统计明细
     */
    private java.util.List<GrowthStatDetail> detailList;    
    /**
     * 类型分布
     */
    private java.util.List<GrowthTypeDistribution> typeDistributionList;}
