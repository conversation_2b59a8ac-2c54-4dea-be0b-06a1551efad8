﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章分布项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeDistributionItem extends Extensible {    
    /**
     * 统计维度
     */
    private String dimension;    
    /**
     * 维度值
     */
    private String dimensionValue;    
    /**
     * 数量
     */
    private Long count;    
    /**
     * 占比
     */
    private java.math.BigDecimal percentage;}
