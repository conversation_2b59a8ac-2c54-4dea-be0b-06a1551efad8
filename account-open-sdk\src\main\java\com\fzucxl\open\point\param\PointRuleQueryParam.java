﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分规则参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 规则类型
     */
    private String ruleType;    
    /**
     * 规则状态
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;}
