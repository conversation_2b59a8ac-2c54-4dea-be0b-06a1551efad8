﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级交易详情结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelTransactionDetailQueryResult extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 变更前等级
     */
    private Integer beforeLevel;    
    /**
     * 变更后等级
     */
    private Integer afterLevel;    
    /**
     * 变更原因
     */
    private String changeReason;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 处理状态
     */
    private String status;}
