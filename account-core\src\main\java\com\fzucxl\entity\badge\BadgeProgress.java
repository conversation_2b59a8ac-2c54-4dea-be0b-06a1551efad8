package com.fzucxl.entity.badge;

import io.micronaut.data.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@MappedEntity("badge_progress")
@Data
public class BadgeProgress {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 勋章ID
     */
    private Long badgeId;

    /**
     * 账户代码
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;
    /**
     * 当前进度
     */
    private Long currentProgress = 0L;
    /**
     * 目标进度
     */
    private Long targetProgress;
    /**
     * 进度数据
     */
    private String progressData;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 计算进度百分比
    public Double getProgressPercentage() {
        if (targetProgress == null || targetProgress == 0) {
            return 0.0;
        }
        return Math.min(100.0, (currentProgress.doubleValue() / targetProgress.doubleValue()) * 100);
    }

    // 判断是否已完成
    public Boolean isCompleted() {
        return currentProgress != null && targetProgress != null && currentProgress >= targetProgress;
    }
}
