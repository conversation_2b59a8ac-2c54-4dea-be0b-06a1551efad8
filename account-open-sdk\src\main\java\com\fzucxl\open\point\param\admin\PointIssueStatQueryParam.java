﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分发放统计参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointIssueStatQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 开始时间
     */
    private String startTime;    
    /**
     * 结束时间
     */
    private String endTime;    
    /**
     * 积分来源
     */
    private String source;    
    /**
     * 分组方式
     */
    private String groupBy;}
