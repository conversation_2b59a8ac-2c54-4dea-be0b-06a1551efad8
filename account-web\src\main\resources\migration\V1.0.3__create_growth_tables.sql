-- 成长值账户表
CREATE TABLE IF NOT EXISTS `growth_account` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_code` varchar(100) NOT NULL COMMENT '账户代码，唯一标识',
    `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
    `account_name` varchar(200) NOT NULL COMMENT '账户名称',
    `account_type` varchar(50) NOT NULL COMMENT '账户类型：BUSINESS-业务账户，PERSONAL-个人账户，SYSTEM-系统账户',
    `tenant_code` varchar(100) DEFAULT NULL COMMENT '租户代码',
    `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态：ACTIVE-激活，INACTIVE-停用，FROZEN-冻结，DELETED-已删除',
    `basic_config` text DEFAULT NULL COMMENT '基础配置（JSON格式）',
    `risk_control_config` text DEFAULT NULL COMMENT '风控配置（JSON格式）',
    `extension_config` text DEFAULT NULL COMMENT '扩展配置（JSON格式）',
    `description` varchar(500) DEFAULT NULL COMMENT '描述信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_account_code` (`account_code`),
    UNIQUE KEY `uk_account_brand` (`account_code`, `brand_code`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值账户表';
-- 成长值记录表
CREATE TABLE IF NOT EXISTS `growth_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
  `transaction_type` varchar(50) NOT NULL COMMENT '事务类型：EARN-获得，EXPIRE-过期，ADJUST-调整，TRANSFER-转移，REVOKE-回收',
  `growth_value` bigint(20) NOT NULL COMMENT '成长值数量',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型：PURCHASE-购买，ACTIVITY-活动，SOCIAL-社交，TASK-任务',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `source` varchar(50) NOT NULL COMMENT '来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则',
  `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
  `reason` varchar(500) DEFAULT NULL COMMENT '原因描述',
  `transaction_id` varchar(100) NOT NULL COMMENT '事务ID',
  `rule_code` varchar(100) DEFAULT NULL COMMENT '规则代码',
  `base_growth` bigint(20) DEFAULT NULL COMMENT '基础成长值',
  `multiplier` decimal(10,2) DEFAULT NULL COMMENT '倍率',
  `extra_data` text DEFAULT NULL COMMENT '扩展数据（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值记录表';
-- 成长值明细追踪表
CREATE TABLE IF NOT EXISTS `growth_record_trace` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `source_transaction_id` varchar(100) DEFAULT NULL COMMENT '源交易ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `source_record_id` bigint(20) DEFAULT NULL COMMENT '源明细ID',
  `target_record_id` bigint(20) DEFAULT NULL COMMENT '目标明细ID',
  `growth_value` bigint(20) NOT NULL COMMENT '成长值数量',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `trace_type` varchar(50) NOT NULL COMMENT '追踪类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '事务类型：EARN-获得，EXPIRE-过期，ADJUST-调整，TRANSFER-转移，REVOKE-回收',
  `trace_reason` varchar(500) DEFAULT NULL COMMENT '追踪原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_source_transaction_id` (`source_transaction_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值明细追踪表';
-- 成长值规则表
CREATE TABLE IF NOT EXISTS `growth_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `rule_code` varchar(100) NOT NULL COMMENT '规则代码',
  `rule_name` varchar(200) NOT NULL COMMENT '规则名称',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `base_growth` bigint(20) DEFAULT NULL COMMENT '基础成长值',
  `multiplier` decimal(10,2) NOT NULL DEFAULT 1.00 COMMENT '倍率',
  `daily_limit` bigint(20) DEFAULT NULL COMMENT '每日限制',
  `monthly_limit` bigint(20) DEFAULT NULL COMMENT '每月限制',
  `condition_rule` text DEFAULT NULL COMMENT '条件规则表达式',
  `action_rule` text DEFAULT NULL COMMENT '动作规则表达式',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-激活，INACTIVE-停用',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_brand_rule` (`account_code`, `brand_code`, `rule_code`),
  KEY `idx_account_code` (`account_code`),
  KEY `idx_brand_code` (`brand_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值规则表';
-- 成长值事务表
CREATE TABLE IF NOT EXISTS `growth_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '事务ID，全局唯一',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `transaction_type` varchar(50) NOT NULL COMMENT '事务类型：EARN-获得，EXPIRE-过期，ADJUST-调整，TRANSFER-转移，REVOKE-回收',
  `total_growth` bigint(20) NOT NULL COMMENT '总成长值变动',
  `consume_growth` bigint(20) DEFAULT NULL COMMENT '消费成长值变动',
  `activity_growth` bigint(20) DEFAULT NULL COMMENT '活跃成长值变动',
  `social_growth` bigint(20) DEFAULT NULL COMMENT '社交成长值变动',
  `task_growth` bigint(20) DEFAULT NULL COMMENT '任务成长值变动',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '事务状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消',
  `source` varchar(50) DEFAULT NULL COMMENT '事务来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则，API-接口',
  `input_data` text DEFAULT NULL COMMENT '业务上下文（JSON格式）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成长值事务表';
-- 用户成长值表
CREATE TABLE IF NOT EXISTS `user_growth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户代码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌代码',
  `total_growth` bigint(20) NOT NULL DEFAULT 0 COMMENT '总成长值',
  `consume_growth` bigint(20) NOT NULL DEFAULT 0 COMMENT '消费成长值',
  `activity_growth` bigint(20) NOT NULL DEFAULT 0 COMMENT '活跃成长值',
  `social_growth` bigint(20) NOT NULL DEFAULT 0 COMMENT '社交成长值',
  `task_growth` bigint(20) NOT NULL DEFAULT 0 COMMENT '任务成长值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_account_brand` (`user_id`, `account_code`, `brand_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_total_growth` (`total_growth`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户成长值表';
