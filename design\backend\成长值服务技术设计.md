# 成长值服务技术设计文档

## 1. 服务概述

### 1.1 服务职责
成长值服务负责用户成长值的计算、累积和统计分析，是等级体系的数据基础和用户价值评估的核心模块。

### 1.2 核心功能
- 多维度成长值计算
- 成长值累积管理
- 成长轨迹记录
- 成长值统计分析
- 成长值规则引擎

## 2. 技术架构

### 2.1 服务架构图
```
┌─────────────────────────────────────────┐
│             成长值服务                   │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐       │
│  │ 成长值控制器 │  │ 成长值服务层 │       │
│  └─────────────┘  └─────────────┘       │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  计算引擎   │  │  统计分析器  │       │
│  └─────────────┘  └─────────────┘       │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  规则引擎   │  │  数据访问层  │       │
│  └─────────────┘  └─────────────┘       │
├─────────────────────────────────────────┤
│              数据存储                    │
│  ┌─────────────┐  ┌─────────────┐       │
│  │    MySQL    │  │    Redis    │       │
│  └─────────────┘  └─────────────┘       │
└─────────────────────────────────────────┘
```

### 2.2 核心类设计

#### 2.2.1 成长值模块实体概览

| 实体名称 | 实体类 | 表名 | 用途 | 核心字段 |
| -------- | ------ | ---- | ---- | -------- |
| **成长值账户** | GrowthAccount | growth_account | 存储成长值账户的基本配置信息和统计数据，管理成长值账户的生命周期 | accountCode, accountName, accountType, brandCode, status, basicConfig |
| **用户成长值账户** | UserGrowth | user_growth | 存储用户成长值账户基本信息和累计成长值 | userId, totalGrowth, consumeGrowth, activityGrowth, socialGrowth, taskGrowth |
| **成长值明细** | GrowthDetail | growth_detail | 记录所有成长值变动的详细信息 | userId, growthValue, businessType, businessId, source, reason |
| **成长值规则** | GrowthRule | growth_rule | 管理成长值获取规则和计算逻辑 | ruleCode, ruleName, businessType, baseGrowth, multiplier, dailyLimit, monthlyLimit |
| **成长值统计** | GrowthStatistics | growth_statistics | 存储用户成长值统计数据 | userId, statisticsDate, totalGrowth, consumeGrowth, activityGrowth, socialGrowth, taskGrowth |
| **成长值明细追踪** | GrowthDetailTrace | growth_detail_trace | 追踪成长值明细之间的关联关系 | sourceDetailId, targetDetailId, traceType |
| **成长值事务** | GrowthTransaction | growth_transaction | 管理成长值操作的事务信息，确保数据一致性 | transactionId, userId, transactionType, totalGrowth, status, businessContext |

**核心关系链路：**

1. **用户成长值流程**：UserGrowth ← GrowthDetail ← GrowthRuleConfig
2. **成长值追踪链路**：GrowthDetail ← GrowthDetailTrace → GrowthDetail  
3. **规则执行链路**：BusinessEvent → GrowthRuleConfig → AttributeMeta
4. **统计分析链路**：GrowthDetail → GrowthStatistics
5. **事务管理链路**：GrowthTransaction ← GrowthDetail
6. **完整事务链路**：GrowthTransaction → UserGrowth → GrowthDetail → GrowthStatistics

#### 2.2.2 成长值账户实体

**实体名称**：成长值账户
**实体类**：GrowthAccount
**表名**：growth_account

```java
@MappedEntity("growth_account")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthAccount {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 账户代码
     */
    @Column(name = "account_code", nullable = false, unique = true, length = 64)
    private String accountCode;
    
    /**
     * 账户名称
     */
    @Column(name = "account_name", nullable = false, length = 128)
    private String accountName;
    
    /**
     * 账户类型：SYSTEM-系统账户，BRAND-品牌账户，TENANT-租户账户
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false, length = 32)
    private AccountType accountType;
    
    /**
     * 品牌代码
     */
    @Column(name = "brand_code", length = 64)
    private String brandCode;
    
    /**
     * 租户代码
     */
    @Column(name = "tenant_code", length = 64)
    private String tenantCode;
    
    /**
     * 账户状态：ACTIVE-激活，INACTIVE-停用，DELETED-删除
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 32)
    private AccountStatus status;
    
    /**
     * 基础配置（JSON格式）
     */
    @Column(name = "basic_config", columnDefinition = "TEXT")
    private String basicConfig;

    @Column(name = "risk_control_config", columnDefinition = "JSON")
    private String riskControlConfig;
    
    @Column(name = "extension_config", columnDefinition = "JSON")
    private String extensionConfig;
    
    /**
     * 描述信息
     */
    @Column(name = "description", length = 512)
    private String description;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    @Column(name = "create_by", length = 64)
    private String createBy;
    
    /**
     * 更新人
     */
    @Column(name = "update_by", length = 64)
    private String updateBy;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        if (this.status == null) {
            this.status = AccountStatus.ACTIVE;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}
```

#### 2.2.3 用户成长值账户实体

**实体名称**：用户成长值账户
**实体类**：UserGrowth
**表名**：user_growth

```java
@MappedEntity("user_growth", 
       indexes = {
           @Index(name = "idx_user_growth_user_id", columnList = "user_id"),
           @Index(name = "idx_user_growth_total", columnList = "total_growth"),
           @Index(name = "idx_user_growth_update_time", columnList = "last_update_time")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserGrowth {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 总成长值
     */
    @Column(name = "total_growth", nullable = false)
    private Long totalGrowth;
    
    /**
     * 消费成长值
     */
    @Column(name = "consume_growth", nullable = false)
    private Long consumeGrowth;
    
    /**
     * 活跃成长值
     */
    @Column(name = "activity_growth", nullable = false)
    private Long activityGrowth;
    
    /**
     * 社交成长值
     */
    @Column(name = "social_growth", nullable = false)
    private Long socialGrowth;
    
    /**
     * 任务成长值
     */
    @Column(name = "task_growth", nullable = false)
    private Long taskGrowth;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 最后更新时间
     */
    @Column(name = "last_update_time", nullable = false)
    private LocalDateTime lastUpdateTime;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.lastUpdateTime = now;
        
        // 初始化成长值为0
        if (this.totalGrowth == null) this.totalGrowth = 0L;
        if (this.consumeGrowth == null) this.consumeGrowth = 0L;
        if (this.activityGrowth == null) this.activityGrowth = 0L;
        if (this.socialGrowth == null) this.socialGrowth = 0L;
        if (this.taskGrowth == null) this.taskGrowth = 0L;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 增加成长值
     */
    public void addGrowth(String businessType, Long growthValue) {
        this.totalGrowth += growthValue;
        
        switch (businessType.toUpperCase()) {
            case "PURCHASE":
            case "CONSUME":
                this.consumeGrowth += growthValue;
                break;
            case "ACTIVITY":
                this.activityGrowth += growthValue;
                break;
            case "SOCIAL":
                this.socialGrowth += growthValue;
                break;
            case "TASK":
                this.taskGrowth += growthValue;
                break;
        }
    }
}
```

#### 2.2.4 成长值明细实体

**实体名称**：成长值明细
**实体类**：GrowthDetail
**表名**：growth_detail

```java
@MappedEntity("growth_detail",
       indexes = {
           @Index(name = "idx_growth_detail_user_time", columnList = "user_id, create_time"),
           @Index(name = "idx_growth_detail_business", columnList = "business_type, business_id"),
           @Index(name = "idx_growth_detail_transaction", columnList = "transaction_id")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthDetail {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "change_type", nullable = false)
    private GrowthChangeType changeType;
    
    /**
     * 成长值数量
     */
    @Column(name = "growth_value", nullable = false)
    private Long growthValue;
    
    /**
     * 业务类型：PURCHASE-购买，ACTIVITY-活动，SOCIAL-社交，TASK-任务
     */
    @Column(name = "business_type", nullable = false, length = 32)
    private String businessType;
    
    /**
     * 业务ID
     */
    @Column(name = "business_id", length = 128)
    private String businessId;
    
    /**
     * 来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则
     */
    @Column(name = "source", nullable = false, length = 32)
    private String source;
    
    /**
     * 原因描述
     */
    @Column(name = "reason", length = 256)
    private String reason;
    
    /**
     * 事务ID
     */
    @Column(name = "transaction_id", nullable = false, length = 64)
    private String transactionId;
    
    /**
     * 规则代码
     */
    @Column(name = "rule_code", length = 64)
    private String ruleCode;
    
    /**
     * 基础成长值
     */
    @Column(name = "base_growth")
    private Long baseGrowth;
    
    /**
     * 倍率
     */
    @Column(name = "multiplier")
    private BigDecimal multiplier;
    
    /**
     * 扩展数据（JSON格式）
     */
    @Column(name = "extra_data", columnDefinition = "TEXT")
    private String extraData;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    @PrePersist
    protected void onCreate() {
        this.createTime = LocalDateTime.now();
        if (this.transactionId == null) {
            this.transactionId = generateTransactionId();
        }
    }
    
    private String generateTransactionId() {
        return "GT" + System.currentTimeMillis() + String.format("%04d", 
            (int)(Math.random() * 10000));
    }
}
```

#### 2.2.5 成长值规则配置实体

**实体名称**：成长值规则配置
**实体类**：GrowthRule
**表名**：growth_rule

```java
@MappedEntity("growth_rule",
       indexes = {
           @Index(name = "idx_growth_rule_code", columnList = "rule_code"),
           @Index(name = "idx_growth_rule_business", columnList = "business_type"),
           @Index(name = "idx_growth_rule_status", columnList = "status")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthRule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 规则代码
     */
    @Column(name = "rule_code", nullable = false, unique = true, length = 64)
    private String ruleCode;
    
    /**
     * 规则名称
     */
    @Column(name = "rule_name", nullable = false, length = 128)
    private String ruleName;
    
    /**
     * 业务类型
     */
    @Column(name = "business_type", nullable = false, length = 32)
    private String businessType;
    
    /**
     * 基础成长值
     */
    @Column(name = "base_growth", nullable = false)
    private Long baseGrowth;
    
    /**
     * 倍率
     */
    @Column(name = "multiplier")
    private BigDecimal multiplier;
    
    /**
     * 每日限制
     */
    @Column(name = "daily_limit")
    private Long dailyLimit;
    
    /**
     * 每月限制
     */
    @Column(name = "monthly_limit")
    private Long monthlyLimit;
    
    /**
     * 规则表达式
     */
    @Column(name = "rule_expression", columnDefinition = "TEXT")
    private String ruleExpression;
    
    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    /**
     * 状态：ACTIVE-激活，INACTIVE-停用
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 32)
    private RuleStatus status;
    
    /**
     * 优先级
     */
    @Column(name = "priority")
    private Integer priority;
    
    /**
     * 描述
     */
    @Column(name = "description", length = 512)
    private String description;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        if (this.status == null) {
            this.status = RuleStatus.ACTIVE;
        }
        if (this.multiplier == null) {
            this.multiplier = BigDecimal.ONE;
        }
        if (this.priority == null) {
            this.priority = 0;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}
```

#### 2.2.6 成长值统计实体

**实体名称**：成长值统计
**实体类**：GrowthStatistics
**表名**：growth_statistics

```java
@MappedEntity("growth_statistics",
       indexes = {
           @Index(name = "idx_growth_stat_user_date", columnList = "user_id, statistics_date"),
           @Index(name = "idx_growth_stat_date", columnList = "statistics_date")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthStatistics {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 统计日期
     */
    @Column(name = "statistics_date", nullable = false)
    private LocalDate statisticsDate;
    
    /**
     * 当日总成长值
     */
    @Column(name = "total_growth", nullable = false)
    private Long totalGrowth;
    
    /**
     * 当日消费成长值
     */
    @Column(name = "consume_growth", nullable = false)
    private Long consumeGrowth;
    
    /**
     * 当日活跃成长值
     */
    @Column(name = "activity_growth", nullable = false)
    private Long activityGrowth;
    
    /**
     * 当日社交成长值
     */
    @Column(name = "social_growth", nullable = false)
    private Long socialGrowth;
    
    /**
     * 当日任务成长值
     */
    @Column(name = "task_growth", nullable = false)
    private Long taskGrowth;
    
    /**
     * 事务数量
     */
    @Column(name = "transaction_count", nullable = false)
    private Integer transactionCount;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        
        // 初始化统计数据为0
        if (this.totalGrowth == null) this.totalGrowth = 0L;
        if (this.consumeGrowth == null) this.consumeGrowth = 0L;
        if (this.activityGrowth == null) this.activityGrowth = 0L;
        if (this.socialGrowth == null) this.socialGrowth = 0L;
        if (this.taskGrowth == null) this.taskGrowth = 0L;
        if (this.transactionCount == null) this.transactionCount = 0;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}
```

#### 2.2.7 成长值明细追踪实体

**实体名称**：成长值明细追踪
**实体类**：GrowthDetailTrace
**表名**：growth_detail_trace

```java
@MappedEntity("growth_detail_trace",
       indexes = {
           @Index(name = "idx_growth_trace_source", columnList = "source_detail_id"),
           @Index(name = "idx_growth_trace_target", columnList = "target_detail_id")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthDetailTrace {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 源明细ID
     */
    @Column(name = "source_detail_id", nullable = false)
    private Long sourceDetailId;
    
    /**
     * 目标明细ID
     */
    @Column(name = "target_detail_id", nullable = false)
    private Long targetDetailId;
    
    /**
     * 追踪类型：ADJUSTMENT-调整，CORRECTION-纠正，TRANSFER-转移
     */
    @Column(name = "trace_type", nullable = false, length = 32)
    private String traceType;
    
    /**
     * 追踪原因
     */
    @Column(name = "trace_reason", length = 256)
    private String traceReason;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 源明细关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_detail_id", insertable = false, updatable = false)
    private GrowthDetail sourceDetail;
    
    /**
     * 目标明细关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_detail_id", insertable = false, updatable = false)
    private GrowthDetail targetDetail;
    
    @PrePersist
    protected void onCreate() {
        this.createTime = LocalDateTime.now();
    }
}
```

#### 2.2.8 成长值事务实体

**实体名称**：成长值事务
**实体类**：GrowthTransaction
**表名**：growth_transaction

```java
@MappedEntity("growth_transaction",
       indexes = {
           @Index(name = "idx_growth_transaction_id", columnList = "transaction_id"),
           @Index(name = "idx_growth_transaction_user", columnList = "user_id"),
           @Index(name = "idx_growth_transaction_status", columnList = "status"),
           @Index(name = "idx_growth_transaction_time", columnList = "create_time"),
           @Index(name = "idx_growth_transaction_business", columnList = "business_type, business_id")
       })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrowthTransaction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 事务ID，全局唯一
     */
    @Column(name = "transaction_id", nullable = false, unique = true, length = 64)
    private String transactionId;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 事务类型：EARN-获得，ADJUST-调整，CORRECTION-纠正，TRANSFER-转移
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false, length = 32)
    private TransactionType transactionType;
    
    /**
     * 总成长值变动
     */
    @Column(name = "total_growth", nullable = false)
    private Long totalGrowth;
    
    /**
     * 消费成长值变动
     */
    @Column(name = "consume_growth")
    private Long consumeGrowth;
    
    /**
     * 活跃成长值变动
     */
    @Column(name = "activity_growth")
    private Long activityGrowth;
    
    /**
     * 社交成长值变动
     */
    @Column(name = "social_growth")
    private Long socialGrowth;
    
    /**
     * 任务成长值变动
     */
    @Column(name = "task_growth")
    private Long taskGrowth;
    
    /**
     * 业务类型
     */
    @Column(name = "business_type", length = 32)
    private String businessType;
    
    /**
     * 业务ID
     */
    @Column(name = "business_id", length = 128)
    private String businessId;
    
    /**
     * 事务状态：PENDING-待处理，SUCCESS-成功，FAILED-失败，CANCELLED-已取消
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 32)
    private TransactionStatus status;
    
    /**
     * 事务来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则，API-接口
     */
    @Column(name = "source", nullable = false, length = 32)
    private String source;
    
    /**
     * 事务原因
     */
    @Column(name = "reason", length = 256)
    private String reason;
    
    /**
     * 业务上下文（JSON格式）
     */
    @Column(name = "input_data", columnDefinition = "JSON")
    private String inputData;
    
    /**
     * 规则代码
     */
    @Column(name = "rule_code", length = 64)
    private String ruleCode;
    
    /**
     * 基础成长值
     */
    @Column(name = "base_growth")
    private Long baseGrowth;
    
    /**
     * 倍率
     */
    @Column(name = "multiplier")
    private BigDecimal multiplier;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 512)
    private String errorMessage;
    
    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count")
    private Integer maxRetryCount;
    
    /**
     * 下次重试时间
     */
    @Column(name = "next_retry_time")
    private LocalDateTime nextRetryTime;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    private LocalDateTime completeTime;
    
    /**
     * 关联的成长值明细列表
     */
    @OneToMany(mappedBy = "transactionId", fetch = FetchType.LAZY)
    private List<GrowthDetail> growthDetails;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        
        if (this.transactionId == null) {
            this.transactionId = generateTransactionId();
        }
        if (this.status == null) {
            this.status = TransactionStatus.PENDING;
        }
        if (this.retryCount == null) {
            this.retryCount = 0;
        }
        if (this.maxRetryCount == null) {
            this.maxRetryCount = 3;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updateTime = LocalDateTime.now();
        
        if (this.status == TransactionStatus.SUCCESS || this.status == TransactionStatus.FAILED) {
            this.completeTime = LocalDateTime.now();
        }
    }
    
    /**
     * 生成事务ID
     */
    private String generateTransactionId() {
        return "GT" + System.currentTimeMillis() + String.format("%04d", 
            (int)(Math.random() * 10000));
    }
    
    /**
     * 标记事务成功
     */
    public void markSuccess() {
        this.status = TransactionStatus.SUCCESS;
        this.completeTime = LocalDateTime.now();
        this.errorMessage = null;
    }
    
    /**
     * 标记事务失败
     */
    public void markFailed(String errorMessage) {
        this.status = TransactionStatus.FAILED;
        this.completeTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
        if (this.retryCount < this.maxRetryCount) {
            // 指数退避策略：2^n * 60秒
            long delaySeconds = (long) Math.pow(2, this.retryCount) * 60;
            this.nextRetryTime = LocalDateTime.now().plusSeconds(delaySeconds);
        } else {
            this.status = TransactionStatus.FAILED;
            this.completeTime = LocalDateTime.now();
            this.errorMessage = "超过最大重试次数";
        }
    }
    
    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return this.status == TransactionStatus.PENDING 
            && this.retryCount < this.maxRetryCount
            && (this.nextRetryTime == null || LocalDateTime.now().isAfter(this.nextRetryTime));
    }
    
    /**
     * 计算各维度成长值变动
     */
    public void calculateGrowthByType() {
        if (this.businessType == null) return;
        
        switch (this.businessType.toUpperCase()) {
            case "PURCHASE":
            case "CONSUME":
                this.consumeGrowth = this.totalGrowth;
                break;
            case "ACTIVITY":
                this.activityGrowth = this.totalGrowth;
                break;
            case "SOCIAL":
                this.socialGrowth = this.totalGrowth;
                break;
            case "TASK":
                this.taskGrowth = this.totalGrowth;
                break;
        }
    }
}
```

## 3. 业务流程设计

### 3.1 成长值获取流程

成长值获取是系统的核心业务流程，涉及事件接收、规则匹配、计算执行、账户更新等多个环节：

```
用户行为事件 → 事件验证 → 规则匹配 → 成长值计算 → 限额检查 → 账户更新 → 明细记录 → 统计更新
```

**流程详细说明：**

1. **事件接收**：接收来自各业务系统的用户行为事件
2. **事件验证**：验证事件数据的完整性和合法性
3. **规则匹配**：根据业务类型匹配适用的成长值规则
4. **成长值计算**：执行规则计算逻辑，得出应获得的成长值
5. **限额检查**：检查日限额、月限额等约束条件
6. **账户更新**：更新用户成长值账户信息
7. **明细记录**：记录成长值变动明细
8. **统计更新**：更新相关统计数据

### 3.2 成长值查询流程

成长值查询支持多维度、多层级的数据查询需求：

```
查询请求 → 参数验证 → 缓存查询 → 数据库查询 → 数据聚合 → 结果返回
```

**查询类型：**
- 用户成长值账户查询
- 成长值明细查询
- 成长值统计查询
- 成长值排行查询

### 3.3 成长值规则管理流程

成长值规则管理支持规则的动态配置和实时生效：

```
规则配置 → 规则验证 → 规则保存 → 缓存更新 → 规则生效
```

## 4. 核心服务设计

### 4.1 成长值计算引擎

成长值计算引擎是系统的核心组件，负责根据业务事件和规则配置计算用户应获得的成长值。

**设计原则：**
- 高性能：支持高并发计算请求
- 可扩展：支持复杂的计算规则
- 可配置：支持规则的动态配置
- 可追溯：记录计算过程和结果

**核心功能：**
- 规则匹配：根据业务类型匹配适用规则
- 条件判断：执行规则条件表达式
- 成长值计算：执行成长值计算逻辑
- 结果验证：验证计算结果的合理性

### 4.2 成长值服务层

成长值服务层提供完整的成长值业务功能，包括获取、查询、统计等核心服务。

**服务接口：**
- 成长值获取服务
- 成长值查询服务
- 成长值统计服务
- 成长值规则管理服务

**服务特性：**
- 事务管理：确保数据一致性
- 异常处理：完善的异常处理机制
- 性能优化：缓存策略和查询优化
- 监控告警：完整的监控指标

### 4.3 缓存策略设计

成长值服务采用多级缓存策略，提升系统性能和用户体验。

**缓存层级：**
- L1缓存：本地缓存（Caffeine）
- L2缓存：分布式缓存（Redis）
- L3缓存：数据库查询缓存

**缓存内容：**
- 用户成长值账户信息
- 成长值规则配置
- 热点查询结果
- 统计数据

**缓存策略：**
- 写入策略：Write-Through
- 失效策略：TTL + 主动失效
- 一致性策略：最终一致性

## 5. 数据存储设计

### 5.1 数据库设计原则

**设计原则：**
- 数据一致性：确保成长值数据的准确性
- 查询性能：优化高频查询场景
- 存储效率：合理的数据类型和索引设计
- 扩展性：支持业务发展和数据增长

### 5.2 分库分表策略

**分表策略：**
- 成长值明细表：按用户ID分表
- 成长值统计表：按时间分表
- 成长值事务表：按时间分表

**分表规则：**
- 用户维度：user_id % 16
- 时间维度：按月分表

### 5.3 索引设计

**核心索引：**
- 用户成长值账户：user_id（唯一索引）
- 成长值明细：(user_id, create_time)（复合索引）
- 成长值规则：rule_code（唯一索引）
- 成长值事务：transaction_id（唯一索引）

## 6. 接口设计

### 6.1 RESTful API设计

成长值服务提供标准的RESTful API接口，支持各种业务场景的调用需求。

**API设计原则：**
- 统一的响应格式
- 完善的错误处理
- 详细的接口文档
- 版本管理机制

**核心接口：**

#### 6.1.1 成长值获取接口
- **接口路径**：POST /api/v1/growth/earn
- **功能描述**：用户获取成长值
- **请求参数**：用户ID、业务类型、业务数据等
- **响应结果**：成长值事务信息

#### 6.1.2 成长值账户查询接口
- **接口路径**：GET /api/v1/growth/account/{userId}
- **功能描述**：查询用户成长值账户
- **请求参数**：用户ID
- **响应结果**：用户成长值账户信息

#### 6.1.3 成长值明细查询接口
- **接口路径**：GET /api/v1/growth/details/{userId}
- **功能描述**：查询用户成长值明细
- **请求参数**：用户ID、分页参数、时间范围等
- **响应结果**：成长值明细列表

#### 6.1.4 成长值统计查询接口
- **接口路径**：GET /api/v1/growth/statistics/{userId}
- **功能描述**：查询用户成长值统计
- **请求参数**：用户ID、统计维度、时间范围等
- **响应结果**：成长值统计数据

### 6.2 内部服务接口

成长值服务还提供内部服务接口，供其他系统模块调用。

**gRPC接口：**
- 高性能的内部服务调用
- 强类型的接口定义
- 支持流式处理

**消息队列接口：**
- 异步事件处理
- 解耦系统依赖
- 提高系统可用性

## 7. 监控与运维

### 7.1 监控指标设计

**业务指标：**
- 成长值获取量：每日/每小时成长值获取总量
- 用户活跃度：活跃用户数量和成长值获取频次
- 规则命中率：各规则的命中次数和成功率
- 异常率：失败事务占比和错误类型分布

**技术指标：**
- 接口性能：响应时间、吞吐量、错误率
- 系统资源：CPU使用率、内存使用率、磁盘IO
- 数据库性能：连接数、慢查询、锁等待
- 缓存性能：命中率、内存使用、网络延迟

### 7.2 日志管理

**日志分类：**
- 业务日志：记录业务操作和状态变更
- 系统日志：记录系统运行状态和异常
- 访问日志：记录API调用和性能数据
- 审计日志：记录敏感操作和数据变更

**日志格式：**
- 结构化日志：JSON格式，便于解析和查询
- 统一字段：请求ID、用户ID、时间戳等
- 敏感数据脱敏：保护用户隐私

### 7.3 告警机制

**告警级别：**
- 紧急：系统不可用、数据丢失
- 重要：性能严重下降、错误率过高
- 警告：资源使用率过高、异常增多
- 信息：系统状态变更、配置更新

**告警渠道：**
- 短信通知：紧急和重要级别告警
- 邮件通知：所有级别告警
- 企业微信：实时告警推送
- 监控面板：可视化告警展示

## 8. 安全设计

### 8.1 数据安全

**数据加密：**
- 传输加密：HTTPS/TLS协议
- 存储加密：敏感数据字段加密
- 密钥管理：统一的密钥管理服务

**数据脱敏：**
- 日志脱敏：敏感信息不记录到日志
- 接口脱敏：返回数据中的敏感信息脱敏
- 测试数据脱敏：测试环境使用脱敏数据

### 8.2 访问控制

**身份认证：**
- JWT Token认证
- API密钥认证
- OAuth2.0授权

**权限控制：**
- 基于角色的访问控制（RBAC）
- 接口级权限控制
- 数据级权限控制

### 8.3 防护机制

**接口防护：**
- 请求限流：防止恶意请求
- 参数校验：防止注入攻击
- 重放攻击防护：请求签名和时间戳验证

**系统防护：**
- 防火墙配置：网络层防护
- 入侵检测：异常行为监控
- 安全审计：操作日志审计

## 9. 性能优化

### 9.1 查询优化

**数据库优化：**
- 索引优化：合理设计索引策略
- 查询优化：优化SQL语句和执行计划
- 连接池优化：合理配置数据库连接池
- 读写分离：分离读写操作，提高并发能力

**缓存优化：**
- 缓存预热：系统启动时预加载热点数据
- 缓存更新：合理的缓存更新策略
- 缓存穿透防护：布隆过滤器防止缓存穿透
- 缓存雪崩防护：缓存过期时间随机化

### 9.2 计算优化

**算法优化：**
- 规则匹配优化：使用高效的匹配算法
- 计算并行化：支持并行计算提高性能
- 结果缓存：缓存计算结果避免重复计算

**资源优化：**
- 线程池优化：合理配置线程池参数
- 内存优化：避免内存泄漏和过度使用
- GC优化：优化垃圾回收参数

### 9.3 架构优化

**服务拆分：**
- 微服务架构：按业务功能拆分服务
- 服务治理：服务注册、发现、负载均衡
- 熔断降级：防止服务雪崩

**异步处理：**
- 消息队列：异步处理非关键业务
- 事件驱动：基于事件的异步架构
- 批量处理：批量处理提高效率

## 10. 扩展性设计

### 10.1 水平扩展

**服务扩展：**
- 无状态设计：服务实例可任意扩展
- 负载均衡：智能分发请求
- 自动扩缩容：根据负载自动调整实例数量

**数据扩展：**
- 分库分表：支持数据水平扩展
- 数据迁移：平滑的数据迁移方案
- 一致性哈希：数据分布均匀

### 10.2 功能扩展

**规则扩展：**
- 插件化规则：支持自定义规则插件
- 规则引擎：灵活的规则配置和执行
- 多租户支持：支持多租户规则隔离

**业务扩展：**
- 多业务类型：支持新的业务类型接入
- 多维度成长值：支持更多成长值维度
- 国际化支持：支持多语言和多时区

## 11. 部署方案

### 11.1 容器化部署

**Docker容器：**
- 标准化部署环境
- 快速启动和扩展
- 资源隔离和限制

**Kubernetes编排：**
- 自动化部署和管理
- 服务发现和负载均衡
- 健康检查和自愈能力

### 11.2 环境管理

**多环境部署：**
- 开发环境：开发和调试
- 测试环境：功能和性能测试
- 预发环境：生产前验证
- 生产环境：正式服务

**配置管理：**
- 配置中心：统一配置管理
- 环境隔离：不同环境独立配置
- 动态配置：支持配置热更新

### 11.3 发布策略

**灰度发布：**
- 分批发布：逐步扩大发布范围
- 流量控制：控制新版本流量比例
- 快速回滚：发现问题快速回滚

**蓝绿部署：**
- 零停机发布：无缝切换服务版本
- 风险控制：降低发布风险
- 快速验证：快速验证新版本功能

## 12. 测试策略

### 12.1 测试分层

**单元测试：**
- 代码覆盖率：确保核心逻辑覆盖
- 边界测试：测试边界条件和异常情况
- Mock测试：隔离外部依赖

**集成测试：**
- 接口测试：验证API接口功能
- 数据库测试：验证数据操作正确性
- 缓存测试：验证缓存逻辑

**系统测试：**
- 功能测试：验证业务功能完整性
- 性能测试：验证系统性能指标
- 稳定性测试：长时间运行稳定性

### 12.2 自动化测试

**持续集成：**
- 代码提交触发自动测试
- 测试结果实时反馈
- 测试失败阻止发布

**测试数据管理：**
- 测试数据准备和清理
- 数据隔离和保护
- 测试环境数据同步

### 12.3 性能测试

**压力测试：**
- 并发用户测试
- 峰值流量测试
- 资源极限测试

**性能基准：**
- 响应时间要求
- 吞吐量指标
- 资源使用限制
