﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 冻结积分结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointFreezeResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;    
    /**
     * 冻结记录ID
     */
    private String freezeId;    
    /**
     * 实际冻结的积分数量
     */
    private Long point;    
    /**
     * 冻结前可用余额
     */
    private Long beforeBalance;    
    /**
     * 冻结后可用余额
     */
    private Long afterBalance;    
    /**
     * 冻结后冻结余额
     */
    private Long frozenBalance;    
    /**
     * 冻结过期时间
     */
    private String expireTime;    
    /**
     * 处理状态
     */
    private String status;}
