﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级倍率
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelMultiplier extends Extensible {    
    /**
     * 倍率ID
     */
    private String multiplierId;    
    /**
     * 倍率代码
     */
    private String multiplierCode;    
    /**
     * 倍率名称
     */
    private String multiplierName;    
    /**
     * 倍率类型
     */
    private String multiplierType;    
    /**
     * 倍率值
     */
    private Double multiplierValue;    
    /**
     * 倍率描述
     */
    private String description;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;}
