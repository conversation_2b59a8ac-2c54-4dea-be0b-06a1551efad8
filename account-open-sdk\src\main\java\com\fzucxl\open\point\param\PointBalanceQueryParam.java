package com.fzucxl.open.point.param;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户积分余额参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointBalanceQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户ID不能为空")
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易ID，用于关联上下游交易
     */
    private String transactionId;}
