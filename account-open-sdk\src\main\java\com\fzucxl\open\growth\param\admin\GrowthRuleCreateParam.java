﻿package com.fzucxl.open.growth.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建成长值规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRuleCreateParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则代码，唯一标识
     */
    private String ruleCode;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 基础成长值
     */
    private Long baseGrowth;    
    /**
     * 倍率，默认1.0
     */
    private java.math.BigDecimal multiplier;    
    /**
     * 每日限制
     */
    private Long dailyLimit;    
    /**
     * 每月限制
     */
    private Long monthlyLimit;    
    /**
     * 规则表达式
     */
    private String ruleExpression;    
    /**
     * 优先级，数字越大优先级越高
     */
    private Integer priority;    
    /**
     * 状态：ACTIVE-生效，INACTIVE-未生效，默认ACTIVE
     */
    private String status;    
    /**
     * 开始时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String startTime;    
    /**
     * 结束时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String endTime;}
