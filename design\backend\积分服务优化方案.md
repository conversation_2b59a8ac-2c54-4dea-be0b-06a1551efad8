# 积分服务优化方案 - 有效积分记录实体

## 1. 新增有效积分记录实体设计

### 1.1 实体定义
已在积分服务技术设计文档中新增 `PointValidRecord` 实体，包含以下核心字段：

- **userId**: 用户ID
- **transactionId**: 交易ID，关联积分明细
- **pointType**: 积分类型
- **issuedPoint**: 发放积分数
- **remainingPoint**: 剩余积分数
- **issueTime**: 发放时间
- **expireTime**: 过期时间
- **recordStatus**: 记录状态（有效/已过期/已消费完/已冻结）

### 1.2 数据库表结构
```sql
CREATE TABLE point_valid_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    transaction_id VARCHAR(64) NOT NULL COMMENT '交易ID',
    point_type VARCHAR(32) NOT NULL COMMENT '积分类型',
    issued_point BIGINT NOT NULL COMMENT '发放积分数',
    remaining_point BIGINT NOT NULL COMMENT '剩余积分数',
    issue_time DATETIME NOT NULL COMMENT '发放时间',
    expire_time DATETIME COMMENT '过期时间',
    record_status VARCHAR(16) NOT NULL DEFAULT 'VALID' COMMENT '记录状态',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 0 COMMENT '版本号',
    
    INDEX idx_user_id_status (user_id, record_status),
    INDEX idx_user_id_expire (user_id, expire_time),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_expire_time_status (expire_time, record_status)
) COMMENT='有效积分记录表';
```

## 2. 核心业务逻辑实现

### 2.1 积分获取流程优化
```java
@Service
@Transactional
public class PointService {
    
    @Autowired
    private UserPointRepository accountRepository;
    
    @Autowired
    private PointDetailRepository detailRepository;
    
    @Autowired
    private PointValidRecordRepository validRecordRepository;
    
    @Autowired
    private PointCalculationEngine calculationEngine;
    
    public PointTransaction earnPoint(EarnPointRequest request) {
        // 1. 参数校验
        validateEarnPointRequest(request);
        
        // 2. 分布式锁防并发
        String lockKey = "point:lock:" + request.getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (!lock.tryLock(5, TimeUnit.SECONDS)) {
                throw new BusinessException("系统繁忙，请稍后重试");
            }
            
            // 3. 获取用户积分账户
            UserPoint account = getOrCreateAccount(request.getUserId());
            
            // 4. 计算积分
            PointCalculationResult result = calculationEngine.calculate(request);
            
            // 5. 检查每日限额
            checkDailyLimit(request.getUserId(), result.getFinalPoint());
            
            // 6. 更新账户余额
            account.setTotalPoint(account.getTotalPoint() + result.getFinalPoint());
            account.setAvailablePoint(account.getAvailablePoint() + result.getFinalPoint());
            accountRepository.save(account);
            
            // 7. 记录积分明细
            PointDetail detail = buildPointDetail(request, result, account);
            detailRepository.save(detail);
            
            // 8. 创建有效积分记录
            PointValidRecord validRecord = buildValidRecord(request, result, detail);
            validRecordRepository.save(validRecord);
            
            // 9. 清除缓存
            clearAccountCache(request.getUserId());
            
            // 10. 发布事件
            publishPointEarnedEvent(request.getUserId(), result.getFinalPoint());
            
            return buildPointTransaction(detail);
            
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 构建有效积分记录
     */
    private PointValidRecord buildValidRecord(EarnPointRequest request, 
                                             PointCalculationResult result, 
                                             PointDetail detail) {
        PointValidRecord validRecord = new PointValidRecord();
        validRecord.setUserId(request.getUserId());
        validRecord.setTransactionId(detail.getTransactionId());
        validRecord.setPointType(detail.getPointType());
        validRecord.setIssuedPoint(result.getFinalPoint());
        validRecord.setRemainingPoint(result.getFinalPoint());
        validRecord.setIssueTime(LocalDateTime.now());
        validRecord.setExpireTime(calculateExpireTime(request.getBusinessType()));
        validRecord.setRecordStatus(ValidRecordStatus.VALID);
        
        return validRecord;
    }
    
    /**
     * 计算过期时间
     */
    private LocalDateTime calculateExpireTime(String businessType) {
        // 根据业务类型计算过期时间
        // 例如：年底过期的积分，过期时间为当年12月31日23:59:59
        return LocalDateTime.of(LocalDate.now().getYear(), 12, 31, 23, 59, 59);
    }
}
```

### 2.2 积分消费流程优化
```java
public PointTransaction consumePoint(ConsumePointRequest request) {
    // 1. 参数校验
    validateConsumePointRequest(request);
    
    // 2. 分布式锁
    String lockKey = "point:lock:" + request.getUserId();
    RLock lock = redissonClient.getLock(lockKey);
    
    try {
        if (!lock.tryLock(5, TimeUnit.SECONDS)) {
            throw new BusinessException("系统繁忙，请稍后重试");
        }
        
        // 3. 获取用户积分账户
        UserPoint account = accountRepository.findByUserId(request.getUserId());
        if (account == null) {
            throw new BusinessException("用户积分账户不存在");
        }
        
        // 4. 检查积分余额（实时计算可用余额）
        Long realTimeAvailablePoint = calculateRealTimeAvailablePoint(request.getUserId());
        if (realTimeAvailablePoint < request.getPointAmount()) {
            throw new BusinessException("积分余额不足");
        }
        
        // 5. 扣减积分（FIFO原则，优先消费即将过期的积分）
        List<PointValidRecord> availableRecords = getAvailableValidRecords(request.getUserId());
        long remainingAmount = request.getPointAmount();
        
        for (PointValidRecord record : availableRecords) {
            if (remainingAmount <= 0) break;
            
            long consumeAmount = Math.min(remainingAmount, record.getRemainingPoint());
            record.setRemainingPoint(record.getRemainingPoint() - consumeAmount);
            
            if (record.getRemainingPoint() == 0) {
                record.setRecordStatus(ValidRecordStatus.CONSUMED);
            }
            
            validRecordRepository.save(record);
            remainingAmount -= consumeAmount;
        }
        
        // 6. 更新账户余额
        account.setAvailablePoint(realTimeAvailablePoint - request.getPointAmount());
        accountRepository.save(account);
        
        // 7. 记录消费明细
        PointDetail consumeDetail = buildConsumeDetail(request, account);
        detailRepository.save(consumeDetail);
        
        // 8. 清除缓存
        clearAccountCache(request.getUserId());
        
        // 9. 发布事件
        publishPointConsumedEvent(request.getUserId(), request.getPointAmount());
        
        return buildPointTransaction(consumeDetail);
        
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}

/**
 * 获取可用的有效积分记录（按过期时间排序，优先消费即将过期的）
 */
private List<PointValidRecord> getAvailableValidRecords(Long userId) {
    return validRecordRepository.findByUserIdAndRecordStatusAndRemainingPointGreaterThanOrderByExpireTimeAsc(
        userId, ValidRecordStatus.VALID, 0L);
}
```

### 2.3 实时积分余额查询优化
```java
@Service
public class PointQueryService {
    
    @Autowired
    private PointValidRecordRepository validRecordRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 获取用户实时可用积分余额
     * 核心功能：实时扣除已过期但状态仍为有效的积分
     */
    public Long getRealTimeAvailablePoint(Long userId) {
        String cacheKey = "point:realtime:available:" + userId;
        
        // 1. 先尝试从缓存获取（缓存时间较短，5分钟）
        Long cachedPoint = (Long) redisTemplate.opsForValue().get(cacheKey);
        if (cachedPoint != null) {
            return cachedPoint;
        }
        
        // 2. 实时计算可用积分
        Long availablePoint = calculateAvailablePointRealTime(userId);
        
        // 3. 缓存结果
        redisTemplate.opsForValue().set(cacheKey, availablePoint, Duration.ofMinutes(5));
        
        return availablePoint;
    }
    
    /**
     * 实时计算可用积分
     */
    private Long calculateAvailablePointRealTime(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        // 查询所有有效状态的积分记录
        List<PointValidRecord> validRecords = validRecordRepository
            .findByUserIdAndRecordStatusAndRemainingPointGreaterThan(
                userId, ValidRecordStatus.VALID, 0L);
        
        Long totalAvailablePoint = 0L;
        List<PointValidRecord> expiredRecords = new ArrayList<>();
        
        for (PointValidRecord record : validRecords) {
            // 检查是否已过期
            if (record.getExpireTime() != null && record.getExpireTime().isBefore(now)) {
                // 标记为过期，但不立即更新数据库（异步处理）
                record.setRecordStatus(ValidRecordStatus.EXPIRED);
                expiredRecords.add(record);
            } else {
                // 未过期，计入可用余额
                totalAvailablePoint += record.getRemainingPoint();
            }
        }
        
        // 异步更新过期记录状态
        if (!expiredRecords.isEmpty()) {
            CompletableFuture.runAsync(() -> {
                batchUpdateExpiredRecords(expiredRecords);
            });
        }
        
        return totalAvailablePoint;
    }
    
    /**
     * 批量更新过期记录状态
     */
    @Async("pointTaskExecutor")
    public void batchUpdateExpiredRecords(List<PointValidRecord> expiredRecords) {
        try {
            validRecordRepository.saveAll(expiredRecords);
            log.info("批量更新过期积分记录完成，数量: {}", expiredRecords.size());
        } catch (Exception e) {
            log.error("批量更新过期积分记录失败", e);
        }
    }
    
    /**
     * 获取用户积分详情（包含过期信息）
     */
    public PointBalanceDetail getPointBalanceDetail(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        // 查询所有有效积分记录
        List<PointValidRecord> validRecords = validRecordRepository
            .findByUserIdAndRecordStatusAndRemainingPointGreaterThan(
                userId, ValidRecordStatus.VALID, 0L);
        
        Long availablePoint = 0L;
        Long expiringSoonPoint = 0L; // 即将过期的积分（30天内）
        LocalDateTime expiringSoonThreshold = now.plusDays(30);
        
        for (PointValidRecord record : validRecords) {
            if (record.getExpireTime() != null && record.getExpireTime().isBefore(now)) {
                // 已过期，不计入可用余额
                continue;
            }
            
            availablePoint += record.getRemainingPoint();
            
            // 检查是否即将过期
            if (record.getExpireTime() != null && 
                record.getExpireTime().isBefore(expiringSoonThreshold)) {
                expiringSoonPoint += record.getRemainingPoint();
            }
        }
        
        return PointBalanceDetail.builder()
            .userId(userId)
            .availablePoint(availablePoint)
            .expiringSoonPoint(expiringSoonPoint)
            .queryTime(now)
            .build();
    }
}
```

### 2.4 定时过期任务优化
```java
@Component
@Slf4j
public class PointExpirationTask {
    
    @Autowired
    private PointValidRecordRepository validRecordRepository;
    
    @Autowired
    private UserPointRepository accountRepository;
    
    /**
     * 定时处理过期积分（每小时执行一次）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void processExpiredPoint() {
        log.info("开始执行积分过期任务");
        
        LocalDateTime now = LocalDateTime.now();
        int batchSize = 1000;
        int processedCount = 0;
        
        while (true) {
            // 分批查询过期的有效积分记录
            List<PointValidRecord> expiredRecords = validRecordRepository
                .findExpiredValidRecords(now, batchSize);
            
            if (expiredRecords.isEmpty()) {
                break;
            }
            
            // 批量处理过期记录
            processExpiredRecordsBatch(expiredRecords);
            processedCount += expiredRecords.size();
            
            log.info("已处理过期积分记录: {} 条", processedCount);
            
            // 避免长时间占用数据库资源
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("积分过期任务完成，共处理: {} 条记录", processedCount);
    }
    
    /**
     * 批量处理过期记录
     */
    @Transactional
    public void processExpiredRecordsBatch(List<PointValidRecord> expiredRecords) {
        Map<Long, Long> userExpiredPoint = new HashMap<>();
        
        // 1. 更新记录状态为过期
        for (PointValidRecord record : expiredRecords) {
            record.setRecordStatus(ValidRecordStatus.EXPIRED);
            
            // 统计每个用户的过期积分
            userExpiredPoint.merge(record.getUserId(), record.getRemainingPoint(), Long::sum);
            
            // 清零剩余积分
            record.setRemainingPoint(0L);
        }
        
        validRecordRepository.saveAll(expiredRecords);
        
        // 2. 更新用户账户的可用积分
        for (Map.Entry<Long, Long> entry : userExpiredPoint.entrySet()) {
            Long userId = entry.getKey();
            Long expiredPoint = entry.getValue();
            
            UserPoint account = accountRepository.findByUserId(userId);
            if (account != null) {
                account.setAvailablePoint(account.getAvailablePoint() - expiredPoint);
                account.setExpiredPoint(account.getExpiredPoint() + expiredPoint);
                accountRepository.save(account);
                
                // 清除用户积分缓存
                clearUserPointCache(userId);
            }
        }
    }
    
    private void clearUserPointCache(Long userId) {
        String cacheKey = "point:realtime:available:" + userId;
        redisTemplate.delete(cacheKey);
    }
}
```

## 3. Repository接口定义

### 3.1 PointValidRecordRepository
```java
@Repository
public interface PointValidRecordRepository extends JpaRepository<PointValidRecord, Long> {
    
    /**
     * 查询用户有效的积分记录
     */
    List<PointValidRecord> findByUserIdAndRecordStatusAndRemainingPointGreaterThan(
        Long userId, ValidRecordStatus status, Long remainingPoint);
    
    /**
     * 查询用户有效的积分记录，按过期时间排序
     */
    List<PointValidRecord> findByUserIdAndRecordStatusAndRemainingPointGreaterThanOrderByExpireTimeAsc(
        Long userId, ValidRecordStatus status, Long remainingPoint);
    
    /**
     * 查询过期的有效积分记录
     */
    @Query("SELECT r FROM PointValidRecord r WHERE r.recordStatus = 'VALID' " +
           "AND r.expireTime < :expireTime AND r.remainingPoint > 0 " +
           "ORDER BY r.expireTime ASC")
    List<PointValidRecord> findExpiredValidRecords(@Param("expireTime") LocalDateTime expireTime, 
                                                   Pageable pageable);
    
    default List<PointValidRecord> findExpiredValidRecords(LocalDateTime expireTime, int limit) {
        return findExpiredValidRecords(expireTime, PageRequest.of(0, limit));
    }
    
    /**
     * 统计用户可用积分总额
     */
    @Query("SELECT COALESCE(SUM(r.remainingPoint), 0) FROM PointValidRecord r " +
           "WHERE r.userId = :userId AND r.recordStatus = 'VALID' " +
           "AND (r.expireTime IS NULL OR r.expireTime > :currentTime)")
    Long sumAvailablePointByUserId(@Param("userId") Long userId, 
                                   @Param("currentTime") LocalDateTime currentTime);
}
```

## 4. 性能优化建议

### 4.1 索引优化
```sql
-- 核心查询索引
CREATE INDEX idx_point_valid_user_status_remaining 
ON point_valid_record(user_id, record_status, remaining_point);

-- 过期任务索引
CREATE INDEX idx_point_valid_expire_status_remaining 
ON point_valid_record(expire_time, record_status, remaining_point);

-- 消费排序索引
CREATE INDEX idx_point_valid_user_expire_remaining 
ON point_valid_record(user_id, expire_time, remaining_point);
```

### 4.2 缓存策略
```java
@Service
public class PointValidRecordCacheService {
    
    private static final String USER_AVAILABLE_POINTS_KEY = "point:valid:available:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(5);
    
    /**
     * 缓存用户可用积分
     */
    public void cacheUserAvailablePoint(Long userId, Long availablePoint) {
        String cacheKey = USER_AVAILABLE_POINTS_KEY + userId;
        redisTemplate.opsForValue().set(cacheKey, availablePoint, CACHE_TTL);
    }
    
    /**
     * 获取缓存的用户可用积分
     */
    public Long getCachedUserAvailablePoint(Long userId) {
        String cacheKey = USER_AVAILABLE_POINTS_KEY + userId;
        return (Long) redisTemplate.opsForValue().get(cacheKey);
    }
    
    /**
     * 清除用户积分缓存
     */
    public void clearUserPointCache(Long userId) {
        String cacheKey = USER_AVAILABLE_POINTS_KEY + userId;
        redisTemplate.delete(cacheKey);
    }
}
```

## 5. 总结

通过新增有效积分记录实体，实现了以下核心功能：

1. **实时过期检查**：在积分余额查询时能够实时识别并扣除已过期的积分
2. **精确余额计算**：基于有效积分记录表进行余额计算，避免复杂的明细表聚合查询
3. **优化消费策略**：支持FIFO消费策略，优先消费即将过期的积分
4. **异步过期处理**：通过定时任务异步处理过期积分，不影响实时查询性能
5. **性能优化**：通过合理的索引设计和缓存策略，保证高并发场景下的查询性能

这种设计既解决了年底积分过期的业务需求，又保证了系统的高性能和数据一致性。