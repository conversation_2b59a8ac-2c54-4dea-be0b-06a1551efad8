package com.fzucxl.point.rule.dto;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.serde.annotation.Serdeable;

import java.util.Map;

/**
 * 规则测试结果DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Serdeable
public class RuleTestResult {
    
    private String accountCode;
    private String ruleCode;
    private String testUserId;
    private Map<String, Object> testContext;
    private Object result;
    private Long executionTime;
    private boolean success;
    private String errorMessage;
    
    public RuleTestResult() {
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private RuleTestResult result = new RuleTestResult();
        
        public Builder accountCode(String accountCode) {
            result.accountCode = accountCode;
            return this;
        }
        
        public Builder ruleCode(String ruleCode) {
            result.ruleCode = ruleCode;
            return this;
        }
        
        public Builder testUserId(String testUserId) {
            result.testUserId = testUserId;
            return this;
        }
        
        public Builder testContext(Map<String, Object> testContext) {
            result.testContext = testContext;
            return this;
        }
        
        public Builder result(Object result) {
            this.result.result = result;
            return this;
        }
        
        public Builder executionTime(Long executionTime) {
            result.executionTime = executionTime;
            return this;
        }
        
        public Builder success(boolean success) {
            result.success = success;
            return this;
        }
        
        public Builder errorMessage(String errorMessage) {
            result.errorMessage = errorMessage;
            return this;
        }
        
        public RuleTestResult build() {
            return result;
        }
    }
    
    // Getter and Setter methods
    public String getAccountCode() {
        return accountCode;
    }
    
    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }
    
    public String getRuleCode() {
        return ruleCode;
    }
    
    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }
    
    public String getTestUserId() {
        return testUserId;
    }
    
    public void setTestUserId(String testUserId) {
        this.testUserId = testUserId;
    }
    
    public Map<String, Object> getTestContext() {
        return testContext;
    }
    
    public void setTestContext(Map<String, Object> testContext) {
        this.testContext = testContext;
    }
    
    public Object getResult() {
        return result;
    }
    
    public void setResult(Object result) {
        this.result = result;
    }
    
    public Long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}