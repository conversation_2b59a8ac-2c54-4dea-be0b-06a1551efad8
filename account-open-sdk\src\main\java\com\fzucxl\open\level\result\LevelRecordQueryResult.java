﻿package com.fzucxl.open.level.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级变更记录结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelRecordQueryResult extends Extensible {    
    /**
     * 等级变更记录列表
     */
    private java.util.List<LevelRecord> recordList;    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;}
