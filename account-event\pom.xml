<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>account-event</artifactId>
    <version>0.1.0</version>
    <packaging>${packaging}</packaging>

    <parent>
        <groupId>com.fzucxl</groupId>
        <artifactId>fast-account</artifactId>
        <version>0.1.0</version>
    </parent>

    <properties>
        <packaging>jar</packaging>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <micronaut.version>4.9.2</micronaut.version>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>com.fzucxl.aot.generated</micronaut.aot.packageName>
        <micronaut.runtime>netty</micronaut.runtime>
        <exec.mainClass>com.fzucxl.Application</exec.mainClass>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fzucxl</groupId>
            <artifactId>account-core</artifactId>
            <version>0.1.0</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>io.micronaut.maven</groupId>
                <artifactId>micronaut-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!-- Uncomment to enable incremental compilation -->
                    <!-- <useIncrementalCompilation>false</useIncrementalCompilation> -->

                    <annotationProcessorPaths combine.children="append">
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-http-validation</artifactId>
                            <version>${micronaut.core.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.serde</groupId>
                            <artifactId>micronaut-serde-processor</artifactId>
                            <version>${micronaut.serialization.version}</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject</artifactId>
                                </exclusion>
                            </exclusions>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Amicronaut.processing.group=com.fzucxl</arg>
                        <arg>-Amicronaut.processing.module=account-event</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
