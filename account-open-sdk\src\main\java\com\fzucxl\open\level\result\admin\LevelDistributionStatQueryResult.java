﻿package com.fzucxl.open.level.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询等级分布统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelDistributionStatQueryResult extends Extensible {    
    /**
     * 等级分布列表
     */
    private java.util.List<LevelDistribution> distributionList;    
    /**
     * 总用户数
     */
    private Long totalUser;    
    /**
     * 统计时间
     */
    private String statTime;}
