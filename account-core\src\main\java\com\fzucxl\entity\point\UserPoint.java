package com.fzucxl.entity.point;

import com.fzucxl.open.base.AccountStatus;
import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户积分实体
 * 
 * <AUTHOR>
 */
@MappedEntity("user_point")
@Data
public class UserPoint {
    
    @Id
    @GeneratedValue
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;

    /**
     * 总积分
     */
    private Long totalPoint;
    
    /**
     * 可用积分
     */
    private Long availablePoint;
    
    /**
     * 冻结积分
     */
    private Long frozenPoint;
    
    /**
     * 已过期积分
     */
    private Long expiredPoint;

    
    /**
     * 账户状态：ACTIVE-正常，INACTIVE-停用，FROZEN-冻结，DELETED-已删除
     */
    private AccountStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Integer version;
    
    // 构造函数
    public UserPoint() {
        this.totalPoint = 0L;
        this.availablePoint = 0L;
        this.frozenPoint = 0L;
        this.expiredPoint = 0L;
        this.status = AccountStatus.valueOf("ACTIVE");
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.version = 0;
    }
}
