﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章账户模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccount extends Extensible {    
    /**
     * 账户ID
     */
    private Long accountId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 账户类型
     */
    private String accountType;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 勋章总数
     */
    private Integer badgeCount;    
    /**
     * 有效勋章数
     */
    private Integer activeBadgeCount;    
    /**
     * 过期勋章数
     */
    private Integer expiredBadgeCount;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;}
