﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建积分规则参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleCreateParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 规则类型
     */
    private String ruleType;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 积分值
     */
    private Long pointValue;    
    /**
     * 最大积分
     */
    private Long maxPoint;    
    /**
     * 每日限制
     */
    private Long dailyLimit;    
    /**
     * 每月限制
     */
    private Long monthlyLimit;    
    /**
     * 总限制
     */
    private Long totalLimit;    
    /**
     * 有效期天数
     */
    private Integer expireDays;    
    /**
     * 生效时间
     */
    private String effectiveTime;    
    /**
     * 失效时间
     */
    private String expireTime;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 规则配置JSON
     */
    private String ruleConfig;    
    /**
     * 备注
     */
    private String remark;}
