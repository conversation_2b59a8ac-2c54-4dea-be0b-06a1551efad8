# 等级模块PRD产品设计文档

## 1. 核心概念

### 1.1 等级定义
等级是基于用户在平台上的活跃度、贡献度等综合表现，将用户划分为不同层次的身份标识。等级体现用户在平台的地位和价值，不同等级享受不同的权益和特权。

### 1.2 等级特性
- **层次性**：等级从低到高呈现明确的层次结构
- **成长性**：用户可通过积累经验值提升等级
- **权益性**：不同等级享受差异化的权益和特权
- **稳定性**：等级一般只升不降，体现用户历史贡献

## 2. 模型结构设计

### 2.1 用户等级模型
```
UserLevel {
    userId: Long           // 用户ID
    currentLevel: Integer  // 当前等级
    currentExp: Long       // 当前经验值
    nextLevelExp: Long     // 下一等级所需经验值
    totalExp: Long         // 总经验值
    levelUpTime: DateTime  // 最近升级时间
    createTime: DateTime   // 创建时间
    updateTime: DateTime   // 更新时间
}
```

### 2.2 等级配置模型
```
LevelRule {
    level: Integer        // 等级
    levelName: String     // 等级名称
    levelIcon: String     // 等级图标
    requiredExp: Long     // 所需经验值
    privileges: String    // 等级特权（JSON格式）
    description: String   // 等级描述
    status: String        // 状态
    createTime: DateTime  // 创建时间
}
```

### 2.3 等级升级记录模型
```
LevelUpRecord {
    id: Long              // 记录ID
    userId: Long          // 用户ID
    fromLevel: Integer    // 原等级
    toLevel: Integer      // 新等级
    expBefore: Long       // 升级前经验值
    expAfter: Long        // 升级后经验值
    upgradeTime: DateTime // 升级时间
    source: String        // 升级来源
}
```

## 3. 业务场景

### 3.1 等级提升场景
- **经验值累积**：用户通过各种行为获得经验值，达到阈值自动升级
- **任务完成升级**：完成特定任务直接提升等级
- **消费升级**：根据消费金额累积经验值升级
- **活跃度升级**：基于用户活跃度表现提升等级
- **特殊活动升级**：参与特殊活动获得升级机会

### 3.2 等级权益场景
- **折扣特权**：高等级用户享受更大的购物折扣
- **专属客服**：VIP等级用户享受专属客服服务
- **优先权**：在抢购、预约等场景享受优先权
- **专属商品**：高等级用户可购买专属商品
- **免费服务**：享受部分免费增值服务
- **积分加成**：获得积分时享受等级加成

### 3.3 等级展示场景
- **个人中心**：在用户个人中心显示当前等级和进度
- **社区互动**：在评论、发帖时显示用户等级标识
- **排行榜**：展示等级排行榜，激励用户提升等级
- **等级说明**：提供等级体系说明和权益介绍

## 4. API列表设计

### 4.1 等级查询接口
```
GET /api/v1/level/info
功能：查询用户等级信息
参数：userId
返回：用户等级详情
```

```
GET /api/v1/level/config
功能：查询等级配置信息
参数：level（可选）
返回：等级配置列表
```

```
GET /api/v1/level/progress
功能：查询等级进度
参数：userId
返回：当前等级进度信息
```

### 4.2 等级操作接口
```
POST /api/v1/level/addExp
功能：增加用户经验值
参数：userId, exp, source, description
返回：操作结果和等级变化
```

```
POST /api/v1/level/upgrade
功能：手动升级用户等级
参数：userId, targetLevel, reason
返回：升级结果
```

```
GET /api/v1/level/privileges
功能：查询等级特权
参数：userId 或 level
返回：特权列表
```

### 4.3 等级记录接口
```
GET /api/v1/level/records
功能：查询等级升级记录
参数：userId, pageNum, pageSize
返回：升级记录列表
```

```
GET /api/v1/level/ranking
功能：查询等级排行榜
参数：rankType, limit
返回：排行榜数据
```

### 4.4 等级管理接口
```
POST /api/v1/level/config
功能：创建等级配置
参数：等级配置信息
返回：创建结果
```

```
PUT /api/v1/level/config/{level}
功能：更新等级配置
参数：level, 配置信息
返回：更新结果
```

```
GET /api/v1/level/statistics
功能：查询等级统计数据
参数：startTime, endTime
返回：统计数据
```

## 5. 等级体系设计

### 5.1 等级划分
```
等级1：新手 (0-99经验值)
等级2：青铜 (100-499经验值)
等级3：白银 (500-1499经验值)
等级4：黄金 (1500-3999经验值)
等级5：铂金 (4000-9999经验值)
等级6：钻石 (10000-24999经验值)
等级7：大师 (25000-49999经验值)
等级8：王者 (50000+经验值)
```

### 5.2 经验值获取规则
- 每日签到：+10经验值
- 完成订单：订单金额/10经验值
- 发表评价：+20经验值
- 分享商品：+5经验值
- 邀请好友：+100经验值
- 参与活动：+50经验值

### 5.3 等级特权设计
- **折扣特权**：等级越高，享受折扣越大
- **积分加成**：高等级用户获得积分时享受加成
- **专属客服**：VIP等级享受专属客服通道
- **免邮特权**：达到一定等级免运费门槛降低
- **优先购买**：新品发布时享受优先购买权

## 6. 技术实现要点

### 6.1 等级计算
- 实现经验值到等级的自动转换
- 支持等级升级的实时触发
- 建立等级变化的事件通知机制

### 6.2 权益管理
- 动态配置等级权益内容
- 实现权益的自动生效和失效
- 支持权益的个性化定制

### 6.3 性能优化
- 用户等级信息缓存
- 等级计算的异步处理
- 等级排行榜的定时更新