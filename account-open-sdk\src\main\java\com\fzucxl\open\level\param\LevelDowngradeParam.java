﻿package com.fzucxl.open.level.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级降级参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelDowngradeParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 目标等级
     */
    private Integer targetLevel;    
    /**
     * 降级原因
     */
    private String downgradeReason;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
