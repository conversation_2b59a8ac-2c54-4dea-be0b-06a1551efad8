# 积分系统API设计文档

## 1. 概述

### 1.1 文档说明

本文档定义了积分系统的RESTful API接口设计，采用标准的HTTP协议，支持JSON格式的数据交换。文档包含对外服务API和Web管理端API两大类接口。

### 1.2 接口分类

- **对外服务API**：面向业务系统和客户端应用，提供用户积分管理、交易处理、规则查询等核心功能
- **Web管理端API**：面向运营管理人员，提供积分规则配置、数据统计分析、系统配置等管理功能

### 1.3 接口列表

#### 1.3.1 对外服务API接口汇总

| 功能模块       | 接口名称 | 请求方式 | 接口路径                                        | 接口描述                           |
|------------| -------- | -------- |---------------------------------------------|--------------------------------|
| **用户积分管理** | 查询用户积分余额 | GET | `/api/v1/point/balance`                     | 查询指定用户的积分余额信息，包括总积分、可用积分、冻结积分等 |
|            | 查询用户积分明细 | GET | `/api/v1/point/record`                      | 分页查询指定用户的积分明细记录，支持多维度筛选        |
| **积分交易处理** | 发放积分 | POST | `/api/v1/point/issue`                       | 向指定用户发放积分，支持批量发放和延迟生效          |
|            | 消费积分 | POST | `/api/v1/point/consume`                     | 消费指定用户的积分，支持预扣和确认扣减两阶段提交       |
|            | 冻结积分 | POST | `/api/v1/point/freeze`                      | 冻结指定用户的积分，用于风控或业务锁定场景          |
|            | 解冻积分 | POST | `/api/v1/point/unfreeze`                    | 解冻指定用户的积分，支持部分解冻和全部解冻          |
|            | 交易撤销 | POST | `/api/v1/point/reverse`                     | 撤销之前的积分交易，对发放和扣减的操作进行回滚冲正      |
|            | 积分事务查询 | GET | `/api/v1/point/transaction`                 | 查询积分事务记录，支持按多种条件筛选和分页查询        |
|            | 积分事务详情查询 | GET | `/api/v1/point/transaction/{transactionId}` | 根据事务ID查询积分事务的详细信息，包含执行日志和规则信息 |
| **积分规则**   | 获取积分规则 | GET | `/api/v1/point/rule`                        | 获取账户的积分规则列表                    |
|            | 计算积分 | POST | `/api/v1/point/calculate`                   | 根据业务场景计算应得积分                   |

#### 1.3.2 Web管理端API接口汇总

| 功能模块 | 接口名称 | 请求方式 | 接口路径 | 接口描述 |
| -------- | -------- | -------- | -------- | -------- |
| **积分规则管理** | 创建积分规则 | POST | `/admin/api/v1/point/rule` | 创建新的积分规则 |
| | 更新积分规则 | PUT | `/admin/api/v1/point/rule/{id}` | 更新现有积分规则 |
| | 删除积分规则 | DELETE | `/admin/api/v1/point/rule/{id}` | 删除积分规则 |
| | 查询积分规则列表 | GET | `/admin/api/v1/point/rule` | 查询积分规则列表 |
| | 测试积分规则 | POST | `/admin/api/v1/point/rule/test` | 测试积分规则的执行效果，验证规则表达式的正确性和计算结果 |
| **积分统计分析** | 积分发放统计 | GET | `/admin/api/v1/point/stat/issue` | 查询积分发放统计数据 |
| | 积分消费统计 | GET | `/admin/api/v1/point/stat/consume` | 查询积分消费统计数据 |
| | 用户积分排行 | GET | `/admin/api/v1/point/stat/ranking` | 查询用户积分排行 |
| **积分账户管理** | 查询积分账户列表 | GET | `/admin/api/v1/point/accounts` | 查询品牌下所有积分账户配置列表 |
| | 查询积分账户详情 | GET | `/admin/api/v1/point/account/{accountCode}` | 查询指定积分账户的详细配置信息 |
| | 创建积分账户 | POST | `/admin/api/v1/point/account` | 创建新的积分账户配置 |
| | 更新积分账户 | PUT | `/admin/api/v1/point/account/{accountCode}` | 更新积分账户的配置信息 |
| | 删除积分账户 | DELETE | `/admin/api/v1/point/account/{accountCode}` | 删除积分账户配置（软删除） |

#### 1.3.3 接口特性说明

**对外服务API特性：**
- 支持高并发访问，提供完善的限流策略
- 所有交易类接口支持幂等性操作
- 提供实时余额查询和明细查询能力
- 支持多种积分操作：发放、消费、冻结、解冻、撤销
- 提供积分规则查询和计算服务

**Web管理端API特性：**
- 提供完整的积分规则CRUD操作
- 支持属性元数据的动态配置和测试
- 提供丰富的统计分析功能
- 支持系统配置的在线管理
- 所有管理接口支持分页查询

**通用特性：**
- 统一的响应格式和错误码体系
- 完善的参数校验和异常处理
- 支持链路追踪和日志记录
- 提供详细的接口文档和示例

### 1.4 技术规范

- **协议**：HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **API版本**：v1
- **认证方式**：Bearer Token / API Key
- **限流策略**：基于用户/IP的令牌桶算法

### 1.4 通用响应格式

所有API接口均采用统一的响应格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| code | Integer | 响应码，0表示成功，非0表示失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| timestamp | Long | 响应时间戳 |
| traceId | String | 链路追踪ID |

### 1.5 分页响应格式

对于分页查询接口，data字段结构如下：

```json
{
  "total": 100,
  "pageNum": 1,
  "pageSize": 10,
  "pages": 10,
  "list": []
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Integer | 总记录数 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页记录数 |
| pages | Integer | 总页数 |
| list | Array | 数据列表 |

## 2. 对外服务API

### 2.1 用户积分管理

#### 2.1.1 查询用户积分余额

**基本信息**
- **接口路径**：`GET /api/v1/point/balance`
- **接口描述**：查询指定用户的积分余额信息，包括总积分、可用积分、冻结积分等
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述             | 示例 |
| ------ | ---- | ---- | ---- |----------------| ---- |
| userId | String | Query | 是 | 用户唯一标识         | "user_123456" |
| accountCode | String | Query | 是 | 账户代码           | "MALL_APP" |
| transactionId | String | Query | 否 | 交易ID，用于关联上下游交易 | "tx_789012" |

**响应参数**

| 参数名 | 类型 | 描述         | 示例 |
| ------ | ---- |------------| ---- |
| userId | String | 用户ID       | "user_123456" |
| accountCode | String | 账户代码       | "MALL_APP" |
| totalPoint | Long | 总积分（历史累计）  | 5000 |
| availablePoint | Long | 可用积分       | 800 |
| frozenPoint | Long | 冻结积分       | 200 |
| expiredPoint | Long | 已过期积分      | 100 |
| expiringSoonPoint | Long | 30天内即将过期积分 | 50 |
| nextExpireDate | String | 下次过期时间     | "2024-12-31 23:59:59" |
| lastUpdateTime | String | 最后更新时间     | "2024-01-15 10:30:00" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "totalPoint": 5000,
    "availablePoint": 800,
    "frozenPoint": 200,
    "expiredPoint": 100,
    "expiringSoonPoint": 50,
    "nextExpireDate": "2024-12-31 23:59:59",
    "lastUpdateTime": "2024-01-15 10:30:00"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述     | 解决方案       |
| ------ |--------|------------|
| 1001 | 参数错误   | 检查必填参数是否完整 |
| 1002 | 用户不存在  | 确认用户ID是否正确 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |

#### 2.1.2 查询用户积分明细

**基本信息**
- **接口路径**：`GET /api/v1/point/record`
- **接口描述**：分页查询指定用户的积分明细记录，支持多维度筛选
- **访问权限**：需要API认证
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Query | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| pageNum | Integer | Query | 否 | 页码     | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数  | 20 | 默认20，范围1-100 |
| startTime | String | Query | 否 | 开始时间   | "2024-01-01 00:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | Query | 否 | 结束时间   | "2024-01-31 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| type | String | Query | 否 | 积分类型   | "INCOME" | INCOME/EXPENSE/ALL |
| source | String | Query | 否 | 积分来源   | "SIGN_IN" | 长度1-32 |
| minPoint | Long | Query | 否 | 最小积分数  | 10 | 大于0 |
| maxPoint | Long | Query | 否 | 最大积分数  | 1000 | 大于minPoint |
| transactionId | String | Query | 否 | 交易ID   | "tx_789012" | 长度1-64 |

**响应参数**

| 参数名 | 类型 | 描述                | 示例 |
| ------ | ---- |-------------------| ---- |
| total | Integer | 总记录数              | 150 |
| pageNum | Integer | 当前页码              | 1 |
| pageSize | Integer | 每页记录数             | 20 |
| pages | Integer | 总页数               | 8 |
| list | Array | 积分明细列表            | - |
| list[].id | String | 记录唯一标识            | "detail_001" |
| list[].userId | String | 用户ID              | "user_123456" |
| list[].accountCode | String | 账户代码              | "MALL_APP" |
| list[].point | Long | 积分数量（正数为收入，负数为支出） | 100 |
| list[].type | String | 类型                | "INCOME" |
| list[].source | String | 积分来源/用途           | "SIGN_IN" |
| list[].sourceName | String | 来源名称              | "每日签到" |
| list[].description | String | 详细描述              | "连续签到第7天奖励" |
| list[].businessId | String | 业务单号              | "biz_20240115001" |
| list[].transactionId | String | 交易ID              | "tx_789012" |
| list[].status | String | 状态                | "SUCCESS" |
| list[].createTime | String | 创建时间              | "2024-01-15 08:00:00" |
| list[].expireTime | String | 过期时间              | "2024-12-31 23:59:59" |
| list[].remark | String | 备注信息              | "系统自动发放" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 150,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 8,
    "list": [
      {
        "id": "detail_001",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "point": 100,
        "type": "INCOME",
        "source": "SIGN_IN",
        "sourceName": "每日签到",
        "description": "连续签到第7天奖励",
        "businessId": "biz_20240115001",
        "transactionId": "tx_789012",
        "status": "SUCCESS",
        "createTime": "2024-01-15 08:00:00",
        "expireTime": "2024-12-31 23:59:59",
        "remark": "系统自动发放"
      },
      {
        "id": "detail_002",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "point": -50,
        "type": "EXPENSE",
        "source": "EXCHANGE",
        "sourceName": "商品兑换",
        "description": "兑换iPhone手机壳",
        "businessId": "biz_20240115002",
        "transactionId": "tx_789013",
        "status": "SUCCESS",
        "createTime": "2024-01-15 10:30:00",
        "expireTime": null,
        "remark": "用户主动兑换"
      }
    ]
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述     | 解决方案         |
| ------ |--------|--------------|
| 1001 | 参数错误   | 检查参数格式和取值范围  |
| 1002 | 用户不存在  | 确认用户ID是否正确   |
| 1003 | 账户代码无效 | 确认账户代码是否正确   |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数       |

### 2.2 积分交易处理

#### 2.2.1 发放积分

**基本信息**
- **接口路径**：`POST /api/v1/point/issue`
- **接口描述**：向指定用户发放积分，支持批量发放和延迟生效
- **访问权限**：需要API认证
- **限流规则**：200次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| point | Long | Body | 是 | 积分数量   | 100 | 正整数，范围1-100000 |
| source | String | Body | 是 | 积分来源代码 | "SIGN_IN" | 长度1-32 |
| sourceName | String | Body | 否 | 来源名称   | "每日签到" | 长度1-64 |
| description | String | Body | 否 | 详细描述   | "连续签到第7天奖励" | 长度1-200 |
| expireDays | Integer | Body | 否 | 有效期天数  | 365 | 默认365，范围1-3650 |
| effectiveTime | String | Body | 否 | 生效时间   | "2024-01-15 10:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| businessId | String | Body | 是 | 业务单号   | "biz_20240115001" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789012" | 长度1-64 |
| remark | String | Body | 否 | 备注信息   | "系统自动发放" | 长度1-100 |
| notifyUrl | String | Body | 否 | 回调通知地址 | "https://api.example.com/notify" | 有效URL |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "point": 100,
  "source": "SIGN_IN",
  "sourceName": "每日签到",
  "description": "连续签到第7天奖励",
  "expireDays": 365,
  "effectiveTime": "2024-01-15 10:00:00",
  "businessId": "biz_20240115001",
  "transactionId": "tx_789012",
  "remark": "系统自动发放",
  "notifyUrl": "https://api.example.com/notify"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| transactionId | String | 系统生成的交易ID | "sys_tx_001" |
| detailId | String | 积分明细记录ID | "detail_001" |
| point | Long | 实际发放的积分数量 | 100 |
| beforeBalance | Long | 发放前余额 | 1000 |
| afterBalance | Long | 发放后余额 | 1100 |
| expireTime | String | 积分过期时间 | "2025-01-15 23:59:59" |
| effectiveTime | String | 积分生效时间 | "2024-01-15 10:00:00" |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "积分发放成功",
  "data": {
    "success": true,
    "transactionId": "sys_tx_001",
    "detailId": "detail_001",
    "point": 100,
    "beforeBalance": 1000,
    "afterBalance": 1100,
    "expireTime": "2025-01-15 23:59:59",
    "effectiveTime": "2024-01-15 10:00:00",
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2001 | 积分数量超出限制 | 调整积分数量在允许范围内 |
| 2002 | 用户积分账户异常 | 检查用户账户状态 |
| 2003 | 业务单号重复 | 使用新的业务单号 |
| 2004 | 积分来源无效 | 确认积分来源代码是否正确 |
| 2005 | 生效时间无效 | 确认生效时间格式和范围 |

#### 2.2.2 消费积分

**基本信息**
- **接口路径**：`POST /api/v1/point/consume`
- **接口描述**：消费指定用户的积分，支持预扣和确认扣减两阶段提交
- **访问权限**：需要API认证
- **限流规则**：300次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述       | 示例 | 约束 |
| ------ | ---- | ---- | ---- |----------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识   | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码     | "MALL_APP" | 长度1-32 |
| point | Long | Body | 是 | 消费积分数量   | 50 | 正整数，范围1-100000 |
| source | String | Body | 是 | 消费来源代码   | "EXCHANGE" | 长度1-32 |
| sourceName | String | Body | 否 | 来源名称     | "商品兑换" | 长度1-64 |
| description | String | Body | 否 | 详细描述     | "兑换iPhone手机壳" | 长度1-200 |
| consumeMode | String | Body | 否 | 消费模式     | "IMMEDIATE" | IMMEDIATE/PRE_CONSUME |
| businessId | String | Body | 是 | 业务单号     | "biz_20240115002" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID     | "tx_789013" | 长度1-64 |
| remark | String | Body | 否 | 备注信息     | "用户主动兑换" | 长度1-100 |
| notifyUrl | String | Body | 否 | 回调通知地址   | "https://api.example.com/notify" | 有效URL |
| checkBalance | Boolean | Body | 否 | 是否检查余额充足 | true | 默认true |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "point": 50,
  "source": "EXCHANGE",
  "sourceName": "商品兑换",
  "description": "兑换iPhone手机壳",
  "consumeMode": "IMMEDIATE",
  "businessId": "biz_20240115002",
  "transactionId": "tx_789013",
  "remark": "用户主动兑换",
  "notifyUrl": "https://api.example.com/notify",
  "checkBalance": true
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| transactionId | String | 系统生成的交易ID | "sys_tx_002" |
| detailId | String | 积分明细记录ID | "detail_002" |
| point | Long | 实际消费的积分数量 | 50 |
| beforeBalance | Long | 消费前余额 | 1100 |
| afterBalance | Long | 消费后余额 | 1050 |
| consumedDetail | Array | 消费明细（按过期时间优先扣减） | - |
| consumedDetail[].point | Long | 扣减积分数 | 30 |
| consumedDetail[].expireTime | String | 原过期时间 | "2024-06-30 23:59:59" |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "积分消费成功",
  "data": {
    "success": true,
    "transactionId": "sys_tx_002",
    "detailId": "detail_002",
    "point": 50,
    "beforeBalance": 1100,
    "afterBalance": 1050,
    "consumedDetail": [
      {
        "point": 30,
        "expireTime": "2024-06-30 23:59:59"
      },
      {
        "point": 20,
        "expireTime": "2024-12-31 23:59:59"
      }
    ],
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2101 | 积分余额不足 | 检查用户可用积分余额 |
| 2102 | 消费数量超出限制 | 调整消费数量在允许范围内 |
| 2103 | 用户积分账户被冻结 | 联系客服解冻账户 |
| 2104 | 业务单号重复 | 使用新的业务单号 |
| 2105 | 消费来源无效 | 确认消费来源代码是否正确 |

#### 2.2.3 冻结积分

**基本信息**
- **接口路径**：`POST /api/v1/point/freeze`
- **接口描述**：冻结指定用户的积分，用于风控或业务锁定场景
- **访问权限**：需要API认证
- **限流规则**：100次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| point | Long | Body | 是 | 冻结积分数量 | 200 | 正整数，范围1-100000 |
| freezeType | String | Body | 否 | 冻结类型   | "RISK_CONTROL" | RISK_CONTROL/BUSINESS_LOCK |
| reason | String | Body | 是 | 冻结原因   | "风险控制" | 长度1-100 |
| expireTime | String | Body | 否 | 冻结过期时间 | "2024-02-15 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| autoUnfreeze | Boolean | Body | 否 | 是否自动解冻 | true | 默认false |
| businessId | String | Body | 是 | 业务单号   | "freeze_20240115001" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789014" | 长度1-64 |
| remark | String | Body | 否 | 备注信息   | "系统风控冻结" | 长度1-100 |
| notifyUrl | String | Body | 否 | 回调通知地址 | "https://api.example.com/notify" | 有效URL |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "point": 200,
  "freezeType": "RISK_CONTROL",
  "reason": "风险控制",
  "expireTime": "2024-02-15 23:59:59",
  "autoUnfreeze": true,
  "businessId": "freeze_20240115001",
  "transactionId": "tx_789014",
  "remark": "系统风控冻结",
  "notifyUrl": "https://api.example.com/notify"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| freezeId | String | 冻结记录ID | "freeze_001" |
| transactionId | String | 系统生成的交易ID | "sys_tx_003" |
| point | Long | 实际冻结的积分数量 | 200 |
| beforeAvailable | Long | 冻结前可用余额 | 1050 |
| afterAvailable | Long | 冻结后可用余额 | 850 |
| beforeFrozen | Long | 冻结前冻结余额 | 0 |
| afterFrozen | Long | 冻结后冻结余额 | 200 |
| expireTime | String | 冻结过期时间 | "2024-02-15 23:59:59" |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "积分冻结成功",
  "data": {
    "success": true,
    "freezeId": "freeze_001",
    "transactionId": "sys_tx_003",
    "point": 200,
    "beforeAvailable": 1050,
    "afterAvailable": 850,
    "beforeFrozen": 0,
    "afterFrozen": 200,
    "expireTime": "2024-02-15 23:59:59",
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2201 | 可用积分不足 | 检查用户可用积分余额 |
| 2202 | 冻结数量超出限制 | 调整冻结数量在允许范围内 |
| 2203 | 用户积分账户异常 | 检查用户账户状态 |
| 2204 | 业务单号重复 | 使用新的业务单号 |
| 2205 | 冻结过期时间无效 | 确认过期时间格式和范围 |

#### 2.2.4 解冻积分

**基本信息**
- **接口路径**：`POST /api/v1/point/unfreeze`
- **接口描述**：解冻指定用户的积分，支持部分解冻和全部解冻
- **访问权限**：需要API认证
- **限流规则**：100次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| freezeId | String | Body | 是 | 冻结记录ID | "freeze_001" | 长度1-64 |
| point | Long | Body | 否 | 解冻积分数量 | 100 | 正整数，不传则解冻全部 |
| reason | String | Body | 是 | 解冻原因   | "风险解除" | 长度1-100 |
| businessId | String | Body | 是 | 业务单号   | "unfreeze_20240115001" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789015" | 长度1-64 |
| remark | String | Body | 否 | 备注信息   | "系统自动解冻" | 长度1-100 |
| notifyUrl | String | Body | 否 | 回调通知地址 | "https://api.example.com/notify" | 有效URL |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "freezeId": "freeze_001",
  "point": 100,
  "reason": "风险解除",
  "businessId": "unfreeze_20240115001",
  "transactionId": "tx_789015",
  "remark": "系统自动解冻",
  "notifyUrl": "https://api.example.com/notify"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| transactionId | String | 系统生成的交易ID | "sys_tx_004" |
| point | Long | 实际解冻的积分数量 | 100 |
| beforeAvailable | Long | 解冻前可用余额 | 850 |
| afterAvailable | Long | 解冻后可用余额 | 950 |
| beforeFrozen | Long | 解冻前冻结余额 | 200 |
| afterFrozen | Long | 解冻后冻结余额 | 100 |
| remainingFrozen | Long | 该冻结记录剩余冻结积分 | 100 |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "积分解冻成功",
  "data": {
    "success": true,
    "transactionId": "sys_tx_004",
    "point": 100,
    "beforeAvailable": 850,
    "afterAvailable": 950,
    "beforeFrozen": 200,
    "afterFrozen": 100,
    "remainingFrozen": 100,
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2301 | 冻结记录不存在 | 确认冻结记录ID是否正确 |
| 2302 | 冻结记录已过期 | 冻结记录已自动解冻 |
| 2303 | 解冻数量超出冻结数量 | 调整解冻数量 |
| 2304 | 业务单号重复 | 使用新的业务单号 |
| 2305 | 冻结记录状态异常 | 检查冻结记录状态 |

#### 2.2.5 交易撤销

**基本信息**
- **接口路径**：`POST /api/v1/point/reverse`
- **接口描述**：撤销之前的积分交易，对发放和扣减的操作进行回滚冲正
- **访问权限**：需要API认证
- **限流规则**：50次/分钟
- **幂等性**：支持，基于businessId参数
- **撤销时限**：交易完成后24小时内可撤销

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码   | "MALL_APP" | 长度1-32 |
| originalTransactionId | String | Body | 是 | 原交易ID  | "sys_tx_001" | 长度1-64 |
| originalBizNo | String | Body | 否 | 原业务单号  | "biz_20240115001" | 长度1-64 |
| reverseType | String | Body | 否 | 撤销类型   | "FULL" | FULL/PARTIAL |
| reversePoint | Long | Body | 否 | 撤销积分数量 | 50 | 部分撤销时必填 |
| reason | String | Body | 是 | 撤销原因   | "业务回滚" | 长度1-100 |
| businessId | String | Body | 是 | 业务单号   | "reverse_20240115001" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789016" | 长度1-64 |
| remark | String | Body | 否 | 备注信息   | "系统自动撤销" | 长度1-100 |
| notifyUrl | String | Body | 否 | 回调通知地址 | "https://api.example.com/notify" | 有效URL |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "MALL_APP",
  "originalTransactionId": "sys_tx_001",
  "originalBizNo": "biz_20240115001",
  "reverseType": "FULL",
  "reason": "业务回滚",
  "businessId": "reverse_20240115001",
  "transactionId": "tx_789016",
  "remark": "系统自动撤销",
  "notifyUrl": "https://api.example.com/notify"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| reverseTransactionId | String | 撤销交易ID | "sys_tx_005" |
| originalTransaction | Object | 原交易信息 | - |
| originalTransaction.transactionId | String | 原交易ID | "sys_tx_001" |
| originalTransaction.point | Long | 原交易积分数量 | 100 |
| originalTransaction.type | String | 原交易类型 | "INCOME" |
| originalTransaction.createTime | String | 原交易时间 | "2024-01-15 10:00:00" |
| reversePoint | Long | 实际撤销的积分数量 | 100 |
| beforeBalance | Long | 撤销前余额 | 1100 |
| afterBalance | Long | 撤销后余额 | 1000 |
| status | String | 处理状态 | "SUCCESS" |

**响应示例**

```json
{
  "code": 0,
  "message": "交易撤销成功",
  "data": {
    "success": true,
    "reverseTransactionId": "sys_tx_005",
    "originalTransaction": {
      "transactionId": "sys_tx_001",
      "point": 100,
      "type": "INCOME",
      "createTime": "2024-01-15 10:00:00"
    },
    "reversePoint": 100,
    "beforeBalance": 1100,
    "afterBalance": 1000,
    "status": "SUCCESS"
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 2401 | 原交易不存在 | 确认原交易ID是否正确 |
| 2402 | 原交易已被撤销 | 不能重复撤销同一笔交易 |
| 2403 | 超出撤销时限 | 交易完成后24小时内可撤销 |
| 2404 | 撤销积分数量无效 | 部分撤销时检查积分数量 |
| 2405 | 用户积分余额不足 | 撤销消费交易时余额不足 |
| 2406 | 业务单号重复 | 使用新的业务单号 |

#### 2.2.6 积分事务查询

**基本信息**
- **接口路径**：`GET /api/v1/point/transaction`
- **接口描述**：查询积分事务记录，支持按多种条件筛选和分页查询
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Query | 否 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码 | "MALL_APP" | 长度1-32 |
| transactionId | String | Query | 否 | 事务ID | "PT20240115001" | 长度1-64 |
| businessId | String | Query | 否 | 业务单号 | "biz_20240115001" | 长度1-64 |
| transactionType | String | Query | 否 | 事务类型 | "POINT_ISSUE" | 见事务类型枚举 |
| status | String | Query | 否 | 事务状态 | "SUCCESS" | PENDING/PROCESSING/SUCCESS/FAILED/CANCELLED |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01 00:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| total | Integer | 总记录数 | 150 |
| pageNum | Integer | 当前页码 | 1 |
| pageSize | Integer | 每页记录数 | 20 |
| pages | Integer | 总页数 | 8 |
| list | Array | 事务记录列表 | - |
| list[].transactionId | String | 事务ID | "PT20240115001" |
| list[].userId | String | 用户ID | "user_123456" |
| list[].accountCode | String | 账户代码 | "MALL_APP" |
| list[].businessId | String | 业务单号 | "biz_20240115001" |
| list[].businessType | String | 业务类型 | "SIGN_IN" |
| list[].transactionType | String | 事务类型 | "POINT_ISSUE" |
| list[].pointChange | Long | 积分变化量 | 100 |
| list[].beforeBalance | Long | 变化前余额 | 1000 |
| list[].afterBalance | Long | 变化后余额 | 1100 |
| list[].status | String | 事务状态 | "SUCCESS" |
| list[].description | String | 事务描述 | "每日签到奖励" |
| list[].source | String | 积分来源 | "SIGN_IN" |
| list[].effectiveTime | String | 生效时间 | "2024-01-15 10:00:00" |
| list[].expireTime | String | 过期时间 | "2025-01-15 23:59:59" |
| list[].processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| list[].errorMessage | String | 错误信息 | null |
| list[].retryCount | Integer | 重试次数 | 0 |
| list[].createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| list[].updateTime | String | 更新时间 | "2024-01-15 10:00:01" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 150,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 8,
    "list": [
      {
        "transactionId": "PT20240115001",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "businessId": "biz_20240115001",
        "businessType": "SIGN_IN",
        "transactionType": "POINT_ISSUE",
        "pointChange": 100,
        "beforeBalance": 1000,
        "afterBalance": 1100,
        "status": "SUCCESS",
        "description": "每日签到奖励",
        "source": "SIGN_IN",
        "effectiveTime": "2024-01-15 10:00:00",
        "expireTime": "2025-01-15 23:59:59",
        "processedTime": "2024-01-15 10:00:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 10:00:00",
        "updateTime": "2024-01-15 10:00:01"
      },
      {
        "transactionId": "PT20240115002",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "businessId": "biz_20240115002",
        "businessType": "EXCHANGE",
        "transactionType": "POINT_CONSUME",
        "pointChange": -50,
        "beforeBalance": 1100,
        "afterBalance": 1050,
        "status": "SUCCESS",
        "description": "商品兑换",
        "source": "EXCHANGE",
        "effectiveTime": "2024-01-15 14:30:00",
        "expireTime": null,
        "processedTime": "2024-01-15 14:30:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 14:30:00",
        "updateTime": "2024-01-15 14:30:01"
      }
    ]
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**事务类型枚举**

| 类型 | 描述 |
| ---- | ---- |
| POINT_ISSUE | 积分发放 |
| POINT_CONSUME | 积分消费 |
| POINT_FREEZE | 积分冻结 |
| POINT_UNFREEZE | 积分解冻 |
| POINT_REVERSE | 积分撤销 |
| POINT_EXPIRE | 积分过期 |
| POINT_TRANSFER | 积分转移 |
| POINT_ADJUST | 积分调整 |

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查参数格式和取值范围 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数 |

#### 2.2.7 积分事务详情查询

**基本信息**
- **接口路径**：`GET /api/v1/point/transaction/{transactionId}`
- **接口描述**：根据事务ID查询积分事务的详细信息
- **访问权限**：需要API认证
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| transactionId | String | Path | 是 | 事务ID | "PT20240115001" | 长度1-64 |
| includeDetail | Boolean | Query | 否 | 是否包含详细信息 | true | 默认false |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| transactionId | String | 事务ID | "PT20240115001" |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "MALL_APP" |
| businessId | String | 业务单号 | "biz_20240115001" |
| businessType | String | 业务类型 | "SIGN_IN" |
| transactionType | String | 事务类型 | "POINT_ISSUE" |
| pointChange | Long | 积分变化量 | 100 |
| beforeBalance | Long | 变化前余额 | 1000 |
| afterBalance | Long | 变化后余额 | 1100 |
| status | String | 事务状态 | "SUCCESS" |
| description | String | 事务描述 | "每日签到奖励" |
| source | String | 积分来源 | "SIGN_IN" |
| effectiveTime | String | 生效时间 | "2024-01-15 10:00:00" |
| expireTime | String | 过期时间 | "2025-01-15 23:59:59" |
| processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| errorMessage | String | 错误信息 | null |
| retryCount | Integer | 重试次数 | 0 |
| maxRetryCount | Integer | 最大重试次数 | 3 |
| operatorId | String | 操作人ID | "system" |
| extraData | String | 扩展数据 | "{\"remark\":\"系统自动发放\"}" |
| createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| updateTime | String | 更新时间 | "2024-01-15 10:00:01" |
| version | Integer | 版本号 | 1 |
| detail | Object | 详细信息（当includeDetail=true时返回） | - |
| detail.ruleInfo | Object | 规则信息 | - |
| detail.ruleInfo.ruleId | Long | 规则ID | 1001 |
| detail.ruleInfo.ruleName | String | 规则名称 | "每日签到规则" |
| detail.ruleInfo.ruleExpression | String | 规则表达式 | "user.signInDays >= 1 ? 100 : 0" |
| detail.executionLog | Array | 执行日志 | - |
| detail.executionLog[].step | String | 执行步骤 | "RULE_EVALUATION" |
| detail.executionLog[].timestamp | String | 时间戳 | "2024-01-15 10:00:00.123" |
| detail.executionLog[].message | String | 日志信息 | "规则评估完成，计算积分：100" |
| detail.executionLog[].data | Object | 相关数据 | {} |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "transactionId": "PT20240115001",
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "businessId": "biz_20240115001",
    "businessType": "SIGN_IN",
    "transactionType": "POINT_ISSUE",
    "pointChange": 100,
    "beforeBalance": 1000,
    "afterBalance": 1100,
    "status": "SUCCESS",
    "description": "每日签到奖励",
    "source": "SIGN_IN",
    "effectiveTime": "2024-01-15 10:00:00",
    "expireTime": "2025-01-15 23:59:59",
    "processedTime": "2024-01-15 10:00:01",
    "errorMessage": null,
    "retryCount": 0,
    "maxRetryCount": 3,
    "operatorId": "system",
    "extraData": "{\"remark\":\"系统自动发放\"}",
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:01",
    "version": 1,
    "detail": {
      "ruleInfo": {
        "ruleId": 1001,
        "ruleName": "每日签到规则",
        "ruleExpression": "user.signInDays >= 1 ? 100 : 0"
      },
      "executionLog": [
        {
          "step": "TRANSACTION_CREATE",
          "timestamp": "2024-01-15 10:00:00.001",
          "message": "创建积分事务",
          "data": {}
        },
        {
          "step": "RULE_EVALUATION",
          "timestamp": "2024-01-15 10:00:00.123",
          "message": "规则评估完成，计算积分：100",
          "data": {
            "ruleId": 1001,
            "calculatedPoint": 100
          }
        },
        {
          "step": "BALANCE_UPDATE",
          "timestamp": "2024-01-15 10:00:00.456",
          "message": "更新用户积分余额",
          "data": {
            "beforeBalance": 1000,
            "afterBalance": 1100
          }
        },
        {
          "step": "TRANSACTION_COMPLETE",
          "timestamp": "2024-01-15 10:00:01.000",
          "message": "事务处理完成",
          "data": {}
        }
      ]
    }
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查事务ID格式 |
| 2501 | 事务不存在 | 确认事务ID是否正确 |
| 2502 | 事务已过期 | 事务记录已超过保存期限 |



### 2.3 积分规则

#### 2.3.1 获取积分规则

- **接口路径**：`/api/v1/point/rule`
- **请求方式**：GET
- **接口描述**：获取账户的积分规则列表
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                          |
| ------ | ---- | -------- |-----------------------------|
| accountCode | String | 是 | 账户代码                        |
| ruleType | String | 否 | 规则类型                        |
| status | String | 否 | 规则状态：ACTIVE-生效，INACTIVE-未生效 |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| list | Array | 规则列表 |
| list[].ruleCode | String | 规则代码 |
| list[].ruleName | String | 规则名称 |
| list[].ruleType | String | 规则类型 |
| list[].description | String | 规则描述 |
| list[].point | Integer | 积分值 |
| list[].status | String | 状态 |
| list[].effectiveTime | String | 生效时间 |
| list[].expireTime | String | 过期时间 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "ruleCode": "SIGN_IN",
        "ruleName": "每日签到",
        "ruleType": "DAILY",
        "description": "用户每日签到获得积分",
        "point": 10,
        "status": "ACTIVE",
        "effectiveTime": "2023-01-01 00:00:00",
        "expireTime": "2023-12-31 23:59:59"
      },
      {
        "ruleCode": "PURCHASE",
        "ruleName": "购物积分",
        "ruleType": "TRANSACTION",
        "description": "用户购物获得积分，每消费1元获得1积分",
        "point": 1,
        "status": "ACTIVE",
        "effectiveTime": "2023-01-01 00:00:00",
        "expireTime": null
      }
    ]
  }
}
```

#### 2.3.2 计算积分

- **接口路径**：`/api/v1/point/calculate`
- **请求方式**：POST
- **接口描述**：根据业务场景计算应得积分
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述               |
| ------ | ---- | -------- |------------------|
| userId | String | 是 | 用户ID             |
| accountCode | String | 是 | 账户代码             |
| scene | String | 是 | 业务场景代码           |
| bizData | Object | 是 | 业务数据，根据不同场景有不同结构 |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| point | Integer | 计算得出的积分值 |
| rule | Array | 适用的规则列表 |
| rule[].ruleCode | String | 规则代码 |
| rule[].ruleName | String | 规则名称 |
| rule[].point | Integer | 该规则贡献的积分值 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "point": 150,
    "rule": [
      {
        "ruleCode": "PURCHASE_BASE",
        "ruleName": "购物基础积分",
        "point": 100
      },
      {
        "ruleCode": "MEMBER_BONUS",
        "ruleName": "会员额外积分",
        "point": 50
      }
    ]
  }
}
```

## 3. Web管理端API

### 3.1 积分规则管理

#### 3.1.1 创建积分规则

- **接口路径**：`/admin/api/v1/point/rule`
- **请求方式**：POST
- **接口描述**：创建新的积分规则
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                                 |
| ------ | ---- | -------- |------------------------------------|
| accountCode | String | 是 | 账户代码                               |
| ruleCode | String | 是 | 规则代码，唯一标识                          |
| ruleName | String | 是 | 规则名称                               |
| ruleType | String | 是 | 规则类型                               |
| description | String | 否 | 规则描述                               |
| ruleExpression | String | 是 | 规则表达式                              |
| priority | Integer | 否 | 优先级，数字越大优先级越高                      |
| status | String | 否 | 状态：ACTIVE-生效，INACTIVE-未生效，默认ACTIVE |
| effectiveTime | String | 否 | 生效时间，格式：yyyy-MM-dd HH:mm:ss        |
| expireTime | String | 否 | 过期时间，格式：yyyy-MM-dd HH:mm:ss        |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | Long | 规则ID |
| ruleCode | String | 规则代码 |
| ruleName | String | 规则名称 |
| createdTime | String | 创建时间 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1001,
    "ruleCode": "NEW_USER_BONUS",
    "ruleName": "新用户奖励",
    "createdTime": "2023-01-01 10:00:00"
  }
}
```

#### 3.1.2 更新积分规则

- **接口路径**：`/admin/api/v1/point/rule/{id}`
- **请求方式**：PUT
- **接口描述**：更新现有积分规则
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| id | Long | 是 | 规则ID |
| ruleName | String | 否 | 规则名称 |
| description | String | 否 | 规则描述 |
| ruleExpression | String | 否 | 规则表达式 |
| priority | Integer | 否 | 优先级 |
| status | String | 否 | 状态：ACTIVE-生效，INACTIVE-未生效 |
| effectiveTime | String | 否 | 生效时间，格式：yyyy-MM-dd HH:mm:ss |
| expireTime | String | 否 | 过期时间，格式：yyyy-MM-dd HH:mm:ss |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |
| updatedTime | String | 更新时间 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "updatedTime": "2023-01-02 15:30:00"
  }
}
```

#### 3.1.3 删除积分规则

- **接口路径**：`/admin/api/v1/point/rule/{id}`
- **请求方式**：DELETE
- **接口描述**：删除积分规则
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述 |
| ------ | ---- | -------- | ---- |
| id | Long | 是 | 规则ID |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true
  }
}
```

#### 3.1.4 查询积分规则列表

- **接口路径**：`/admin/api/v1/point/rule`
- **请求方式**：GET
- **接口描述**：查询积分规则列表
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                        |
| ------ | ---- | -------- |---------------------------|
| accountCode | String | 是 | 账户代码                      |
| ruleCode | String | 否 | 规则代码                      |
| ruleName | String | 否 | 规则名称，支持模糊查询               |
| ruleType | String | 否 | 规则类型                      |
| status | String | 否 | 状态：ACTIVE-生效，INACTIVE-未生效 |
| pageNum | Integer | 否 | 页码，默认1                    |
| pageSize | Integer | 否 | 每页记录数，默认10                |

- **响应参数**：

| 参数名 | 类型 | 描述    |
| ------ | ---- |-------|
| total | Integer | 总记录数  |
| list | Array | 规则列表  |
| list[].id | Long | 规则ID  |
| list[].accountCode | String | 账户代码  |
| list[].ruleCode | String | 规则代码  |
| list[].ruleName | String | 规则名称  |
| list[].ruleType | String | 规则类型  |
| list[].description | String | 规则描述  |
| list[].ruleExpression | String | 规则表达式 |
| list[].priority | Integer | 优先级   |
| list[].status | String | 状态    |
| list[].effectiveTime | String | 生效时间  |
| list[].expireTime | String | 过期时间  |
| list[].createdBy | String | 创建人   |
| list[].createdTime | String | 创建时间  |
| list[].updatedTime | String | 更新时间  |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1001,
        "accountCode": "MALL",
        "ruleCode": "NEW_USER_BONUS",
        "ruleName": "新用户奖励",
        "ruleType": "USER",
        "description": "新用户注册奖励积分",
        "ruleExpression": "user.isNewUser == true ? 100 : 0",
        "priority": 10,
        "status": "ACTIVE",
        "effectiveTime": "2023-01-01 00:00:00",
        "expireTime": null,
        "createdBy": "admin",
        "createdTime": "2023-01-01 10:00:00",
        "updatedTime": "2023-01-01 10:00:00"
      }
    ]
  }
}
```

#### 3.1.5 测试积分规则

- **接口路径**：`/admin/api/v1/point/rule/test`
- **请求方式**：POST
- **接口描述**：测试积分规则的执行效果，验证规则表达式的正确性和计算结果
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                |
| ------ | ---- | -------- |-------------------|
| ruleId | Long | 否 | 规则ID，测试已存在规则时使用   |
| ruleExpression | String | 否 | 规则表达式，测试新规则时使用    |
| testParams | Object | 是 | 测试参数，模拟业务场景数据     |
| testParams.userId | String | 否 | 用户ID              | 
| testParams.accountCode | String | 是 | 账户代码              |
| testParams.bizData | Object | 否 | 业务数据，根据规则类型提供不同数据 |

- **请求示例**：

```json
{
  "ruleId": 1001,
  "ruleExpression": "user.orderCount >= 5 && order.amount > 100 ? order.amount * 0.1 : 0",
  "testParams": {
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "bizData": {
      "order": {
        "amount": 500,
        "productCount": 3
      },
      "user": {
        "orderCount": 8,
        "memberLevel": "GOLD",
        "isNewUser": false
      }
    }
  }
}
```

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| success | Boolean | 测试是否成功 |
| result | Object | 测试结果 |
| result.point | Number | 计算得出的积分值 |
| result.executionTime | Long | 执行时间（毫秒） |
| result.expressionResult | String | 表达式执行结果详情 |
| result.variables | Object | 变量解析结果 |
| result.errorMessage | String | 错误信息（如果有） |
| ruleInfo | Object | 规则信息 |
| ruleInfo.ruleCode | String | 规则代码 |
| ruleInfo.ruleName | String | 规则名称 |
| ruleInfo.ruleExpression | String | 规则表达式 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "测试成功",
  "data": {
    "success": true,
    "result": {
      "point": 50,
      "executionTime": 12,
      "expressionResult": "user.orderCount(8) >= 5 && order.amount(500) > 100 ? order.amount(500) * 0.1 : 0 = 50",
      "variables": {
        "user.orderCount": 8,
        "user.memberLevel": "GOLD",
        "user.isNewUser": false,
        "order.amount": 500,
        "order.productCount": 3
      },
      "errorMessage": null
    },
    "ruleInfo": {
      "ruleCode": "ORDER_REWARD",
      "ruleName": "订单奖励积分",
      "ruleExpression": "user.orderCount >= 5 && order.amount > 100 ? order.amount * 0.1 : 0"
    }
  }
}
```

- **错误码**：

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 3101 | 规则不存在 | 确认规则ID是否正确 |
| 3102 | 规则表达式语法错误 | 检查表达式语法 |
| 3103 | 测试参数不完整 | 补充必要的测试参数 |
| 3104 | 变量解析失败 | 检查变量名称和数据结构 |
| 3105 | 表达式执行异常 | 检查表达式逻辑和数据类型 |

### 3.2 积分统计分析

#### 3.2.1 积分发放统计

- **接口路径**：`/admin/api/v1/point/stat/issue`
- **请求方式**：GET
- **接口描述**：查询积分发放统计数据
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                          |
| ------ | ---- | -------- |-----------------------------|
| accountCode | String | 是 | 账户代码                        |
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd          |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd          |
| groupBy | String | 否 | 分组方式：DAY, WEEK, MONTH，默认DAY |
| source | String | 否 | 积分来源                        |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Integer | 总积分发放量 |
| userCount | Integer | 涉及用户数 |
| detail | Array | 统计明细 |
| detail[].date | String | 日期 |
| detail[].point | Integer | 积分数量 |
| detail[].userCount | Integer | 用户数量 |
| sourceDistribution | Array | 来源分布 |
| sourceDistribution[].source | String | 来源 |
| sourceDistribution[].point | Integer | 积分数量 |
| sourceDistribution[].percentage | Number | 百分比 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100000,
    "userCount": 5000,
    "detail": [
      {
        "date": "2023-01-01",
        "point": 10000,
        "userCount": 1000
      },
      {
        "date": "2023-01-02",
        "point": 12000,
        "userCount": 1200
      }
    ],
    "sourceDistribution": [
      {
        "source": "SIGN_IN",
        "point": 30000,
        "percentage": 30.0
      },
      {
        "source": "PURCHASE",
        "point": 50000,
        "percentage": 50.0
      },
      {
        "source": "ACTIVITY",
        "point": 20000,
        "percentage": 20.0
      }
    ]
  }
}
```

#### 3.2.2 积分消费统计

- **接口路径**：`/admin/api/v1/point/stat/consume`
- **请求方式**：GET
- **接口描述**：查询积分消费统计数据
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                          |
| ------ | ---- | -------- |-----------------------------|
| accountCode | String | 是 | 账户代码                        |
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd          |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd          |
| groupBy | String | 否 | 分组方式：DAY, WEEK, MONTH，默认DAY |
| source | String | 否 | 消费来源                        |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| total | Integer | 总积分消费量 |
| userCount | Integer | 涉及用户数 |
| detail | Array | 统计明细 |
| detail[].date | String | 日期 |
| detail[].point | Integer | 积分数量 |
| detail[].userCount | Integer | 用户数量 |
| sourceDistribution | Array | 来源分布 |
| sourceDistribution[].source | String | 来源 |
| sourceDistribution[].point | Integer | 积分数量 |
| sourceDistribution[].percentage | Number | 百分比 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 80000,
    "userCount": 4000,
    "detail": [
      {
        "date": "2023-01-01",
        "point": 8000,
        "userCount": 800
      },
      {
        "date": "2023-01-02",
        "point": 9000,
        "userCount": 900
      }
    ],
    "sourceDistribution": [
      {
        "source": "EXCHANGE",
        "point": 60000,
        "percentage": 75.0
      },
      {
        "source": "REDEEM",
        "point": 20000,
        "percentage": 25.0
      }
    ]
  }
}
```

#### 3.2.3 用户积分排行

- **接口路径**：`/admin/api/v1/point/stat/ranking`
- **请求方式**：GET
- **接口描述**：查询用户积分排行
- **请求参数**：

| 参数名 | 类型 | 是否必须 | 描述                                                   |
| ------ | ---- | -------- |------------------------------------------------------|
| accountCode | String | 是 | 账户代码                                                 |
| type | String | 否 | 排行类型：BALANCE-余额排行，CONSUME-消费排行，INCOME-收入排行，默认BALANCE |
| limit | Integer | 否 | 返回记录数，默认100，最大1000                                   |

- **响应参数**：

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| list | Array | 排行列表 |
| list[].rank | Integer | 排名 |
| list[].userId | String | 用户ID |
| list[].userName | String | 用户名称 |
| list[].point | Integer | 积分数量 |

- **响应示例**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "rank": 1,
        "userId": "12345",
        "userName": "张三",
        "point": 10000
      },
      {
        "rank": 2,
        "userId": "12346",
        "userName": "李四",
        "point": 9500
      },
      {
        "rank": 3,
        "userId": "12347",
        "userName": "王五",
        "point": 9000
      }
    ]
  }
}
```

### 3.3 积分账户管理

#### 3.3.1 查询积分账户列表

**基本信息**
- **接口路径**：`GET /admin/api/v1/point/accounts`
- **接口描述**：查询品牌下所有积分账户配置列表
- **访问权限**：需要管理员权限
- **限流规则**：100次/分钟
- **幂等性**：不支持

**请求参数**

| 参数名         | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|-------------|------|------|------|------|------|------|
| brandCode   | String | Query | 是 | 品牌ID | "brand123" | 长度1-64 |
| accountCode | String | Query | 否 | 账户名称 | "MALL_APP" | 长度1-100，模糊匹配 |
| status      | String | Query | 否 | 账户状态 | "ACTIVE" | ACTIVE/INACTIVE |
| pageNum     | Integer | Query | 否 | 页码 | 1 | >=1 |
| pageSize    | Integer | Query | 否 | 每页大小 | 20 | 1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.total | Integer | 总数量 | 50 |
| data.list | Array | 账户列表 | - |
| data.list[].accountCode | String | 账户ID | "acc123" |
| data.list[].accountName | String | 账户名称 | "MALL_APP" |
| data.list[].accountType | String | 账户类型 | "BUSINESS" |
| data.list[].status | String | 账户状态 | "ACTIVE" |
| data.list[].description | String | 账户描述 | "商城积分账户" |
| data.list[].totalUsers | Integer | 用户总数 | 10000 |
| data.list[].totalPoints | Long | 积分总量 | 5000000 |
| data.list[].createTime | String | 创建时间 | "2024-01-01 10:00:00" |
| data.list[].updateTime | String | 更新时间 | "2024-01-15 15:30:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.2 查询积分账户详情

**基本信息**
- **接口路径**：`GET /admin/api/v1/point/account/{accountCode}`
- **接口描述**：查询指定积分账户的详细配置信息
- **访问权限**：需要管理员权限
- **限流规则**：200次/分钟
- **幂等性**：不支持

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Path | 是 | 账户ID | "acc123" | 长度1-64 |
| statistics | Boolean | Query | 否 | 是否统计数据 | true | - |

**响应参数**

| 参数名                             | 类型      | 描述      | 示例 |
|---------------------------------|---------|---------|------|
| code                            | Integer | 响应码     | 200 |
| message                         | String  | 响应消息    | "success" |
| data                            | Object  | 响应数据    | - |
| data.accountCode                | String  | 账户ID    | "acc123" |
| data.accountName                | String  | 账户名称    | "MALL_APP" |
| data.accountType                | String  | 账户类型    | "BUSINESS" |
| data.brandCode                  | String  | 品牌ID    | "brand123" |
| data.status                     | String  | 账户状态    | "ACTIVE" |
| data.description                | String  | 账户描述    | "商城积分账户" |
| data.basicConfig                | JSON    | 账户基础配置  | - |
| data.riskControlConfig          | JSON    | 账户风险控制配置 | - |
| data.extensionConfig            | JSON    | 账户基础配置  | - |
| data.statistics                 | Object  | 统计信息    | - |
| data.statistics.totalUsers      | Integer | 用户总数    | 10000 |
| data.statistics.totalPoints     | Long    | 积分总量    | 5000000 |
| data.statistics.availablePoints | Long    | 可用积分    | 4800000 |
| data.statistics.frozenPoints    | Long    | 冻结积分    | 200000 |
| data.statistics.expiringSoon    | Long    | 即将过期    | 50000 |
| data.createTime                 | String  | 创建时间    | "2024-01-01 10:00:00" |
| data.updateTime                 | String  | 更新时间    | "2024-01-15 15:30:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式 |
| 404001 | 积分账户不存在 | 检查账户ID是否正确 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.3 创建积分账户

**基本信息**
- **接口路径**：`POST /admin/api/v1/point/account`
- **接口描述**：创建新的积分账户配置
- **访问权限**：需要管理员权限
- **限流规则**：20次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Body | 是 | 账户代码 | "MALL_APP" | 长度1-100，同品牌下唯一 |
| accountName | String | Body | 是 | 账户名称 | "积分商城" | 长度1-100，同品牌下唯一 |
| accountType | String | Body | 是 | 账户类型 | "BUSINESS" | BUSINESS/PERSONAL |
| brandCode | String | Body | 是 | 品牌ID | "brand123" | 长度1-64 |
| description | String | Body | 否 | 账户描述 | "商城积分账户" | 长度0-500 |
| basicConfig | JSON | Body | 否 | 账户基础配置 | - | - |
| riskControlConfig | JSON | Body | 否 | 风险控制配置 | - | - |
| extensionConfig | JSON | Body | 否 | 扩展配置 | - | - |
| businessId | String | Body | 否 | 业务单号 | "biz20240101001" | 长度1-64，幂等标识 |
| operator | String | Body | 是 | 操作人 | "admin" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.accountCode | String | 账户ID | "acc123" |
| data.accountName | String | 账户名称 | "MALL_APP" |
| data.accountType | String | 账户类型 | "BUSINESS" |
| data.brandCode | String | 品牌ID | "brand123" |
| data.status | String | 账户状态 | "ACTIVE" |
| data.createTime | String | 创建时间 | "2024-01-01 10:00:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 400002 | 账户名称已存在 | 使用不同的账户名称 |
| 400003 | 业务单号重复 | 使用新的业务单号 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.4 更新积分账户

**基本信息**
- **接口路径**：`PUT /admin/api/v1/point/account/{accountCode}`
- **接口描述**：更新积分账户的配置信息
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟
- **幂等性**：支持（基于businessId）

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Path | 是 | 账户ID | "acc123" | 长度1-64 |
| accountName | String | Body | 否 | 账户名称 | "MALL_APP_V2" | 长度1-100，同品牌下唯一 |
| status | String | Body | 否 | 账户状态 | "ACTIVE" | ACTIVE/INACTIVE |
| description | String | Body | 否 | 账户描述 | "商城积分账户升级版" | 长度0-500 |
| basicConfig | JSON | Body | 否 | 账户基础配置 | - | - |
| riskControlConfig | JSON | Body | 否 | 风险控制配置 | - | - |
| extensionConfig | JSON | Body | 否 | 扩展配置 | - | - |
| businessId | String | Body | 否 | 业务单号 | "biz20240101002" | 长度1-64，幂等标识 |
| operator | String | Body | 是 | 操作人 | "admin" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.accountCode | String | 账户ID | "acc123" |
| data.accountName | String | 账户名称 | "MALL_APP_V2" |
| data.accountType | String | 账户类型 | "BUSINESS" |
| data.status | String | 账户状态 | "ACTIVE" |
| data.updateTime | String | 更新时间 | "2024-01-15 15:30:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 400002 | 账户名称已存在 | 使用不同的账户名称 |
| 400003 | 业务单号重复 | 使用新的业务单号 |
| 404001 | 积分账户不存在 | 检查账户ID是否正确 |
| 400004 | 账户状态不允许修改 | 检查当前账户状态 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

#### 3.3.5 删除积分账户

**基本信息**
- **接口路径**：`DELETE /admin/api/v1/point/account/{accountCode}`
- **接口描述**：删除积分账户配置（软删除）
- **访问权限**：需要管理员权限
- **限流规则**：10次/分钟
- **幂等性**：支持

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|--------|------|------|------|------|------|------|
| accountCode | String | Path | 是 | 账户ID | "acc123" | 长度1-64 |
| operator | String | Body | 是 | 操作人 | "admin" | 长度1-32 |
| reason | String | Body | 否 | 删除原因 | "业务调整" | 长度0-200 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| code | Integer | 响应码 | 200 |
| message | String | 响应消息 | "success" |
| data | Object | 响应数据 | - |
| data.accountCode | String | 账户ID | "acc123" |
| data.deleteTime | String | 删除时间 | "2024-01-15 16:00:00" |

**错误码**

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400001 | 参数校验失败 | 检查请求参数格式和必填项 |
| 404001 | 积分账户不存在 | 检查账户ID是否正确 |
| 400005 | 账户正在使用中 | 存在用户使用该账户，无法删除 |
| 400006 | 系统默认账户无法删除 | 系统默认账户不允许删除 |
| 403001 | 权限不足 | 确认是否有管理员权限 |
| 500001 | 系统内部错误 | 联系技术支持 |

## 4. 错误码定义

| 错误码 | 描述 | 处理建议 |
| ------ | ---- | -------- |
| 0 | 成功 | - |
| 1001 | 参数错误 | 检查请求参数是否符合要求 |
| 1002 | 用户不存在 | 确认用户ID是否正确 |
| 2001 | 积分不足 | 检查用户积分余额 |
| 2002 | 积分已过期 | 提示用户积分已过期 |
| 2003 | 积分已冻结 | 提示用户积分已被冻结 |
| 3001 | 规则不存在 | 检查规则代码是否正确 |
| 3002 | 规则已失效 | 检查规则状态和有效期 |
| 4001 | 业务单号重复 | 检查业务单号是否已使用 |
| 5001 | 系统内部错误 | 联系系统管理员 |
