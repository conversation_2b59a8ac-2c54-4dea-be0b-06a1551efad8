﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章进度统计结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeProgressStatQueryResult extends Extensible {    
    /**
     * 总用户数
     */
    private Long totalUsers;    
    /**
     * 进度统计明细
     */
    private java.util.List<BadgeProgressStatItem> progressStatList;}
