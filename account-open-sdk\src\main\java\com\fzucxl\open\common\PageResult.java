package com.fzucxl.open.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PageResult<T> {
    @Schema(title = "当前页")
    private Integer pageNum;
    @Schema(title = "页大小")
    private Integer pageSize;
    @Schema(title = "总记录数")
    private Long total;
    @Schema(title = "总页码")
    private Integer pages;
    @Schema(title = "明细数据")
    private List<T> list;
}
