﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章账户列表结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccountListQueryResult extends Extensible {    
    /**
     * 总记录数
     */
    private Long total;    
    /**
     * 当前页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 总页数
     */
    private Integer totalPages;    
    /**
     * 账户列表
     */
    private java.util.List<BadgeAccount> list;}
