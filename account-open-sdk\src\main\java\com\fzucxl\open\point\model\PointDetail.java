﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分明细模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointDetail extends Extensible {    
    /**
     * 明细ID
     */
    private String detailId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 积分数量
     */
    private Long point;    
    /**
     * 交易前余额
     */
    private Long beforeBalance;    
    /**
     * 交易后余额
     */
    private Long afterBalance;    
    /**
     * 积分来源
     */
    private String source;    
    /**
     * 来源名称
     */
    private String sourceName;    
    /**
     * 详细描述
     */
    private String description;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 过期时间
     */
    private String expireTime;    
    /**
     * 生效时间
     */
    private String effectiveTime;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 备注
     */
    private String remark;}
