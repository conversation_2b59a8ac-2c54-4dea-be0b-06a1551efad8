﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值类型分布模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthTypeDistribution extends Extensible {    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 成长值
     */
    private Long growthValue;    
    /**
     * 占比
     */
    private java.math.BigDecimal percentage;}
