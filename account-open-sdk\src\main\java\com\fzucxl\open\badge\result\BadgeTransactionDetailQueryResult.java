﻿package com.fzucxl.open.badge.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章交易详情结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeTransactionDetailQueryResult extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 业务类型
     */
    private String businessType;    
    /**
     * 交易类型
     */
    private String transactionType;    
    /**
     * 交易状态
     */
    private String status;    
    /**
     * 交易描述
     */
    private String description;    
    /**
     * 勋章来源
     */
    private String source;    
    /**
     * 处理时间
     */
    private String processedTime;    
    /**
     * 错误信息
     */
    private String errorMessage;    
    /**
     * 重试次数
     */
    private Integer retryCount;    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;    
    /**
     * 操作人ID
     */
    private String operatorId;    
    /**
     * 扩展数据
     */
    private String extraData;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;    
    /**
     * 版本号
     */
    private Integer version;}
