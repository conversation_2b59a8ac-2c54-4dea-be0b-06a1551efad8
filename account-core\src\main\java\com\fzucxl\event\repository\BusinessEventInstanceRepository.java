package com.fzucxl.event.repository;

import com.fzucxl.entity.event.BusinessEventInstance;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.annotation.Repository;
import io.micronaut.data.jdbc.annotation.JdbcRepository;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.model.query.builder.sql.Dialect;
import io.micronaut.data.repository.CrudRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 业务事件实例Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@JdbcRepository(dialect = Dialect.MYSQL)
public interface BusinessEventInstanceRepository extends CrudRepository<BusinessEventInstance, Long> {
    
    /**
     * 根据事件编码查找实例列表
     */
    List<BusinessEventInstance> findByEventCodeOrderByEventTimeDesc(String eventCode);
    
    /**
     * 根据用户ID查找实例列表
     */
    List<BusinessEventInstance> findByUserIdOrderByEventTimeDesc(String userId);
    
    /**
     * 根据事件编码和用户ID查找实例列表
     */
    List<BusinessEventInstance> findByEventCodeAndUserIdOrderByEventTimeDesc(String eventCode, String userId);
    
    /**
     * 根据状态查找实例列表
     */
    List<BusinessEventInstance> findByStatusOrderByCreatedAtDesc(String status);
    
    /**
     * 分页查询事件实例
     */
    Page<BusinessEventInstance> findByEventCodeAndStatus(String eventCode, String status, Pageable pageable);
    
    /**
     * 根据时间范围查找实例
     */
    List<BusinessEventInstance> findByEventTimeBetweenOrderByEventTimeDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据事件编码和时间范围查找实例
     */
    List<BusinessEventInstance> findByEventCodeAndEventTimeBetweenOrderByEventTimeDesc(
        String eventCode, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据用户ID和时间范围查找实例
     */
    List<BusinessEventInstance> findByUserIdAndEventTimeBetweenOrderByEventTimeDesc(
        String userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计用户在指定时间范围内的事件数量
     */
    @Query("SELECT COUNT(*) FROM business_event_instance WHERE user_id = :userId AND event_code = :eventCode AND event_time BETWEEN :startTime AND :endTime")
    long countByUserIdAndEventCodeAndEventTimeBetween(String userId, String eventCode, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计事件实例数量
     */
    long countByEventCodeAndStatus(String eventCode, String status);
    
    /**
     * 查找待处理的事件实例
     */
    List<BusinessEventInstance> findByStatusOrderByCreatedAtAsc(String status);
    
    /**
     * 根据来源查找事件实例
     */
    List<BusinessEventInstance> findBySourceAndStatusOrderByEventTimeDesc(String source, String status);
    
    /**
     * 查找最近的事件实例
     */
    Optional<BusinessEventInstance> findFirstByEventCodeAndUserIdOrderByEventTimeDesc(String eventCode, String userId);
}