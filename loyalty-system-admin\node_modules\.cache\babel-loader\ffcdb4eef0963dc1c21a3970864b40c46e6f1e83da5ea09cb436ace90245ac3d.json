{"ast": null, "code": "import * as React from 'react';\nimport { devUseWarning } from '../../_util/warning';\nconst names = {};\nexport default function useFormWarning(_ref) {\n  let {\n    name\n  } = _ref;\n  const warning = devUseWarning('Form');\n  React.useEffect(() => {\n    if (name) {\n      names[name] = (names[name] || 0) + 1;\n      process.env.NODE_ENV !== \"production\" ? warning(names[name] <= 1, 'usage', 'There exist multiple Form with same `name`.') : void 0;\n      return () => {\n        names[name] -= 1;\n      };\n    }\n  }, [name]);\n}", "map": {"version": 3, "names": ["React", "devUseW<PERSON>ning", "names", "useFormWarning", "_ref", "name", "warning", "useEffect", "process", "env", "NODE_ENV"], "sources": ["D:/code/private/fast-account/loyalty-system-admin/node_modules/antd/es/form/hooks/useFormWarning.js"], "sourcesContent": ["import * as React from 'react';\nimport { devUseWarning } from '../../_util/warning';\nconst names = {};\nexport default function useFormWarning({\n  name\n}) {\n  const warning = devUseWarning('Form');\n  React.useEffect(() => {\n    if (name) {\n      names[name] = (names[name] || 0) + 1;\n      process.env.NODE_ENV !== \"production\" ? warning(names[name] <= 1, 'usage', 'There exist multiple Form with same `name`.') : void 0;\n      return () => {\n        names[name] -= 1;\n      };\n    }\n  }, [name]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,qBAAqB;AACnD,MAAMC,KAAK,GAAG,CAAC,CAAC;AAChB,eAAe,SAASC,cAAcA,CAAAC,IAAA,EAEnC;EAAA,IAFoC;IACrCC;EACF,CAAC,GAAAD,IAAA;EACC,MAAME,OAAO,GAAGL,aAAa,CAAC,MAAM,CAAC;EACrCD,KAAK,CAACO,SAAS,CAAC,MAAM;IACpB,IAAIF,IAAI,EAAE;MACRH,KAAK,CAACG,IAAI,CAAC,GAAG,CAACH,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MACpCG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,OAAO,CAACJ,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,6CAA6C,CAAC,GAAG,KAAK,CAAC;MAClI,OAAO,MAAM;QACXH,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;MAClB,CAAC;IACH;EACF,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}