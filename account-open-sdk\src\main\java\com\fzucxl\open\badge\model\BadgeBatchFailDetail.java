﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章批量失败详情模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeBatchFailDetail extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 错误码
     */
    private String errorCode;    
    /**
     * 错误信息
     */
    private String errorMessage;}
