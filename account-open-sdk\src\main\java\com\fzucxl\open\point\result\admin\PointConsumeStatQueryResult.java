﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分消费统计结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointConsumeStatQueryResult extends Extensible {    
    /**
     * 总消费积分
     */
    private Long totalConsumed;    
    /**
     * 总用户数
     */
    private Integer totalUser;    
    /**
     * 平均消费积分
     */
    private Double avgConsumed;    
    /**
     * 消费统计详情
     */
    private java.util.List<PointConsumeStatDetail> statDetail;}
