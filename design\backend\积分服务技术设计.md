# 积分服务技术设计文档

## 1. 服务概述

### 1.1 服务职责
积分服务是账户体系的核心模块，负责用户积分的获取、消费、管理等全生命周期操作。

### 1.2 核心功能
- 积分账户管理
- 积分获取处理
- 积分消费处理
- 积分规则管理
- 积分规则引擎
- 积分明细记录
- 积分统计分析

## 2. 技术架构

### 2.1 服务架构图
```
┌─────────────────────────────────────────┐
│              积分服务                    │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐       │
│  │  积分控制器  │  │  积分服务层  │       │
│  └─────────────┘  └─────────────┘       │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  规则引擎   │  │  数据访问层  │       │
│  └─────────────┘  └─────────────┘       │
├─────────────────────────────────────────┤
│              数据存储                    │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │    MySQL    │  │    Redis    │       │
│  └─────────────┘  └─────────────┘       │
└─────────────────────────────────────────┘
```

### 2.2 核心类设计

#### 2.2.0 积分系统实体概览

积分系统涉及多个核心实体，用于支撑完整的积分业务流程。以下是各实体的统一描述：

| 实体名称       | 实体类 | 表名 | 主要用途 | 核心字段 |
|------------| ------ | ---- | -------- | -------- |
| **积分账户** | PointAccount | point_account | 存储积分账户的基本配置信息和统计数据，管理积分账户的生命周期 | accountCode, accountName, accountType, brandCode, status, basicConfig |
| **用户积分账户** | UserPoint | user_point | 存储用户积分账户基本信息和余额 | userId, totalPoint, availablePoint, frozenPoint |
| **积分明细**   | PointDetail | point_detail | 记录所有积分变动的详细信息 | userId, point, type, source, businessId, expireTime |
| **有效积分记录** | PointValidRecord | point_valid_record | 管理积分的有效期和过期处理 | userId, point, expireTime, status |
| **积分明细追踪** | PointDetailTrace | point_detail_trace | 追踪积分明细之间的关联关系 | sourceDetailId, targetDetailId, traceType |
| **积分冻结记录** | PointFreezeRecord | point_freeze_record | 管理积分冻结和解冻操作 | userId, point, freezeType, reason, expireTime |
| **积分事务**   | PointTransaction | point_transaction | 记录积分交易的完整生命周期 | transactionId, userId, type, status, businessId |
| **积分规则**   | PointRule | point_rule | 存储积分计算规则 | accountCode, ruleCode, ruleExpression, priority |
| **属性元数据**  | AttributeMeta | attribute_meta | 定义规则引擎中使用的属性配置 | attributeCode, dataType, dataSource, aggregateFunction |
| **业务事件**   | BusinessEvent | business_event | 定义可触发积分计算的业务事件 | eventCode, eventName, eventType, status |
| **事件字段**   | BusinessEventField | business_event_field | 定义业务事件的字段结构和属性 | eventCode, fieldName, fieldType, required, description |
| **事件实例**   | BusinessEventInstance | business_event_instance | 记录业务事件的具体执行实例 | eventCode, instanceId, eventData, processStatus |

#### 实体关系说明

**核心关系链路：**
1. **用户积分流程**：UserPoint ← PointDetail ← PointValidRecord
2. **积分追踪链路**：PointDetail ← PointDetailTrace → PointDetail
3. **冻结管理链路**：UserPoint ← PointFreezeRecord
4. **规则执行链路**：BusinessEvent → PointRule → AttributeMeta
5. **事务管理链路**：PointTransaction ← PointDetail

**数据一致性保障：**
- 积分账户余额 = 所有有效积分明细的汇总
- 冻结积分通过独立记录管理，不影响明细表
- 积分追踪记录保证操作的可审计性
- 事务记录确保操作的原子性和可回滚性

**表索引设计：**
- user_point: 唯一索引(user_id, account_code)
- point_detail: 复合索引(user_id, created_time)，索引(expire_time)
- point_valid_record: 复合索引(user_id, expire_time)
- point_freeze_record: 复合索引(user_id, status)
- point_transaction: 唯一索引(transaction_id)，索引(biz_no)

#### 2.2.1 用户积分实体
```java
@MappedEntity("user_point")
public class UserPoint {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", unique = true, nullable = false)
    private Long userId;
    
    @Column(name = "total_point", nullable = false)
    private Long totalPoint = 0L;
    
    @Column(name = "available_point", nullable = false)
    private Long availablePoint = 0L;
    
    @Column(name = "frozen_point", nullable = false)
    private Long frozenPoint = 0L;
    
    @Column(name = "expired_point", nullable = false)
    private Long expiredPoint = 0L;
    
    @Version
    private Integer version = 0;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
}
```

#### 2.2.2 积分账户实体

**实体名称**：积分账户  
**实体类**：PointAccount  
**表名**：point_account  
**用途**：存储用户积分账户的基本配置信息和统计数据，管理积分账户的生命周期  
**核心字段**：accountCode, accountName, accountType, brandCode, status, basicConfig, riskControlConfig

```java
@MappedEntity("point_account")
public class PointAccount {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "account_code", unique = true, nullable = false)
    private String accountCode;
    
    @Column(name = "account_name", nullable = false)
    private String accountName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false)
    private AccountType accountType;
    
    @Column(name = "brand_code", nullable = false)
    private String brandCode;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AccountStatus status = AccountStatus.ACTIVE;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "basic_config", columnDefinition = "JSON")
    private String basicConfig;
    
    @Column(name = "risk_control_config", columnDefinition = "JSON")
    private String riskControlConfig;
    
    @Column(name = "extension_config", columnDefinition = "JSON")
    private String extensionConfig;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    @Version
    private Integer version = 0;
}

/**
 * 账户类型枚举
 */
public enum AccountType {
    BUSINESS("业务账户"),
    PERSONAL("个人账户"),
    SYSTEM("系统账户");
    
    private final String description;
    
    AccountType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

/**
 * 账户状态枚举
 */
public enum AccountStatus {
    ACTIVE("激活"),
    INACTIVE("停用"),
    FROZEN("冻结"),
    DELETED("已删除");
    
    private final String description;
    
    AccountStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

**字段说明：**
- **accountCode**: 账户代码，全局唯一标识
- **accountName**: 账户名称，用于显示和管理
- **accountType**: 账户类型，区分业务账户、个人账户、系统账户
- **brandCode**: 品牌代码，支持多品牌管理
- **status**: 账户状态，控制账户的可用性
- **basicConfig**: 基础配置，JSON格式存储积分单位、汇率等配置
- **riskControlConfig**: 风控配置，JSON格式存储限额、频控等配置
- **extensionConfig**: 扩展配置，JSON格式存储自定义配置

#### 2.2.3 积分明细实体
```java
@MappedEntity("point_detail")
public class PointDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "transaction_id", nullable = false)
    private String transactionId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "point_type", nullable = false)
    private PointType pointType;

    @Column(name = "balance_before", nullable = false)
    private Long balanceBefore;
    
    @Column(name = "point", nullable = false)
    private Long point;
    
    @Column(name = "balance_after", nullable = false)
    private Long balanceAfter;
    
    @Column(name = "business_type", nullable = false)
    private String businessType;
    
    @Column(name = "business_id")
    private String businessId;

    @Column(name = "rule_code", length = 64)
    private String ruleCode;
    
    @Column(name = "source", nullable = false)
    private String source;
    
    @Column(name = "description")
    private String description;

    @Column(name = "record_time")
    private LocalDateTime recordTime;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PointStatus status = PointStatus.VALID;
    
    @CreationTimestamp
    private LocalDateTime createTime;

    @Column(name = "extra_data", columnDefinition = "JSON")
    private String extraData;
}
```

#### 2.2.4 有效积分记录实体
```java
@MappedEntity("point_valid_record")
public class PointValidRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "transaction_id", nullable = false)
    private String transactionId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "point_type", nullable = false)
    private PointType pointType;
    
    @Column(name = "issued_point", nullable = false)
    private Long issuedPoint;

    @Column(name = "remaining_point", nullable = false)
    private Long remainingPoint;
    
    @Column(name = "issue_time", nullable = false)
    private LocalDateTime issueTime;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "record_status", nullable = false)
    private ValidRecordStatus recordStatus = ValidRecordStatus.VALID;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    @Version
    private Integer version = 0;
}

/**
 * 有效积分记录状态枚举
 */
public enum ValidRecordStatus {
    VALID("有效"),
    EXPIRED("已过期"),
    CONSUMED("已消费完"),
    FROZEN("已冻结");
    
    private final String description;
    
    ValidRecordStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

#### 2.2.5 积分明细追踪实体
```java
@MappedEntity("point_detail_trace")
public class PointDetailTrace {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "source_transaction_id", nullable = false)
    private String sourceTransactionId;
    
    @Column(name = "transaction_id", nullable = false)
    private String transactionId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "trace_type", nullable = false)
    private TraceType traceType;
    
    @Column(name = "point", nullable = false)
    private Long point;
    
    @Column(name = "source_detail_id", nullable = false)
    private Long sourceDetailId;
    
    @Column(name = "target_detail_id", nullable = false)
    private Long targetDetailId;
    
    @Column(name = "business_type", nullable = false)
    private String businessType;
    
    @Column(name = "trace_reason")
    private String traceReason;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    // 关联查询字段
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_detail_id", insertable = false, updatable = false)
    private PointDetail sourceDetail;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "detail_id", insertable = false, updatable = false)
    private PointDetail detail;
}

/**
 * 追踪类型枚举
 */
public enum TraceType {
    EARN("发放追踪"),
    CONSUME_TO_EARN("消费追踪发放"),
    TRANSFER_OUT("转出追踪"),
    TRANSFER_IN("转入追踪"),
    EXPIRE_TO_EARN("过期追踪发放"),
    FREEZE_TO_EARN("冻结追踪发放"),
    UNFREEZE_TO_EARN("解冻追踪发放");
    
    private final String description;
    
    TraceType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

#### 2.2.6 积分冻结记录实体
```java
@MappedEntity("point_freeze_record")
public class PointFreezeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "frozen_point", nullable = false)
    private Long frozenPoint;
    
    @Column(name = "freeze_time", nullable = false)
    private LocalDateTime freezeTime;
    
    @Column(name = "unfreeze_time")
    private LocalDateTime unfreezeTime;
    
    @Column(name = "freeze_deadline", nullable = false)
    private LocalDateTime freezeDeadline;
    
    @Column(name = "writeoff_time")
    private LocalDateTime writeoffTime;
    
    @Column(name = "freeze_transaction_id", nullable = false)
    private String freezeTransactionId;
    
    @Column(name = "unfreeze_transaction_id")
    private String unfreezeTransactionId;
    
    @Column(name = "writeoff_transaction_id")
    private String writeoffTransactionId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private FreezeRecordStatus status = FreezeRecordStatus.FROZEN;
    
    @Column(name = "freeze_reason")
    private String freezeReason;
    
    @Column(name = "business_type")
    private String businessType;
    
    @Column(name = "business_id")
    private String businessId;
    
    @Column(name = "operator_id")
    private Long operatorId;
    
    @Column(name = "remark")
    private String remark;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    @Version
    private Integer version = 0;
}

/**
 * 冻结记录状态枚举
 */
public enum FreezeRecordStatus {
    FROZEN("冻结中"),
    UNFROZEN("已解冻"),
    WRITTEN_OFF("已核销"),
    EXPIRED("已过期");
    
    private final String description;
    
    FreezeRecordStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为有效冻结状态（需要扣减余额）
     */
    public boolean isEffectiveFrozen() {
        return this == FROZEN;
    }
}
```

#### 2.2.7 积分事务实体
```java
@MappedEntity("point_transaction")
public class PointTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "transaction_id", unique = true, nullable = false)
    private String transactionId;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private TransactionType transactionType;
    
    @Column(name = "point", nullable = false)
    private Long point;
    
    @Column(name = "balance_before", nullable = false)
    private Long balanceBefore;
    
    @Column(name = "balance_after", nullable = false)
    private Long balanceAfter;
    
    @Column(name = "available_before", nullable = false)
    private Long availableBefore;
    
    @Column(name = "available_after", nullable = false)
    private Long availableAfter;
    
    @Column(name = "frozen_before", nullable = false)
    private Long frozenBefore = 0L;
    
    @Column(name = "frozen_after", nullable = false)
    private Long frozenAfter = 0L;
    
    @Column(name = "business_type", nullable = false)
    private String businessType;
    
    @Column(name = "business_id")
    private String businessId;
    
    @Column(name = "source", nullable = false)
    private String source;
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_status", nullable = false)
    private TransactionStatus transactionStatus = TransactionStatus.SUCCESS;
    
    @Column(name = "expire_time")
    private LocalDateTime expireTime;
    
    @Column(name = "related_transaction_id")
    private String relatedTransactionId;
    
    @Column(name = "operator_id")
    private Long operatorId;
    
    @Column(name = "operator_type")
    private String operatorType;
    
    @Column(name = "request_id")
    private String requestId;
    
    @Column(name = "client_ip")
    private String clientIp;
    
    @Column(name = "user_agent")
    private String userAgent;
    
    @Column(name = "input_data", columnDefinition = "JSON")
    private String inputData;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
    
    @Version
    private Integer version = 0;
}

/**
 * 事务类型枚举
 */
public enum TransactionType {
    EARN("积分发放"),
    CONSUME("积分消费"),
    EXPIRE("积分过期"),
    FREEZE("积分冻结"),
    UNFREEZE("积分解冻"),
    TRANSFER_OUT("积分转出"),
    TRANSFER_IN("积分转入"),
    REFUND("积分退款"),
    ADJUST("积分调整"),
    CANCEL("积分撤销");
    
    private final String description;
    
    TransactionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为增加积分的事务类型
     */
    public boolean isIncreaseType() {
        return this == EARN || this == TRANSFER_IN || this == REFUND || 
               this == UNFREEZE || (this == ADJUST);
    }
    
    /**
     * 判断是否为减少积分的事务类型
     */
    public boolean isDecreaseType() {
        return this == CONSUME || this == EXPIRE || this == TRANSFER_OUT || 
               this == FREEZE || this == CANCEL;
    }
}

/**
 * 事务状态枚举
 */
public enum TransactionStatus {
    PENDING("处理中"),
    SUCCESS("成功"),
    FAILED("失败"),
    CANCELLED("已取消");
    
    private final String description;
    
    TransactionStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
```

#### 2.2.8 业务事件字段实体
```java
@MappedEntity("business_event_field")
@Introspected
@Serdeable
public class BusinessEventField {
    
    @Id
    @GeneratedValue(GeneratedValue.Type.IDENTITY)
    private Long id;
    
    /**
     * 关联的业务事件ID
     */
    private Long eventId;
    
    /**
     * 字段编码
     */
    private String fieldCode;
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 字段类型：STRING, INTEGER, LONG, DOUBLE, BOOLEAN, DATE, DATETIME, LIST, JSON
     */
    private String fieldType;
    
    /**
     * 字段描述
     */
    private String description;
    
    /**
     * 是否必填
     */
    private Boolean required = false;
    
    /**
     * 默认值
     */
    private String defaultValue;
    
    /**
     * 字段验证规则（JSON格式）
     */
    private String validationRule;
    
    /**
     * 字段排序
     */
    private Integer sortOrder = 0;
    
    /**
     * 字段状态：ACTIVE-启用，INACTIVE-禁用
     */
    private String status = "ACTIVE";
    
    /**
     * 创建时间
     */
    @DateCreated
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @DateUpdated
    private LocalDateTime updatedAt;
    
    /**
     * 关联的业务事件
     */
    @Relation(value = Relation.Kind.MANY_TO_ONE)
    @JoinColumn(name = "event_id")
    private BusinessEvent businessEvent;
}
```

**字段说明：**
- **id**: 主键ID，自增长
- **eventId**: 关联的业务事件ID，外键关联business_event表
- **fieldCode**: 字段编码，用于在规则表达式中引用
- **fieldName**: 字段名称，用于显示
- **fieldType**: 字段类型，支持STRING、INTEGER、LONG、DOUBLE、BOOLEAN、DATE、DATETIME、LIST、JSON等类型
- **description**: 字段描述，说明字段的用途和含义
- **required**: 是否必填，控制字段验证
- **defaultValue**: 默认值，当字段值为空时使用
- **validationRule**: 字段验证规则，JSON格式存储复杂验证逻辑
- **sortOrder**: 字段排序，控制字段在界面上的显示顺序
- **status**: 字段状态，ACTIVE表示启用，INACTIVE表示禁用
- **createdAt**: 创建时间，自动设置
- **updatedAt**: 更新时间，自动更新
- **businessEvent**: 关联的业务事件对象，多对一关系

**表结构：**
```sql
CREATE TABLE business_event_field (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT NOT NULL,
    field_code VARCHAR(100) NOT NULL,
    field_name VARCHAR(200) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    description TEXT,
    required BOOLEAN DEFAULT FALSE,
    default_value TEXT,
    validation_rule TEXT,
    sort_order INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES business_event(id),
    UNIQUE KEY uk_event_field_code (event_id, field_code),
    INDEX idx_event_id (event_id),
    INDEX idx_status (status)
);
```

## 3. 核心业务逻辑

### 3.1 积分获取流程
```java
@Service
@Transactional
public class PointService {
    
    @Autowired
    private UserPointRepository accountRepository;
    
    @Autowired
    private PointDetailRepository detailRepository;
    
    @Autowired
    private PointCalculationEngine calculationEngine;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public PointTransaction earnPoint(EarnPointRequest request) {
        // 1. 参数校验
        validateEarnPointRequest(request);
        
        // 2. 分布式锁防并发
        String lockKey = "point:lock:" + request.getUserId();
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (!lock.tryLock(5, TimeUnit.SECONDS)) {
                throw new BusinessException("系统繁忙，请稍后重试");
            }
            
            // 3. 获取用户积分账户
            UserPoint account = getOrCreateAccount(request.getUserId());
            
            // 4. 计算积分
            PointCalculationResult result = calculationEngine.calculate(request);
            
            // 5. 检查每日限额
            checkDailyLimit(request.getUserId(), result.getFinalPoint());
            
            // 6. 更新账户余额
            account.setTotalPoint(account.getTotalPoint() + result.getFinalPoint());
            account.setAvailablePoint(account.getAvailablePoint() + result.getFinalPoint());
            accountRepository.save(account);
            
            // 7. 记录积分明细
            PointDetail detail = buildPointDetail(request, result, account);
            detailRepository.save(detail);
            
            // 8. 清除缓存
            clearAccountCache(request.getUserId());
            
            // 9. 发布事件
            publishPointEarnedEvent(request.getUserId(), result.getFinalPoint());
            
            return buildPointTransaction(detail);
            
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

### 3.2 积分余额查询服务

```java
@Service
public class PointBalanceService {
    
    @Autowired
    private UserPointRepository accountRepository;
    
    @Autowired
    private PointFreezeRecordRepository freezeRecordRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 获取用户实时可用积分余额
     */
    public Long getAvailablePoint(Long userId) {
        // 1. 获取账户总可用积分
        UserPoint account = accountRepository.findByUserId(userId);
        if (account == null) {
            return 0L;
        }
        
        Long availablePoint = account.getAvailablePoint();
        
        // 2. 计算当前有效冻结积分
        Long effectiveFrozenPoint = getEffectiveFrozenPoint(userId);
        
        // 3. 返回实际可用积分
        return Math.max(0L, availablePoint - effectiveFrozenPoint);
    }
    
    /**
     * 获取用户当前有效冻结积分
     * 状态为冻结并且当前时间小于冻结截止时间的记录
     */
    public Long getEffectiveFrozenPoint(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        // 查询状态为冻结且未过期的记录
        List<PointFreezeRecord> effectiveRecords = freezeRecordRepository
            .findByUserIdAndStatusAndFreezeDeadlineAfter(
                userId, FreezeRecordStatus.FROZEN, now);
        
        return effectiveRecords.stream()
            .mapToLong(PointFreezeRecord::getFrozenPoint)
            .sum();
    }
    
    /**
     * 获取用户积分余额详情
     */
    public PointBalanceDetail getBalanceDetail(Long userId) {
        UserPoint account = accountRepository.findByUserId(userId);
        if (account == null) {
            return PointBalanceDetail.empty(userId);
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 统计各种状态的冻结积分
        Map<FreezeRecordStatus, Long> frozenSummary = freezeRecordRepository
            .findByUserId(userId)
            .stream()
            .collect(Collectors.groupingBy(
                PointFreezeRecord::getStatus,
                Collectors.summingLong(PointFreezeRecord::getFrozenPoint)
            ));
        
        // 计算有效冻结积分（状态为冻结且未过期）
        Long effectiveFrozenPoint = getEffectiveFrozenPoint(userId);
        
        return PointBalanceDetail.builder()
            .userId(userId)
            .totalPoint(account.getTotalPoint())
            .availablePoint(account.getAvailablePoint())
            .effectiveFrozenPoint(effectiveFrozenPoint)
            .realAvailablePoint(Math.max(0L, account.getAvailablePoint() - effectiveFrozenPoint))
            .frozenPoint(frozenSummary.getOrDefault(FreezeRecordStatus.FROZEN, 0L))
            .unfrozenPoint(frozenSummary.getOrDefault(FreezeRecordStatus.UNFROZEN, 0L))
            .writtenOffPoint(frozenSummary.getOrDefault(FreezeRecordStatus.WRITTEN_OFF, 0L))
            .expiredFrozenPoint(frozenSummary.getOrDefault(FreezeRecordStatus.EXPIRED, 0L))
            .build();
    }
}
```

### 3.3 积分冻结服务

```java
@Service
@Transactional
public class PointFreezeService {
    
    @Autowired
    private PointFreezeRecordRepository freezeRecordRepository;
    
    @Autowired
    private PointTransactionService transactionService;
    
    @Autowired
    private PointBalanceService balanceService;
    
    @Autowired
    private UserPointRepository accountRepository;
    
    /**
     * 冻结积分
     */
    public PointFreezeRecord freezePoint(FreezePointRequest request) {
        // 1. 参数校验
        validateFreezeRequest(request);
        
        // 2. 检查可用余额
        Long availablePoint = balanceService.getAvailablePoint(request.getUserId());
        if (availablePoint < request.getFrozenPoint()) {
            throw new InsufficientPointException(
                request.getUserId(), request.getFrozenPoint(), availablePoint);
        }
        
        // 3. 创建冻结记录
        PointFreezeRecord freezeRecord = PointFreezeRecord.builder()
            .userId(request.getUserId())
            .frozenPoint(request.getFrozenPoint())
            .freezeTime(LocalDateTime.now())
            .freezeDeadline(request.getFreezeDeadline())
            .freezeTransactionId(generateTransactionId())
            .status(FreezeRecordStatus.FROZEN)
            .freezeReason(request.getFreezeReason())
            .businessType(request.getBusinessType())
            .businessId(request.getBusinessId())
            .operatorId(request.getOperatorId())
            .remark(request.getRemark())
            .build();
        
        freezeRecordRepository.save(freezeRecord);
        
        // 4. 记录冻结事务
        transactionService.recordFreezeTransaction(freezeRecord);
        
        // 5. 清除缓存
        clearBalanceCache(request.getUserId());
        
        return freezeRecord;
    }
    
    /**
     * 解冻积分
     */
    public PointFreezeRecord unfreezePoint(Long freezeRecordId, String unfreezeReason) {
        PointFreezeRecord freezeRecord = freezeRecordRepository.findById(freezeRecordId)
            .orElseThrow(() -> new BusinessException("冻结记录不存在"));
        
        if (freezeRecord.getStatus() != FreezeRecordStatus.FROZEN) {
            throw new BusinessException("只能解冻状态为冻结的记录");
        }
        
        // 更新记录状态
        freezeRecord.setStatus(FreezeRecordStatus.UNFROZEN);
        freezeRecord.setUnfreezeTime(LocalDateTime.now());
        freezeRecord.setUnfreezeTransactionId(generateTransactionId());
        freezeRecord.setRemark(freezeRecord.getRemark() + "; 解冻原因: " + unfreezeReason);
        
        freezeRecordRepository.save(freezeRecord);
        
        // 记录解冻事务
        transactionService.recordUnfreezeTransaction(freezeRecord);
        
        // 清除缓存
        clearBalanceCache(freezeRecord.getUserId());
        
        return freezeRecord;
    }
    
    /**
     * 核销积分
     */
    public PointFreezeRecord writeOffPoint(Long freezeRecordId, String writeOffReason) {
        PointFreezeRecord freezeRecord = freezeRecordRepository.findById(freezeRecordId)
            .orElseThrow(() -> new BusinessException("冻结记录不存在"));
        
        if (freezeRecord.getStatus() != FreezeRecordStatus.FROZEN) {
            throw new BusinessException("只能核销状态为冻结的记录");
        }
        
        // 更新记录状态
        freezeRecord.setStatus(FreezeRecordStatus.WRITTEN_OFF);
        freezeRecord.setWriteoffTime(LocalDateTime.now());
        freezeRecord.setWriteoffTransactionId(generateTransactionId());
        freezeRecord.setRemark(freezeRecord.getRemark() + "; 核销原因: " + writeOffReason);
        
        freezeRecordRepository.save(freezeRecord);
        
        // 记录核销事务并扣减账户余额
        transactionService.recordWriteOffTransaction(freezeRecord);
        
        // 从账户中扣减核销的积分
        UserPoint account = accountRepository.findByUserId(freezeRecord.getUserId());
        account.setAvailablePoint(account.getAvailablePoint() - freezeRecord.getFrozenPoint());
        account.setTotalPoint(account.getTotalPoint() - freezeRecord.getFrozenPoint());
        accountRepository.save(account);
        
        // 清除缓存
        clearBalanceCache(freezeRecord.getUserId());
        
        return freezeRecord;
    }
    
    /**
     * 定时处理过期冻结记录
     */
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void processExpiredFreezeRecords() {
        LocalDateTime now = LocalDateTime.now();
        
        List<PointFreezeRecord> expiredRecords = freezeRecordRepository
            .findByStatusAndFreezeDeadlineBefore(FreezeRecordStatus.FROZEN, now);
        
        for (PointFreezeRecord record : expiredRecords) {
            record.setStatus(FreezeRecordStatus.EXPIRED);
            freezeRecordRepository.save(record);
            
            // 清除用户缓存
            clearBalanceCache(record.getUserId());
        }
        
        if (!expiredRecords.isEmpty()) {
            log.info("处理过期冻结记录: {} 条", expiredRecords.size());
        }
    }
    
    private void validateFreezeRequest(FreezePointRequest request) {
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (request.getFrozenPoint() == null || request.getFrozenPoint() <= 0) {
            throw new IllegalArgumentException("冻结积分必须大于0");
        }
        if (request.getFreezeDeadline() == null) {
            throw new IllegalArgumentException("冻结截止时间不能为空");
        }
        if (request.getFreezeDeadline().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("冻结截止时间不能早于当前时间");
        }
    }
    
    private String generateTransactionId() {
        return "TXN" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(6);
    }
    
    private void clearBalanceCache(Long userId) {
        String cacheKey = "point:balance:" + userId;
        redisTemplate.delete(cacheKey);
    }
}
```

### 3.4 积分消费流程
```java
public PointTransaction consumePoint(ConsumePointRequest request) {
    // 1. 参数校验
    validateConsumePointRequest(request);
    
    // 2. 分布式锁
    String lockKey = "point:lock:" + request.getUserId();
    RLock lock = redissonClient.getLock(lockKey);
    
    try {
        if (!lock.tryLock(5, TimeUnit.SECONDS)) {
            throw new BusinessException("系统繁忙，请稍后重试");
        }
        
        // 3. 获取用户积分账户
        UserPoint account = accountRepository.findByUserId(request.getUserId());
        if (account == null) {
            throw new BusinessException("用户积分账户不存在");
        }
        
        // 4. 检查积分余额
        if (account.getAvailablePoint() < request.getPointAmount()) {
            throw new BusinessException("积分余额不足");
        }
        
        // 5. 扣减积分（FIFO原则）
        List<PointDetail> availableDetail = getAvailablePointDetail(request.getUserId());
        long remainingAmount = request.getPointAmount();
        
        for (PointDetail detail : availableDetail) {
            if (remainingAmount <= 0) break;
            
            long consumeAmount = Math.min(remainingAmount, detail.getPointAmount());
            detail.setPointAmount(detail.getPointAmount() - consumeAmount);
            
            if (detail.getPointAmount() == 0) {
                detail.setStatus(PointStatus.USED);
            }
            
            detailRepository.save(detail);
            remainingAmount -= consumeAmount;
        }
        
        // 6. 更新账户余额
        account.setAvailablePoint(account.getAvailablePoint() - request.getPointAmount());
        accountRepository.save(account);
        
        // 7. 记录消费明细
        PointDetail consumeDetail = buildConsumeDetail(request, account);
        detailRepository.save(consumeDetail);
        
        // 8. 清除缓存
        clearAccountCache(request.getUserId());
        
        // 9. 发布事件
        publishPointConsumedEvent(request.getUserId(), request.getPointAmount());
        
        return buildPointTransaction(consumeDetail);
        
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
```

## 4. 积分计算引擎

### 4.1 规则引擎技术选型

积分服务的规则引擎采用 **阿里QLExpress** 作为核心技术，结合自定义规则解析器实现灵活的积分计算逻辑。

#### 4.1.1 技术架构
```
┌─────────────────────────────────────────┐
│            规则引擎架构                  │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐       │
│  │  规则管理器  │  │QLExpress引擎 │       │
│  └─────────────┘  └─────────────┘       │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  规则解析器  │  │  规则缓存    │       │
│  └─────────────┘  └─────────────┘       │
├─────────────────────────────────────────┤
│              规则存储                    │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │  规则配置表  │  │  表达式文件  │       │
│  └─────────────┘  └─────────────┘       │
└─────────────────────────────────────────┘
```

#### 4.1.2 QLExpress依赖配置
```xml
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>QLExpress</artifactId>
    <version>4.0.1</version>
</dependency>
```

### 4.2 规则引擎配置
```java
@Configuration
@EnableConfigurationProperties(Express4Properties.class)
public class Express4Config {
    
    @Autowired
    private Express4Properties express4Properties;
    
    @Bean
    public Express4Runner express4Runner() {
        Express4Runner runner = new Express4Runner();
        
        // 配置运行参数
        runner.setTrace(qlExpressProperties.isTrace());
        runner.setPrecise(qlExpressProperties.isPrecise());
        runner.setShortCircuit(qlExpressProperties.isShortCircuit());
        
        // 注册自定义函数
        registerCustomFunctions(runner);
        
        // 注册自定义操作符
        registerCustomOperators(runner);
        
        return runner;
    }
    
    @Bean
    public RuleExecutionEngine ruleExecutionEngine(Express4Runner express4Runner) {
        return new RuleExecutionEngine(express4Runner);
    }
    
    private void registerCustomFunctions(Express4Runner runner) {
        try {
            // 注册积分计算相关函数
            runner.addFunction("calculateLadderPoint", new CalculateLadderPointFunction());
            runner.addFunction("getLevelMultiplier", new GetLevelMultiplierFunction());
            runner.addFunction("checkTimeRange", new CheckTimeRangeFunction());
            runner.addFunction("formatDate", new FormatDateFunction());
            
        } catch (Exception e) {
            throw new RuntimeException("注册自定义函数失败", e);
        }
    }
    
    private void registerCustomOperators(Express4Runner runner) {
        try {
            // 注册自定义操作符
            runner.addOperator("between", new BetweenOperator());
            runner.addOperator("in", new InOperator());
            
        } catch (Exception e) {
            throw new RuntimeException("注册自定义操作符失败", e);
        }
    }
}
```

### 4.3 计算引擎设计
```java
@Component
@Slf4j
public class PointCalculationEngine {
    
    @Inject
    private RuleExecutionEngine ruleExecutionEngine;
    
    @Autowired
    private PointRuleService ruleService;
    
    @Autowired
    private LevelService levelService;
    
    @Autowired
    private ActivityService activityService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public PointCalculationResult calculate(PointCalculationRequest request) {
        // 创建规则执行上下文
        RuleExecutionContext context = buildRuleContext(request);
        
        // 执行QLExpress规则
        executeRule(context);
        
        // 构建计算结果
        return buildCalculationResult(context);
    }
    
    private RuleExecutionContext buildRuleContext(PointCalculationRequest request) {
        RuleExecutionContext context = new RuleExecutionContext();
        context.setUserId(request.getUserId());
        context.setBusinessType(request.getBusinessType());
        context.setAmount(request.getAmount());
        context.setTimestamp(LocalDateTime.now());
        
        // 设置用户等级信息
        UserLevel userLevel = levelService.getUserLevel(request.getUserId());
        context.setUserLevel(userLevel.getCurrentLevel());
        context.setLevelMultiplier(getLevelMultiplier(userLevel.getCurrentLevel()));
        
        // 设置活动信息
        List<ActivityRule> activeRule = activityService.getActiveRule(request.getBusinessType());
        context.setActiveRule(activeRule);
        
        // 设置基础规则
        PointRule baseRule = ruleService.getBaseRule(request.getBusinessType());
        context.setBaseRule(baseRule);
        
        return context;
    }
    
    private void executeRule(RuleExecutionContext context) {
        try {
            // 准备执行上下文
            DefaultContext<String, Object> expressContext = new DefaultContext<>();
            expressContext.put("context", context);
            expressContext.put("userId", context.getUserId());
            expressContext.put("businessType", context.getBusinessType());
            expressContext.put("amount", context.getAmount());
            expressContext.put("userLevel", context.getUserLevel());
            expressContext.put("levelMultiplier", context.getLevelMultiplier());
            expressContext.put("baseRule", context.getBaseRule());
            expressContext.put("activeRule", context.getActiveRule());
            expressContext.put("timestamp", context.getTimestamp());
            
            // 执行基础积分计算规则
            executeBasePointRule(expressContext, context);
            
            // 执行等级加成规则
            executeLevelBonusRule(expressContext, context);
            
            // 执行活动加成规则
            executeActivityBonusRule(expressContext, context);
            
            // 计算最终积分
            context.calculateFinalPoint();
            
            log.debug("规则执行完成，最终积分: {}", context.getFinalPoint());
            
        } catch (Exception e) {
            log.error("规则执行失败", e);
            throw new RuleExecutionException("规则执行失败", e);
        }
    }
    
    private void executeBasePointRule(DefaultContext<String, Object> expressContext, RuleExecutionContext context) throws Exception {
        PointRule baseRule = context.getBaseRule();
        if (baseRule == null) return;
        
        String expression = buildBasePointExpression(baseRule);
        Object result = qlExpressRuleEngine.execute(expression, expressContext);
        
        if (result instanceof Number) {
            Long basePoint = ((Number) result).longValue();
            context.addBasePoint(basePoint);
            context.addAppliedRule("基础积分计算");
        }
    }
    
    private void executeLevelBonusRule(DefaultContext<String, Object> expressContext, RuleExecutionContext context) throws Exception {
        if (context.getUserLevel() <= 0) return;
        
        String expression = buildLevelBonusExpression(context.getUserLevel());
        Object result = qlExpressRuleEngine.execute(expression, expressContext);
        
        if (result instanceof Number) {
            Long levelBonus = ((Number) result).longValue();
            context.addLevelBonus(levelBonus);
            context.addAppliedRule("等级加成计算");
        }
    }
    
    private void executeActivityBonusRule(DefaultContext<String, Object> expressContext, RuleExecutionContext context) throws Exception {
        List<ActivityRule> activeRule = context.getActiveRule();
        if (activeRule == null || activeRule.isEmpty()) return;
        
        for (ActivityRule activityRule : activeRule) {
            String expression = buildActivityBonusExpression(activityRule);
            Object result = qlExpressRuleEngine.execute(expression, expressContext);
            
            if (result instanceof Number) {
                Long activityBonus = ((Number) result).longValue();
                context.addActivityBonus(activityBonus);
                context.addAppliedRule("活动加成: " + activityRule.getActivityType());
            }
        }
    }
    
    private PointCalculationResult buildCalculationResult(RuleExecutionContext context) {
        return PointCalculationResult.builder()
            .basePoint(context.getBasePoint())
            .levelBonus(context.getLevelBonus())
            .activityBonus(context.getActivityBonus())
            .finalPoint(context.getFinalPoint())
            .appliedRule(context.getAppliedRule())
            .build();
    }
}
```

### 4.4 规则执行上下文
```java
@Data
public class RuleExecutionContext {
    // 请求参数
    private Long userId;
    private String businessType;
    private Long amount;
    private LocalDateTime timestamp;
    
    // 用户信息
    private Integer userLevel;
    private Double levelMultiplier;
    
    // 规则信息
    private PointRule baseRule;
    private List<ActivityRule> activeRule;
    
    // 计算结果
    private Long basePoint = 0L;
    private Long levelBonus = 0L;
    private Long activityBonus = 0L;
    private Long finalPoint = 0L;
    
    // 执行记录
    private List<String> appliedRule = new ArrayList<>();
    
    public void addAppliedRule(String ruleName) {
        this.appliedRule.add(ruleName);
    }
    
    public void addBasePoint(Long point) {
        this.basePoint += point;
    }
    
    public void addLevelBonus(Long bonus) {
        this.levelBonus += bonus;
    }
    
    public void addActivityBonus(Long bonus) {
        this.activityBonus += bonus;
    }
    
    public void calculateFinalPoint() {
        this.finalPoint = this.basePoint + this.levelBonus + this.activityBonus;
    }
}
```

### 4.5 QLExpress规则表达式示例

#### 4.5.1 QLExpress规则引擎封装
```java
@Component
@Slf4j
public class RuleExecutionEngine {
    
    private final Express4Runner express4Runner;
    
    public RuleExecutionEngine(Express4Runner express4Runner) {
        this.express4Runner = express4Runner;
    }
    
    public Object execute(String expression, DefaultContext<String, Object> context) throws Exception {
        return expressRunner.execute(expression, context, null, true, false);
    }
    
    // 构建基础积分计算表达式
    public String buildBasePointExpression(PointRule rule) {
        switch (rule.getCalculationType()) {
            case FIXED:
                return String.valueOf(rule.getFixedPoint());
            case RATIO:
                return "Math.round(amount * " + rule.getRatio() + ")";
            case LADDER:
                return "calculateLadderPoint(amount, baseRule.ladderRule)";
            default:
                return "0";
        }
    }
    
    // 构建等级加成表达式
    public String buildLevelBonusExpression(Integer userLevel) {
        StringBuilder expression = new StringBuilder();
        expression.append("if (userLevel >= 1 && userLevel < 2) { ");
        expression.append("  return Math.round(context.basePoint * 0.1); ");
        expression.append("} else if (userLevel >= 2 && userLevel < 3) { ");
        expression.append("  return Math.round(context.basePoint * 0.2); ");
        expression.append("} else if (userLevel >= 3) { ");
        expression.append("  return Math.round(context.basePoint * 0.3); ");
        expression.append("} else { ");
        expression.append("  return 0; ");
        expression.append("}");
        return expression.toString();
    }
    
    // 构建活动加成表达式
    public String buildActivityBonusExpression(ActivityRule activityRule) {
        StringBuilder expression = new StringBuilder();
        
        switch (activityRule.getActivityType()) {
            case "DOUBLE_POINTS":
                expression.append("if (checkTimeRange(timestamp, '")
                         .append(activityRule.getStartTime()).append("', '")
                         .append(activityRule.getEndTime()).append("') && ")
                         .append("businessType == '").append(activityRule.getBusinessType()).append("') { ")
                         .append("  return context.basePoint; ")
                         .append("} else { return 0; }");
                break;
                
            case "NEW_USER_BONUS":
                expression.append("if (businessType == 'FIRST_PURCHASE') { ")
                         .append("  return ").append(activityRule.getBonusPoint()).append("; ")
                         .append("} else { return 0; }");
                break;
                
            case "AMOUNT_BONUS":
                expression.append("if (amount >= 1000 && businessType == '")
                         .append(activityRule.getBusinessType()).append("') { ")
                         .append("  return Math.round(amount * ").append(activityRule.getBonusRatio()).append("); ")
                         .append("} else { return 0; }");
                break;
                
            default:
                expression.append("0");
        }
        
        return expression.toString();
    }
}
```

#### 4.5.2 自定义函数实现
```java
// 阶梯积分计算函数
public class CalculateLadderPointFunction extends Operator {
    
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list.length != 2) {
            throw new Exception("calculateLadderPoint函数需要2个参数");
        }
        
        Long amount = ((Number) list[0]).longValue();
        List<LadderRule> ladderRule = (List<LadderRule>) list[1];
        
        Long totalPoint = 0L;
        Long remainingAmount = amount;
        
        for (LadderRule ladder : ladderRule) {
            if (remainingAmount <= 0) break;
            
            Long ladderAmount = Math.min(remainingAmount, ladder.getMaxAmount() - ladder.getMinAmount());
            totalPoint += Math.round(ladderAmount * ladder.getRatio());
            remainingAmount -= ladderAmount;
        }
        
        return totalPoint;
    }
}

// 等级倍数获取函数
public class GetLevelMultiplierFunction extends Operator {
    
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list.length != 1) {
            throw new Exception("getLevelMultiplier函数需要1个参数");
        }
        
        Integer level = ((Number) list[0]).intValue();
        
        if (level >= 3) return 1.3;
        if (level >= 2) return 1.2;
        if (level >= 1) return 1.1;
        return 1.0;
    }
}

// 时间范围检查函数
public class CheckTimeRangeFunction extends Operator {
    
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list.length != 3) {
            throw new Exception("checkTimeRange函数需要3个参数");
        }
        
        LocalDateTime current = (LocalDateTime) list[0];
        LocalDateTime startTime = LocalDateTime.parse((String) list[1]);
        LocalDateTime endTime = LocalDateTime.parse((String) list[2]);
        
        return current.isAfter(startTime) && current.isBefore(endTime);
    }
}
```

#### 4.5.3 规则表达式配置示例
```java
@Component
public class PointRuleExpressionBuilder {
    
    // 基础积分规则表达式
    public static final Map<String, String> BASE_POINTS_EXPRESSIONS = Map.of(
        "PURCHASE_FIXED", "100",  // 固定100积分
        "PURCHASE_RATIO", "Math.round(amount * 0.01)",  // 按金额1%计算
        "SIGN_IN", "10",  // 签到固定10积分
        "SHARE", "5",     // 分享固定5积分
        "REVIEW", "Math.round(amount * 0.005)"  // 评价按金额0.5%计算
    );
    
    // 等级加成规则表达式
    public static final String LEVEL_BONUS_EXPRESSION = 
        "if (userLevel >= 3) { " +
        "  return Math.round(context.basePoint * 0.3); " +
        "} else if (userLevel >= 2) { " +
        "  return Math.round(context.basePoint * 0.2); " +
        "} else if (userLevel >= 1) { " +
        "  return Math.round(context.basePoint * 0.1); " +
        "} else { " +
        "  return 0; " +
        "}";
    
    // 活动加成规则表达式
    public static final Map<String, String> ACTIVITY_BONUS_EXPRESSIONS = Map.of(
        "DOUBLE_POINTS", 
        "if (checkTimeRange(timestamp, startTime, endTime) && businessType == targetBusinessType) { " +
        "  return context.basePoint; " +
        "} else { return 0; }",
        
        "NEW_USER_BONUS",
        "if (businessType == 'FIRST_PURCHASE') { " +
        "  return 500; " +
        "} else { return 0; }",
        
        "WEEKEND_BONUS",
        "if (formatDate(timestamp, 'E') in ['Sat', 'Sun']) { " +
        "  return Math.round(context.basePoint * 0.5); " +
        "} else { return 0; }",
        
        "AMOUNT_BONUS",
        "if (amount >= 1000) { " +
        "  return Math.round(amount * 0.02); " +
        "} else if (amount >= 500) { " +
        "  return Math.round(amount * 0.01); " +
        "} else { return 0; }"
    );
    
    public String buildExpression(String ruleType, Map<String, Object> params) {
        switch (ruleType) {
            case "BASE_POINTS":
                return BASE_POINTS_EXPRESSIONS.getOrDefault(
                    (String) params.get("businessType"), "0");
            case "LEVEL_BONUS":
                return LEVEL_BONUS_EXPRESSION;
            case "ACTIVITY_BONUS":
                return ACTIVITY_BONUS_EXPRESSIONS.getOrDefault(
                    (String) params.get("activityType"), "0");
            default:
                return "0";
        }
    }
}
```

### 4.6 规则管理服务
```java
@Service
@Slf4j
public class RuleManagementService {
    
    @Autowired
    private QLExpressRuleEngine qlExpressRuleEngine;
    
    @Autowired
    private PointRuleRepository ruleRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String RULE_CACHE_KEY = "point:rule:";
    private static final String EXPRESSION_CACHE_KEY = "point:expressions:";
    
    /**
     * 动态更新规则
     */
    public void updateRule(PointRule rule) {
        try {
            // 1. 保存到数据库
            ruleRepository.save(rule);
            
            // 2. 生成QLExpress表达式
            String expression = generateExpression(rule);
            
            // 3. 验证表达式语法
            validateExpression(expression);
            
            // 4. 缓存表达式
            cacheExpression(rule.getRuleName(), expression);
            
            // 5. 清除相关缓存
            clearRuleCache(rule.getBusinessType());
            
            log.info("规则更新成功: {}", rule.getRuleName());
            
        } catch (Exception e) {
            log.error("规则更新失败: {}", rule.getRuleName(), e);
            throw new RuleUpdateException("规则更新失败", e);
        }
    }
    
    /**
     * 生成QLExpress表达式
     */
    private String generateExpression(PointRule rule) {
        StringBuilder expression = new StringBuilder();
        
        switch (rule.getCalculationType()) {
            case FIXED:
                expression.append(rule.getFixedPoint());
                break;
            case RATIO:
                expression.append("Math.round(amount * ").append(rule.getRatio()).append(")");
                break;
            case LADDER:
                expression.append("calculateLadderPoint(amount, ")
                         .append("'").append(rule.getLadderConfig()).append("')");
                break;
            case CONDITIONAL:
                expression.append(buildConditionalExpression(rule));
                break;
            default:
                expression.append("0");
        }
        
        return expression.toString();
    }
    
    /**
     * 构建条件表达式
     */
    private String buildConditionalExpression(PointRule rule) {
        StringBuilder expression = new StringBuilder();
        List<RuleCondition> conditions = rule.getConditions();
        
        if (conditions != null && !conditions.isEmpty()) {
            for (int i = 0; i < conditions.size(); i++) {
                RuleCondition condition = conditions.get(i);
                
                if (i > 0) {
                    expression.append(" else ");
                }
                
                expression.append("if (").append(condition.getCondition()).append(") { ");
                expression.append("return ").append(condition.getResult()).append("; ");
                expression.append("}");
            }
            expression.append(" else { return 0; }");
        } else {
            expression.append("0");
        }
        
        return expression.toString();
    }
    
    /**
     * 验证表达式语法
     */
    private void validateExpression(String expression) throws Exception {
        DefaultContext<String, Object> testContext = new DefaultContext<>();
        testContext.put("amount", 100L);
        testContext.put("userLevel", 1);
        testContext.put("businessType", "TEST");
        testContext.put("timestamp", LocalDateTime.now());
        
        try {
            qlExpressRuleEngine.execute(expression, testContext);
        } catch (Exception e) {
            throw new RuleValidationException("表达式语法验证失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 缓存表达式
     */
    private void cacheExpression(String ruleName, String expression) {
        String cacheKey = EXPRESSION_CACHE_KEY + ruleName;
        redisTemplate.opsForValue().set(cacheKey, expression, Duration.ofHours(24));
    }
    
    /**
     * 获取缓存的表达式
     */
    public String getCachedExpression(String ruleName) {
        String cacheKey = EXPRESSION_CACHE_KEY + ruleName;
        return (String) redisTemplate.opsForValue().get(cacheKey);
    }
    
    /**
     * 批量更新规则
     */
    public BatchUpdateResult batchUpdateRule(List<PointRule> rule) {
        BatchUpdateResult result = new BatchUpdateResult();
        
        for (PointRule rule : rule) {
            try {
                updateRule(rule);
                result.addSuccess(rule.getRuleName());
            } catch (Exception e) {
                result.addFailure(rule.getRuleName(), e.getMessage());
                log.error("批量更新规则失败: {}", rule.getRuleName(), e);
            }
        }
        
        return result;
    }
    
    /**
     * 规则表达式热更新
     */
    public void hotUpdateExpression(String ruleName, String newExpression) {
        try {
            // 1. 验证新表达式
            validateExpression(newExpression);
            
            // 2. 更新缓存
            cacheExpression(ruleName, newExpression);
            
            // 3. 记录更新日志
            log.info("规则表达式热更新成功: ruleName={}, expression={}", ruleName, newExpression);
            
        } catch (Exception e) {
            log.error("规则表达式热更新失败: ruleName={}", ruleName, e);
            throw new RuleUpdateException("规则表达式热更新失败", e);
        }
    }
    
    private void clearRuleCache(String businessType) {
        String cacheKey = RULE_CACHE_KEY + businessType;
        redisTemplate.delete(cacheKey);
    }
}
```

### 4.7 规则测试工具
```java
@Component
public class RuleTestTool {
    
    @Autowired
    private PointCalculationEngine calculationEngine;
    
    @Autowired
    private QLExpressRuleEngine qlExpressRuleEngine;
    
    public RuleTestResult testRule(RuleTestRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            PointCalculationResult result = calculationEngine.calculate(
                PointCalculationRequest.builder()
                    .userId(request.getUserId())
                    .businessType(request.getBusinessType())
                    .amount(request.getAmount())
                    .build()
            );
            
            return RuleTestResult.builder()
                .success(true)
                .result(result)
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            return RuleTestResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }
    
    /**
     * 测试单个表达式
     */
    public ExpressionTestResult testExpression(ExpressionTestRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            DefaultContext<String, Object> context = new DefaultContext<>();
            context.put("amount", request.getAmount());
            context.put("userLevel", request.getUserLevel());
            context.put("businessType", request.getBusinessType());
            context.put("timestamp", LocalDateTime.now());
            
            // 添加测试数据
            if (request.getTestData() != null) {
                request.getTestData().forEach(context::put);
            }
            
            Object result = qlExpressRuleEngine.execute(request.getExpression(), context);
            
            return ExpressionTestResult.builder()
                .success(true)
                .result(result)
                .resultType(result != null ? result.getClass().getSimpleName() : "null")
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
                
        } catch (Exception e) {
            return ExpressionTestResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .executionTime(System.currentTimeMillis() - startTime)
                .build();
        }
    }
    
    /**
     * 批量测试规则
     */
    public BatchTestResult batchTestRule(List<RuleTestRequest> requests) {
        BatchTestResult batchResult = new BatchTestResult();
        
        for (RuleTestRequest request : requests) {
            RuleTestResult result = testRule(request);
            batchResult.addResult(request.getTestName(), result);
        }
        
        return batchResult;
    }
    
    /**
     * 性能压测
     */
    public PerformanceTestResult performanceTest(PerformanceTestRequest request) {
        List<Long> executionTimes = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        for (int i = 0; i < request.getIterations(); i++) {
            long startTime = System.currentTimeMillis();
            
            try {
                calculationEngine.calculate(
                    PointCalculationRequest.builder()
                        .userId(request.getUserId())
                        .businessType(request.getBusinessType())
                        .amount(request.getAmount())
                        .build()
                );
                successCount++;
            } catch (Exception e) {
                failureCount++;
            }
            
            executionTimes.add(System.currentTimeMillis() - startTime);
        }
        
        // 计算统计信息
        double avgTime = executionTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long maxTime = executionTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
        long minTime = executionTimes.stream().mapToLong(Long::longValue).min().orElse(0L);
        
        return PerformanceTestResult.builder()
            .iterations(request.getIterations())
            .successCount(successCount)
            .failureCount(failureCount)
            .avgExecutionTime(avgTime)
            .maxExecutionTime(maxTime)
            .minExecutionTime(minTime)
            .build();
    }
}
```

通过采用阿里QLExpress规则引擎，积分服务实现了：

1. **灵活性**：支持复杂的业务规则配置和动态更新，表达式语法简洁易懂
2. **可维护性**：规则与代码分离，使用类Java语法，便于业务人员理解和维护
3. **高性能**：轻量级引擎，启动快速，支持高并发执行
4. **可扩展性**：支持自定义函数和操作符，满足复杂业务需求
5. **可测试性**：提供完善的规则测试工具，确保规则正确性和性能
6. **易集成**：无需额外的规则文件，直接使用字符串表达式，集成简单

### 4.2 规则配置管理
```java
@MappedEntity("point_rule")
public class PointRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "rule_name", nullable = false)
    private String ruleName;
    
    @Column(name = "business_type", nullable = false)
    private String businessType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "calculation_type", nullable = false)
    private CalculationType calculationType;
    
    @Column(name = "fixed_point")
    private Long fixedPoint;
    
    @Column(name = "ratio")
    private BigDecimal ratio;
    
    @Column(name = "daily_limit")
    private Long dailyLimit;
    
    @Column(name = "monthly_limit")
    private Long monthlyLimit;
    
    @Column(name = "start_time")
    private LocalDateTime startTime;
    
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    @Column(name = "status")
    private RuleStatus status = RuleStatus.ACTIVE;
    
    @CreationTimestamp
    private LocalDateTime createTime;
    
    @UpdateTimestamp
    private LocalDateTime updateTime;
}
```

## 5. 缓存策略

### 5.1 缓存设计
```java
@Service
public class PointCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String ACCOUNT_CACHE_KEY = "point:account:";
    private static final String RULE_CACHE_KEY = "point:rule:";
    private static final String DAILY_LIMIT_KEY = "point:daily:";
    
    // 积分账户缓存
    @Cacheable(value = "pointAccount", key = "#userId")
    public UserPoint getAccountFromCache(Long userId) {
        return accountRepository.findByUserId(userId);
    }
    
    // 规则缓存
    @Cacheable(value = "pointRule", key = "#businessType")
    public List<PointRule> getRuleFromCache(String businessType) {
        return ruleRepository.findByBusinessTypeAndStatus(businessType, RuleStatus.ACTIVE);
    }
    
    // 每日限额检查
    public boolean checkDailyLimit(Long userId, String businessType, Long point) {
        String key = DAILY_LIMIT_KEY + userId + ":" + businessType + ":" + LocalDate.now();
        Long currentPoint = (Long) redisTemplate.opsForValue().get(key);
        
        if (currentPoint == null) {
            currentPoint = 0L;
        }
        
        PointRule rule = getRuleByBusinessType(businessType);
        if (rule.getDailyLimit() != null && currentPoint + point > rule.getDailyLimit()) {
            return false;
        }
        
        // 更新当日积分计数
        redisTemplate.opsForValue().increment(key, point);
        redisTemplate.expire(key, Duration.ofDays(1));
        
        return true;
    }
    
    // 清除账户缓存
    public void clearAccountCache(Long userId) {
        String key = ACCOUNT_CACHE_KEY + userId;
        redisTemplate.delete(key);
    }
}
```

## 6. 异常处理

### 6.1 异常定义
```java
public class PointException extends RuntimeException {
    private final String errorCode;
    
    public PointException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}

public class InsufficientPointException extends PointException {
    public InsufficientPointException(Long userId, Long requiredPoint, Long availablePoint) {
        super("INSUFFICIENT_POINTS", 
              String.format("用户%d积分不足，需要%d，可用%d", userId, requiredPoint, availablePoint));
    }
}

public class DailyLimitExceededException extends PointException {
    public DailyLimitExceededException(Long userId, String businessType, Long limit) {
        super("DAILY_LIMIT_EXCEEDED", 
              String.format("用户%d在%s业务类型下超过每日积分限额%d", userId, businessType, limit));
    }
}
```

### 6.2 全局异常处理
```java
@RestControllerAdvice
public class PointExceptionHandler {
    
    @ExceptionHandler(InsufficientPointException.class)
    public Result<Void> handleInsufficientPoint(InsufficientPointException e) {
        return Result.error(e.getErrorCode(), e.getMessage());
    }
    
    @ExceptionHandler(DailyLimitExceededException.class)
    public Result<Void> handleDailyLimitExceeded(DailyLimitExceededException e) {
        return Result.error(e.getErrorCode(), e.getMessage());
    }
    
    @ExceptionHandler(PointException.class)
    public Result<Void> handlePointException(PointException e) {
        return Result.error(e.getErrorCode(), e.getMessage());
    }
}
```

## 7. 性能优化

### 7.1 数据库优化
```sql
-- 积分账户表索引
CREATE UNIQUE INDEX uk_user_point_user_id ON user_point(user_id);
CREATE INDEX idx_user_point_update_time ON user_point(update_time);

-- 积分明细表索引
CREATE INDEX idx_point_detail_user_id_create_time ON point_detail(user_id, create_time);
CREATE INDEX idx_point_detail_transaction_id ON point_detail(transaction_id);
CREATE INDEX idx_point_detail_business ON point_detail(business_type, business_id);
CREATE INDEX idx_point_detail_status_expire ON point_detail(status, expire_time);

-- 积分冻结记录表
CREATE TABLE point_freeze_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    frozen_point BIGINT NOT NULL COMMENT '冻结积分数量',
    freeze_time DATETIME NOT NULL COMMENT '冻结时间',
    unfreeze_time DATETIME COMMENT '解冻时间',
    freeze_deadline DATETIME NOT NULL COMMENT '冻结截止时间',
    writeoff_time DATETIME COMMENT '核销时间',
    freeze_transaction_id VARCHAR(64) NOT NULL COMMENT '冻结事务ID',
    unfreeze_transaction_id VARCHAR(64) COMMENT '解冻事务ID',
    writeoff_transaction_id VARCHAR(64) COMMENT '核销事务ID',
    status VARCHAR(20) NOT NULL DEFAULT 'FROZEN' COMMENT '状态：FROZEN-冻结中，UNFROZEN-已解冻，WRITTEN_OFF-已核销，EXPIRED-已过期',
    freeze_reason VARCHAR(255) COMMENT '冻结原因',
    business_type VARCHAR(50) COMMENT '业务类型',
    business_id VARCHAR(100) COMMENT '业务ID',
    operator_id BIGINT COMMENT '操作人ID',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本号'
) COMMENT '积分冻结记录表';

-- 积分冻结记录表索引
CREATE INDEX idx_freeze_record_user_status_deadline ON point_freeze_record(user_id, status, freeze_deadline);
CREATE INDEX idx_freeze_record_status_deadline ON point_freeze_record(status, freeze_deadline);
CREATE INDEX idx_freeze_record_freeze_transaction ON point_freeze_record(freeze_transaction_id);
CREATE INDEX idx_freeze_record_business ON point_freeze_record(business_type, business_id);
CREATE INDEX idx_freeze_record_operator ON point_freeze_record(operator_id);
CREATE INDEX idx_freeze_record_create_time ON point_freeze_record(create_time);

-- 分表策略
CREATE TABLE point_detail_202501 LIKE point_detail;
CREATE TABLE point_detail_202502 LIKE point_detail;
```

### 7.2 批量处理
```java
@Service
public class PointBatchService {
    
    @Autowired
    private PointService pointService;
    
    @Async("pointTaskExecutor")
    public CompletableFuture<BatchResult> batchEarnPoint(List<EarnPointRequest> requests) {
        BatchResult result = new BatchResult();
        
        for (EarnPointRequest request : requests) {
            try {
                pointService.earnPoint(request);
                result.addSuccess(request.getUserId());
            } catch (Exception e) {
                result.addFailure(request.getUserId(), e.getMessage());
            }
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void processExpiredPoint() {
        LocalDateTime now = LocalDateTime.now();
        List<PointDetail> expiredDetail = detailRepository.findExpiredPoint(now);
        
        Map<Long, Long> userExpiredPoint = expiredDetail.stream()
            .collect(Collectors.groupingBy(
                PointDetail::getUserId,
                Collectors.summingLong(PointDetail::getPointAmount)
            ));
        
        for (Map.Entry<Long, Long> entry : userExpiredPoint.entrySet()) {
            processUserExpiredPoint(entry.getKey(), entry.getValue());
        }
    }
}
```

## 8. 监控和日志

### 8.1 业务监控
```java
@Component
public class PointMetrics {
    
    private final Counter pointEarnedCounter;
    private final Counter pointConsumedCounter;
    private final Timer pointProcessTimer;
    private final Gauge dailyActiveUsers;
    
    public PointMetrics(MeterRegistry meterRegistry) {
        this.pointEarnedCounter = Counter.builder("point.earned.total")
            .description("总积分获得数量")
            .register(meterRegistry);
            
        this.pointConsumedCounter = Counter.builder("point.consumed.total")
            .description("总积分消费数量")
            .register(meterRegistry);
            
        this.pointProcessTimer = Timer.builder("point.process.duration")
            .description("积分处理耗时")
            .register(meterRegistry);
    }
    
    public void recordPointEarned(Long point) {
        pointEarnedCounter.increment(point);
    }
    
    public void recordPointConsumed(Long point) {
        pointConsumedCounter.increment(point);
    }
    
    public void recordProcessTime(Duration duration) {
        pointProcessTimer.record(duration);
    }
}
```

### 8.2 业务日志
```java
@Slf4j
@Component
public class PointLogger {
    
    public void logPointTransaction(PointTransaction transaction) {
        log.info("积分交易: userId={}, type={}, amount={}, balance={}, transactionId={}, businessType={}", 
            transaction.getUserId(),
            transaction.getPointType(),
            transaction.getPointAmount(),
            transaction.getBalanceAfter(),
            transaction.getTransactionId(),
            transaction.getBusinessType());
    }
    
    public void logPointCalculation(PointCalculationRequest request, PointCalculationResult result) {
        log.info("积分计算: userId={}, businessType={}, amount={}, basePoint={}, levelBonus={}, activityBonus={}, finalPoint={}", 
            request.getUserId(),
            request.getBusinessType(),
            request.getAmount(),
            result.getBasePoint(),
            result.getLevelBonus(),
            result.getActivityBonus(),
            result.getFinalPoint());
    }
    
    public void logError(String operation, Exception e, Object... params) {
        log.error("积分操作失败: operation={}, params={}, error={}", 
            operation, Arrays.toString(params), e.getMessage(), e);
    }
}
```

## 9. 测试用例

### 9.1 单元测试
```java
@ExtendWith(MockitoExtension.class)
class PointServiceTest {
    
    @Mock
    private UserPointRepository accountRepository;
    
    @Mock
    private PointCalculationEngine calculationEngine;
    
    @InjectMocks
    private PointService pointService;
    
    @Test
    void earnPoint_Success() {
        // Given
        EarnPointRequest request = EarnPointRequest.builder()
            .userId(1L)
            .businessType("PURCHASE")
            .amount(100L)
            .build();
        
        UserPoint account = new UserPoint();
        account.setUserId(1L);
        account.setAvailablePoint(1000L);
        
        PointCalculationResult calculationResult = PointCalculationResult.builder()
            .basePoint(100L)
            .levelBonus(20L)
            .activityBonus(10L)
            .finalPoint(130L)
            .build();
        
        when(accountRepository.findByUserId(1L)).thenReturn(account);
        when(calculationEngine.calculate(any())).thenReturn(calculationResult);
        
        // When
        PointTransaction result = pointService.earnPoint(request);
        
        // Then
        assertNotNull(result);
        assertEquals(1L, result.getUserId());
        assertEquals(130L, result.getPointAmount());
        verify(accountRepository).save(any(UserPoint.class));
    }
    
    @Test
    void consumePoint_InsufficientBalance_ThrowsException() {
        // Given
        ConsumePointRequest request = ConsumePointRequest.builder()
            .userId(1L)
            .point(1000L)
            .businessType("REDEEM")
            .build();
        
        UserPoint account = new UserPoint();
        account.setUserId(1L);
        account.setAvailablePoint(500L);
        
        when(accountRepository.findByUserId(1L)).thenReturn(account);
        
        // When & Then
        assertThrows(InsufficientPointException.class, () -> {
            pointService.consumePoint(request);
        });
    }
}
```

## 10. 积分冻结相关实体和接口

### 10.1 积分余额详情实体
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointBalanceDetail {
    private Long userId;
    private Long totalPoint;           // 总积分
    private Long availablePoint;       // 账户可用积分
    private Long effectiveFrozenPoint; // 有效冻结积分
    private Long realAvailablePoint;   // 实际可用积分（扣减冻结后）
    private Long frozenPoint;          // 冻结中积分
    private Long unfrozenPoint;        // 已解冻积分
    private Long writtenOffPoint;      // 已核销积分
    private Long expiredFrozenPoint;   // 过期冻结积分
    
    public static PointBalanceDetail empty(Long userId) {
        return PointBalanceDetail.builder()
            .userId(userId)
            .totalPoint(0L)
            .availablePoint(0L)
            .effectiveFrozenPoint(0L)
            .realAvailablePoint(0L)
            .frozenPoint(0L)
            .unfrozenPoint(0L)
            .writtenOffPoint(0L)
            .expiredFrozenPoint(0L)
            .build();
    }
}
```

### 10.2 冻结积分请求实体
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FreezePointRequest {
    private Long userId;
    private Long frozenPoint;
    private LocalDateTime freezeDeadline;
    private String freezeReason;
    private String businessType;
    private String businessId;
    private Long operatorId;
    private String remark;
}
```

### 10.3 Repository接口
```java
@Repository
public interface PointFreezeRecordRepository extends JpaRepository<PointFreezeRecord, Long> {
    
    /**
     * 查询用户的所有冻结记录
     */
    List<PointFreezeRecord> findByUserId(Long userId);
    
    /**
     * 查询用户指定状态且未过期的冻结记录
     */
    List<PointFreezeRecord> findByUserIdAndStatusAndFreezeDeadlineAfter(
        Long userId, FreezeRecordStatus status, LocalDateTime deadline);
    
    /**
     * 查询指定状态且已过期的冻结记录
     */
    List<PointFreezeRecord> findByStatusAndFreezeDeadlineBefore(
        FreezeRecordStatus status, LocalDateTime deadline);
    
    /**
     * 根据冻结事务ID查询
     */
    Optional<PointFreezeRecord> findByFreezeTransactionId(String freezeTransactionId);
    
    /**
     * 根据业务类型和业务ID查询
     */
    List<PointFreezeRecord> findByBusinessTypeAndBusinessId(String businessType, String businessId);
    
    /**
     * 统计用户有效冻结积分
     */
    @Query("SELECT COALESCE(SUM(r.frozenPoint), 0) FROM PointFreezeRecord r " +
           "WHERE r.userId = :userId AND r.status = :status AND r.freezeDeadline > :now")
    Long sumEffectiveFrozenPoint(@Param("userId") Long userId, 
                                  @Param("status") FreezeRecordStatus status, 
                                  @Param("now") LocalDateTime now);
}
```

### 10.4 控制器接口
```java
@RestController
@RequestMapping("/api/point/freeze")
@Slf4j
public class PointFreezeController {
    
    @Autowired
    private PointFreezeService freezeService;
    
    @Autowired
    private PointBalanceService balanceService;
    
    /**
     * 冻结积分
     */
    @PostMapping("/freeze")
    public Result<PointFreezeRecord> freezePoint(@RequestBody @Valid FreezePointRequest request) {
        try {
            PointFreezeRecord result = freezeService.freezePoint(request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("冻结积分失败", e);
            return Result.error("冻结积分失败: " + e.getMessage());
        }
    }
    
    /**
     * 解冻积分
     */
    @PostMapping("/unfreeze/{freezeRecordId}")
    public Result<PointFreezeRecord> unfreezePoint(
            @PathVariable Long freezeRecordId,
            @RequestParam String unfreezeReason) {
        try {
            PointFreezeRecord result = freezeService.unfreezePoint(freezeRecordId, unfreezeReason);
            return Result.success(result);
        } catch (Exception e) {
            log.error("解冻积分失败", e);
            return Result.error("解冻积分失败: " + e.getMessage());
        }
    }
    
    /**
     * 核销积分
     */
    @PostMapping("/writeoff/{freezeRecordId}")
    public Result<PointFreezeRecord> writeOffPoint(
            @PathVariable Long freezeRecordId,
            @RequestParam String writeOffReason) {
        try {
            PointFreezeRecord result = freezeService.writeOffPoint(freezeRecordId, writeOffReason);
            return Result.success(result);
        } catch (Exception e) {
            log.error("核销积分失败", e);
            return Result.error("核销积分失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户积分余额详情
     */
    @GetMapping("/balance/{userId}")
    public Result<PointBalanceDetail> getBalanceDetail(@PathVariable Long userId) {
        try {
            PointBalanceDetail result = balanceService.getBalanceDetail(userId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询积分余额失败", e);
            return Result.error("查询积分余额失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户冻结记录
     */
    @GetMapping("/records/{userId}")
    public Result<List<PointFreezeRecord>> getFreezeRecords(@PathVariable Long userId) {
        try {
            List<PointFreezeRecord> records = freezeService.getFreezeRecords(userId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("查询冻结记录失败", e);
            return Result.error("查询冻结记录失败: " + e.getMessage());
        }
    }
}
```

## 11. 积分规则管理

### 11.1 规则管理架构概述

积分规则管理系统采用**元数据驱动 + 动态SQL生成 + 可视化配置**的架构，让业务顾问能够自主配置复杂的积分规则，无需技术人员介入。

```
┌─────────────────────────────────────────┐
│            积分规则管理系统              │
├─────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐       │
│  │可视化配置界面│  │规则验证引擎  │       │
│  └─────────────┘  └─────────────┘       │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │属性表达式引擎│  │动态SQL生成器 │       │
│  └─────────────┘  └─────────────┘       │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │规则执行引擎  │  │规则缓存管理  │       │
│  └─────────────┘  └─────────────┘       │
├─────────────────────────────────────────┤
│              规则存储层                  │
│                                         │
│  ┌─────────────┐  ┌─────────────┐       │
│  │规则元数据表  │  │表达式配置表  │       │
│  └─────────────┘  └─────────────┘       │
└─────────────────────────────────────────┘
```


```mermaid
graph TD
  静态属性对象 -->|注册| 静态属性集
  静态属性集   -->|引用| 静态表达式
  静态表达式   -->|引用| 求值表达式

  

  动态属性对象 -->|注册| 动态属性集
  动态属性集   -->|引用| 动态表达式
  动态表达式   -->|引用| 求值表达式

  条件运算符 -->|引用| 条件表达式
  求值表达式 -->|引用| 条件表达式
  求值表达式 -->|引用| 计算表达式

  条件表达式 -->|条件| 积分规则

  计算表达式 -->|动作| 积分规则
```

### 11.2 属性元数据模型

#### 11.2.1 属性表达式定义实体
```java
@MappedEntity("attribute_meta")
@Data
public class AttributeMeta {
    
    @Id
    @GeneratedValue
    private Long id;
    
    private String attributeCode;
    private String attributeName;
    private String description;
    private String dataType;
    private String dataSource;
    private String targetTable;
    private String aggregateFunction;
    private String aggregateField;
    
    @TypeDef(type = DataType.JSON)
    private String filterConditions;
    
    @TypeDef(type = DataType.JSON)
    private String timeRangeConfig;
    
    private Integer cacheTtl;
    private BigDecimal defaultValue;
    private String status;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 构造函数
    public AttributeMeta() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
        this.status = "ACTIVE";
        this.cacheTtl = 300;
        this.defaultValue = BigDecimal.ZERO;
    }
}
```

#### 11.2.2 过滤条件和时间配置
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterCondition {
    private String field;                    // 字段名
    private String operator;                 // 操作符：=, !=, >, <, IN, NOT IN, LIKE
    private Object value;                    // 值
    private String logicOperator = "AND";    // 逻辑操作符：AND, OR
    private String valueType = "STATIC";     // 值类型：STATIC, DYNAMIC, PARAM
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeRangeConfig {
    private String rangeType;                // DAYS, MONTHS, YEARS, CUSTOM
    private Integer rangeValue;              // 数值
    private String dateField;                // 时间字段名
    private String startTime;                // 自定义开始时间
    private String endTime;                  // 自定义结束时间
}
```

### 11.3 积分规则配置管理

#### 11.3.1 积分规则实体
```java
@MappedEntity("point_rule")
@Data
public class PointRule {
    
    @Id
    @GeneratedValue
    private Long id;
    private String accountCode;
    private String ruleCode;
    private String ruleName;
    private String description;
    private String ruleExpression;
    private Integer priority;
    private String status;
    private LocalDateTime effectiveTime;
    private LocalDateTime expireTime;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 构造函数
    public PointRule() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
        this.status = "ACTIVE";
        this.priority = 0;
    }
}
```

### 11.4 通用属性表达式执行器

#### 11.4.1 动态SQL生成器
```java
@Component
@Slf4j
public class DynamicSqlGenerator {
    
}
```

#### 11.4.2 通用属性执行器
```java
@Component
@Slf4j
public class AttributeExecutor {
}
```

### 11.5 QLExpress集成

#### 11.5.1 通用属性函数

#### 11.5.2 规则执行引擎
```java

```

### 11.6 可视化配置管理

#### 11.6.1 属性元数据服务
```java
@Service
@Transactional
@Slf4j
public class AttributeMetaService {
    
}
```

#### 11.6.2 积分规则配置服务
```java

```

### 11.7 前端可视化配置界面

#### 11.7.1 属性表达式配置器
```javascript
// 属性表达式配置组件
const AttributeExpressionBuilder = {
  data() {
    return {
      formData: {
        expressionCode: '',
        expressionName: '',
        description: '',
        dataSource: 'order',
        aggregateFunction: 'COUNT',
        aggregateField: 'id',
        resultType: 'INTEGER',
        baseFilters: [],
        timeConfigs: []
      },
      
      dataSourceOptions: [
        { value: 'order', label: '订单表' },
        { value: 'user', label: '用户表' },
        { value: 'product', label: '商品表' },
        { value: 'payment', label: '支付表' }
      ],
      
      aggregateFunctionOptions: [
        { value: 'COUNT', label: '计数' },
        { value: 'SUM', label: '求和' },
        { value: 'AVG', label: '平均值' },
        { value: 'MAX', label: '最大值' },
        { value: 'MIN', label: '最小值' }
      ],
      
      operatorOptions: [
        { value: '=', label: '等于' },
        { value: '!=', label: '不等于' },
        { value: '>', label: '大于' },
        { value: '<', label: '小于' },
        { value: '>=', label: '大于等于' },
        { value: '<=', label: '小于等于' },
        { value: 'IN', label: '包含' },
        { value: 'NOT IN', label: '不包含' },
        { value: 'LIKE', label: '模糊匹配' }
      ]
    }
  },
  
  methods: {
    // 添加过滤条件
    addFilter() {
      this.formData.baseFilters.push({
        field: '',
        operator: '=',
        value: '',
        logicOperator: 'AND'
      });
    },
    
    // 删除过滤条件
    removeFilter(index) {
      this.formData.baseFilters.splice(index, 1);
    },
    
    // 添加时间配置
    addTimeConfig() {
      this.formData.timeConfigs.push({
        rangeType: 'DAYS',
        rangeValue: 365,
        dateField: 'create_time'
      });
    },
    
    // 删除时间配置
    removeTimeConfig(index) {
      this.formData.timeConfigs.splice(index, 1);
    },
    
    // 保存配置
    async saveExpression() {
      try {
        const response = await this.$http.post('/api/attribute-expressions', this.formData);
        this.$message.success('属性表达式创建成功');
        this.$emit('saved', response.data);
      } catch (error) {
        this.$message.error('创建失败: ' + error.message);
      }
    },
    
    // 测试表达式
    async testExpression() {
      try {
        const testData = {
          expressionCode: this.formData.expressionCode,
          testUserId: 'test_user_123',
          testParams: {}
        };
        
        const response = await this.$http.post('/api/attribute-expressions/test', testData);
        
        if (response.data.success) {
          this.$message.success(`测试成功，结果: ${response.data.result}`);
        } else {
          this.$message.error(`测试失败: ${response.data.errorMessage}`);
        }
      } catch (error) {
        this.$message.error('测试失败: ' + error.message);
      }
    }
  }
};
```

#### 11.7.2 积分规则配置器
```javascript

```

### 11.8 配置示例

#### 11.8.1 属性表达式配置示例
```json
{
  "expressionCode": "yearly_purchase_count_exclude_refund",
  "expressionName": "一年内购买次数(排除退单)",
  "description": "统计用户一年内的有效购买次数，排除已退款和已取消的订单",
  "dataSource": "order",
  "aggregateFunction": "COUNT",
  "aggregateField": "id",
  "resultType": "INTEGER",
  "baseFilters": [
    {
      "field": "status",
      "operator": "NOT IN",
      "value": ["REFUNDED", "CANCELLED"],
      "logicOperator": "AND"
    },
    {
      "field": "pay_status",
      "operator": "=",
      "value": "PAID",
      "logicOperator": "AND"
    }
  ],
  "timeConfigs": [
    {
      "rangeType": "DAYS",
      "rangeValue": 365,
      "dateField": "create_time"
    }
  ]
}
```

#### 11.8.2 积分规则配置示例
```json
{
  "accountCode": "PROJECT_A",
  "ruleCode": "vip_qualification_rule",
  "ruleName": "VIP资格判定规则",
  "attributeCode": "yearly_purchase_count_exclude_refund",
  "operator": ">=",
  "threshold": 10,
  "additionalConditions": [
    "getAttribute(userId, 'yearly_consumption_amount') >= 5000",
    "getAttribute(userId, 'user_level') >= 2"
  ],
  "priority": 1,
  "description": "用户一年内购买次数>=10且消费金额>=5000且用户等级>=2"
}
```

### 11.9 性能优化和监控

#### 11.9.1 缓存策略
```java
@Component
public class RuleCacheManager {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String RULE_CACHE_PREFIX = "rule:";
    private static final String ATTR_CACHE_PREFIX = "attr:";
    private static final Duration DEFAULT_CACHE_DURATION = Duration.ofMinutes(30);
    
    /**
     * 缓存规则执行结果
     */
    public void cacheRuleResult(String accountCode, String ruleCode, String userId, boolean result) {
        String key = buildRuleResultKey(accountCode, ruleCode, userId);
        redisTemplate.opsForValue().set(key, result, DEFAULT_CACHE_DURATION);
    }
    
    /**
     * 获取缓存的规则结果
     */
    public Boolean getCachedRuleResult(String accountCode, String ruleCode, String userId) {
        String key = buildRuleResultKey(accountCode, ruleCode, userId);
        return (Boolean) redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 预热规则缓存
     */
    @Async
    public void warmupRuleCache(String accountCode) {
        // 预加载所有活跃规则
        List<PointRule> rule = ruleRepository.findByAccountCodeAndStatus(accountCode, "ACTIVE");
        
        for (PointRule rule : rule) {
            String cacheKey = "account_rule:" + accountCode + ":" + rule.getRuleCode();
            redisTemplate.opsForValue().set(cacheKey, rule, Duration.ofHours(1));
        }
        
        log.info("规则缓存预热完成: accountCode={}, ruleCount={}", accountCode, rule.size());
    }
    
    private String buildRuleResultKey(String accountCode, String ruleCode, String userId) {
        return RULE_CACHE_PREFIX + accountCode + ":" + ruleCode + ":" + userId;
    }
}
```

#### 11.9.2 监控指标
```java

```

## 12. 总结

积分服务作为账户体系的核心模块，通过完善的技术架构设计，实现了：

1. **高性能**：通过缓存、批量处理、异步操作等手段保证系统性能
2. **高可靠**：分布式锁、事务管理、异常处理保证数据一致性
3. **高扩展**：规则引擎、插件化设计支持业务快速迭代
4. **高可观测**：完善的监控指标和日志记录便于运维管理
5. **积分冻结管理**：新增的积分冻结功能提供了完整的冻结、解冻、核销流程，支持实时余额计算
6. **智能规则管理**：通过元数据驱动的规则配置系统，实现了业务人员自主配置复杂积分规则的能力

**积分规则管理系统的核心优势：**

- **完全配置化**：所有属性表达式都通过可视化界面配置，无需编写代码
- **动态扩展**：可以随时添加新的属性表达式类型和积分规则
- **SQL自动生成**：根据元数据自动生成高效的查询SQL
- **性能可控**：支持缓存策略和性能监控，确保系统稳定运行
- **业务友好**：提供直观的可视化配置界面，业务人员可以独立完成规则配置

该设计方案能够支撑大规模用户和高并发场景，为业务发展提供强有力的技术支撑。积分冻结功能和智能规则管理系统的加入，进一步增强了系统的灵活性和业务适应性。