﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章规则模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRule extends Extensible {    
    /**
     * 规则ID
     */
    private Long ruleId;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 规则名称
     */
    private String ruleName;    
    /**
     * 规则类型
     */
    private String ruleType;    
    /**
     * 触发条件
     */
    private String triggerCondition;    
    /**
     * 条件表达式
     */
    private String conditionExpression;    
    /**
     * 优先级
     */
    private Integer priority;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 规则描述
     */
    private String description;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;}
