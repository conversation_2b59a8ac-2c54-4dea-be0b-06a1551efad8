﻿package com.fzucxl.open.point.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 积分账户模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccount extends Extensible {    
    /**
     * 账户ID
     */
    private String accountId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 总积分
     */
    private Long totalPoint;    
    /**
     * 可用积分
     */
    private Long availablePoint;    
    /**
     * 冻结积分
     */
    private Long frozenPoint;    
    /**
     * 已过期积分
     */
    private Long expiredPoint;    
    /**
     * 账户状态
     */
    private String status;    
    /**
     * 创建时间
     */
    private String createTime;    
    /**
     * 更新时间
     */
    private String updateTime;    
    /**
     * 备注
     */
    private String remark;}
