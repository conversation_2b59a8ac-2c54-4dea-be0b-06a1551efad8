package com.fzucxl.event.service;

import com.fzucxl.common.utils.JsonUtils;
import com.fzucxl.entity.event.BusinessEvent;
import com.fzucxl.entity.event.BusinessEventField;
import com.fzucxl.entity.event.BusinessEventInstance;
import com.fzucxl.event.model.BusinessEventContext;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 业务事件上下文构建器
 * 负责将事件实例数据转换为规则引擎可用的上下文对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class BusinessEventContextBuilder {
    
    private static final Logger log = LoggerFactory.getLogger(BusinessEventContextBuilder.class);

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 从事件实例构建上下文
     */
    public BusinessEventContext buildContext(BusinessEventInstance instance, BusinessEvent event, List<BusinessEventField> fields) {
        BusinessEventContext context = new BusinessEventContext(instance.getEventCode(), instance.getUserId());
        
        // 设置基础信息
        context.setEventTime(instance.getEventTime());
        context.setSource(instance.getSource());
        
        // 解析事件数据
        if (instance.getEventData() != null) {
            try {
                Map<String, Object> eventData = JsonUtils.parseMap(instance.getEventData());
                context.addFields(convertEventData(eventData, fields));
            } catch (Exception e) {
                log.error("解析事件数据失败: eventCode={}, instanceId={}", 
                    instance.getEventCode(), instance.getId(), e);
            }
        }
        
        return context;
    }
    
    /**
     * 从原始数据构建上下文
     */
    public BusinessEventContext buildContext(String eventCode, String userId, 
                                           Map<String, Object> rawData, List<BusinessEventField> fields) {
        BusinessEventContext context = new BusinessEventContext(eventCode, userId);
        
        if (rawData != null) {
            context.addFields(convertEventData(rawData, fields));
        }
        
        return context;
    }
    
    /**
     * 转换事件数据，根据字段定义进行类型转换
     */
    private Map<String, Object> convertEventData(Map<String, Object> rawData, List<BusinessEventField> fields) {
        if (fields == null || fields.isEmpty()) {
            return rawData;
        }
        
        // 创建字段类型映射
        Map<String, String> fieldTypeMap = new java.util.HashMap<>();
        for (BusinessEventField field : fields) {
            fieldTypeMap.put(field.getFieldCode(), field.getFieldType());
        }
        
        // 转换数据类型
        Map<String, Object> convertedData = new java.util.HashMap<>();
        for (Map.Entry<String, Object> entry : rawData.entrySet()) {
            String fieldCode = entry.getKey();
            Object value = entry.getValue();
            String fieldType = fieldTypeMap.get(fieldCode);
            
            if (value == null) {
                convertedData.put(fieldCode, null);
                continue;
            }
            
            try {
                Object convertedValue = convertValue(value, fieldType);
                convertedData.put(fieldCode, convertedValue);
            } catch (Exception e) {
                log.warn("字段类型转换失败: fieldCode={}, value={}, targetType={}", 
                    fieldCode, value, fieldType, e);
                // 转换失败时保留原值
                convertedData.put(fieldCode, value);
            }
        }
        
        return convertedData;
    }
    
    /**
     * 值类型转换
     */
    private Object convertValue(Object value, String targetType) {
        if (value == null || targetType == null) {
            return value;
        }
        
        String valueStr = value.toString();
        
        switch (targetType.toUpperCase()) {
            case "STRING":
                return valueStr;
                
            case "INTEGER":
                if (value instanceof Number) {
                    return ((Number) value).intValue();
                }
                return Integer.parseInt(valueStr);
                
            case "LONG":
                if (value instanceof Number) {
                    return ((Number) value).longValue();
                }
                return Long.parseLong(valueStr);
                
            case "DOUBLE":
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                }
                return Double.parseDouble(valueStr);
                
            case "BOOLEAN":
                if (value instanceof Boolean) {
                    return value;
                }
                return Boolean.parseBoolean(valueStr);
                
            case "DATE":
                if (value instanceof LocalDate) {
                    return value;
                }
                if (value instanceof LocalDateTime) {
                    return ((LocalDateTime) value).toLocalDate();
                }
                return LocalDate.parse(valueStr, DateTimeFormatter.ISO_LOCAL_DATE);
                
            case "DATETIME":
                if (value instanceof LocalDateTime) {
                    return value;
                }
                if (value instanceof LocalDate) {
                    return ((LocalDate) value).atStartOfDay();
                }
                return LocalDateTime.parse(valueStr, formatter);
                
            case "JSON":
                if (value instanceof String) {
                    return JsonUtils.parseObject(valueStr, Object.class);
                }
                return value;
                
            default:
                return value;
        }
    }
    
    /**
     * 验证事件数据
     */
    public boolean validateEventData(Map<String, Object> eventData, List<BusinessEventField> fields) {
        if (fields == null || fields.isEmpty()) {
            return true;
        }
        
        // 分离主字段和子字段
        Map<String, BusinessEventField> mainFields = new java.util.HashMap<>();
        Map<String, BusinessEventField> subFields = new java.util.HashMap<>();
        
        for (BusinessEventField field : fields) {
            String fieldCode = field.getFieldCode();
            if (fieldCode.contains(".")) {
                // 子字段，如 orderItems.item_id
                subFields.put(fieldCode, field);
            } else {
                // 主字段
                mainFields.put(fieldCode, field);
            }
        }
        
        // 验证主字段
        for (BusinessEventField field : mainFields.values()) {
            String fieldCode = field.getFieldCode();
            
            if (field.getRequired() && !eventData.containsKey(fieldCode)) {
                log.warn("必填字段缺失: fieldCode={}", fieldCode);
                return false;
            }
            
            Object value = eventData.get(fieldCode);
            if (value != null && !isValidValue(value, field.getFieldType())) {
                log.warn("字段值类型不匹配: fieldCode={}, value={}, expectedType={}", 
                    fieldCode, value, field.getFieldType());
                return false;
            }
        }
        
        // 验证子字段（如orderItems数组中的字段）
        if (!subFields.isEmpty()) {
            if (!validateSubFields(eventData, subFields)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证子字段（处理JSON数组情况）
     */
    private boolean validateSubFields(Map<String, Object> eventData, Map<String, BusinessEventField> subFields) {
        // 按前缀分组子字段
        Map<String, List<BusinessEventField>> fieldGroups = new java.util.HashMap<>();
        
        for (BusinessEventField field : subFields.values()) {
            String fieldCode = field.getFieldCode();
            String prefix = fieldCode.substring(0, fieldCode.indexOf('.'));
            
            fieldGroups.computeIfAbsent(prefix, k -> new java.util.ArrayList<>()).add(field);
        }
        
        // 验证每个前缀组（如orderItems组）
        for (Map.Entry<String, List<BusinessEventField>> entry : fieldGroups.entrySet()) {
            String prefix = entry.getKey();
            List<BusinessEventField> groupFields = entry.getValue();
            
            Object arrayValue = eventData.get(prefix);
            if (arrayValue == null) {
                // 检查是否有必填的子字段
                boolean hasRequiredSubField = groupFields.stream()
                    .anyMatch(BusinessEventField::getRequired);
                
                if (hasRequiredSubField) {
                    log.warn("包含必填子字段的数组字段缺失: prefix={}", prefix);
                    return false;
                }
                continue;
            }
            
            // 验证数组数据
            if (!validateArrayField(arrayValue, prefix, groupFields)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证数组字段
     */
    private boolean validateArrayField(Object arrayValue, String prefix, List<BusinessEventField> groupFields) {
        List<?> arrayList;
        
        try {
            if (arrayValue instanceof List) {
                arrayList = (List<?>) arrayValue;
            } else {
                log.warn("数组字段类型不正确: prefix={}, valueType={}", prefix, arrayValue.getClass().getSimpleName());
                return false;
            }
        } catch (Exception e) {
            log.warn("解析数组字段失败: prefix={}, value={}", prefix, arrayValue, e);
            return false;
        }
        
        if (arrayList.isEmpty()) {
            log.debug("数组字段为空: prefix={}", prefix);
            return true;
        }
        
        // 验证数组中每个元素的子字段
        for (int i = 0; i < arrayList.size(); i++) {
            Object item = arrayList.get(i);
            
            if (!(item instanceof Map)) {
                log.warn("数组元素不是对象类型: prefix={}, index={}, itemType={}", 
                    prefix, i, item != null ? item.getClass().getSimpleName() : "null");
                return false;
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> itemMap = (Map<String, Object>) item;
            
            // 验证每个子字段
            for (BusinessEventField field : groupFields) {
                String fullFieldCode = field.getFieldCode();
                String subFieldCode = fullFieldCode.substring(fullFieldCode.indexOf('.') + 1);
                
                if (field.getRequired() && !itemMap.containsKey(subFieldCode)) {
                    log.warn("数组元素中必填子字段缺失: prefix={}, index={}, subField={}", 
                        prefix, i, subFieldCode);
                    return false;
                }
                
                Object subValue = itemMap.get(subFieldCode);
                if (subValue != null && !isValidValue(subValue, field.getFieldType())) {
                    log.warn("数组元素中子字段值类型不匹配: prefix={}, index={}, subField={}, value={}, expectedType={}", 
                        prefix, i, subFieldCode, subValue, field.getFieldType());
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 检查值是否符合指定类型
     */
    private boolean isValidValue(Object value, String fieldType) {
        if (value == null) {
            return true;
        }
        
        try {
            convertValue(value, fieldType);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}