﻿package com.fzucxl.open.badge.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建勋章账户结果
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAccountCreateResult extends Extensible {    
    /**
     * 账户ID
     */
    private Long accountId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 创建时间
     */
    private String createTime;}
