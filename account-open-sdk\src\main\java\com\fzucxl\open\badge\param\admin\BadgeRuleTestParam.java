﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 测试勋章规则参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRuleTestParam extends Extensible {    
    /**
     * 规则ID
     */
    private Long ruleId;    
    /**
     * 测试用户ID
     */
    private Long userId;    
    /**
     * 测试数据
     */
    private java.util.Map<String, Object> testData;}
