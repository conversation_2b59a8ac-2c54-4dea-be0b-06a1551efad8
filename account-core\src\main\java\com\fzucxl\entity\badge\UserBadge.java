package com.fzucxl.entity.badge;

import com.fzucxl.open.base.AccountStatus;
import io.micronaut.data.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@MappedEntity("user_badge")
@Data
public class UserBadge {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 勋章ID
     */
    private Long badgeId;

    /**
     * 账户代码
     */
    private String accountCode;
    /**
     * 品牌代码
     */
    private String brandCode;
    /**
     * 账户名称
     */
    private String accountName;
    /**
     * 获取时间
     */
    private LocalDateTime obtainTime;
    /**
     * 失效时间
     */
    private LocalDateTime expireTime;

    /**
     * 获取方式
     */
    private String obtainType;

    /**
     * 账户状态：ACTIVE-正常，INACTIVE-停用，FROZEN-冻结，DELETED-已删除
     */
    private AccountStatus status;

    /**
     * 获取数量
     */
    private Integer obtainCount = 1;

    /**
     * 是否显示
     */
    private Boolean isDisplayed = true;
    /**
     * 显示顺序
     */
    private Integer displayOrder;

    /**
     * 扩展数据
     */
    private String extraData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
