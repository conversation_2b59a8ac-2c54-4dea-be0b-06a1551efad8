﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询积分规则列表参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleListQueryParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 规则名称（模糊查询）
     */
    private String ruleName;    
    /**
     * 规则类型
     */
    private String ruleType;    
    /**
     * 状态
     */
    private String status;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 每页记录数
     */
    private Integer pageSize;}
