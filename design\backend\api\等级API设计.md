# 等级API设计文档

## 1. API概述

### 1.1 服务职责
等级API负责提供用户等级管理的完整接口服务，包括等级查询、等级计算、等级配置管理、等级变更记录等功能。

### 1.2 API特性
- RESTful设计风格
- 统一的响应格式
- 完善的错误处理
- 支持批量操作
- 实时等级计算
- 等级权益查询

### 1.3 接口列表

#### 1.3.1 对外服务API接口汇总

| 功能模块       | 接口名称                                      | 请求方式 | 接口路径                                      | 接口描述                          |
|------------|-------------------------------------------| -------- |-------------------------------------------|-------------------------------|
| **用户等级管理** | 查询用户等级信息                                  | GET | `/api/v1/level/user/{userId}`             | 查询指定用户的等级信息，包括当前等级、升级进度、权益等   |
|            | 批量查询用户等级                                  | POST | `/api/v1/level/user/batch`                | 批量查询多个用户的等级信息                 |
|            | 计算用户等级                                    | POST | `/api/v1/level/calculate`                 | 根据用户数据计算应有等级                  |
|            | 批量计算用户等级                                  | POST | `/api/v1/level/calculate/batch`           | 批量计算多个用户的等级                   |
|            | 用户等级升级                                    | POST | `/api/v1/level/upgrade`                   | 手动升级用户等级，支持跨级升级               |
|            | 用户等级降级                                    | POST | `/api/v1/level/downgrade`                 | 手动降级用户等级，用于违规处罚或特殊业务场景        |
| **等级权益管理** | 查询用户等级权益                                  | GET | `/api/v1/level/privileges/{userId}`       | 查询用户当前等级的所有权益信息               |
|            | 检查用户权益                                    | GET | `/api/v1/level/privileges/{userId}/check` | 检查用户是否拥有特定权益                  |
|            | 获取积分倍数                                    | GET | `/api/v1/level/multiplier/{userId}`       | 获取用户等级对应的积分倍数                 |
| **等级规则查询** | 查询等级配置列表                                  | GET | `/api/v1/level/rule`                      | 查询指定账户的所有等级配置                 |
|            | 查询单个等级配置                                  | GET | `/api/v1/level/rule/{ruleId}`             | 查询指定等级的配置信息                   |
| **等级变更记录** | 查询等级变更记录                                  | GET | `/api/v1/level/record/{userId}`            | 查询用户的等级变更历史记录                 |
|            | 查询等级变更统计                                  | GET | `/api/v1/level/record/statistic`          | 查询等级变更的统计信息                   |
| **等级事务管理** | 等级事务查询                                    | GET | `/api/v1/level/transaction` | 查询等级事务记录，支持按多种条件筛选和分页查询       |
|            | 等级事务详情查询                                  | GET | `/api/v1/level/transaction/{transactionId}` | 根据事务ID查询等级事务的详细信息|
#### 1.3.2 Web管理端API接口汇总

| 功能模块       | 接口名称      | 请求方式 | 接口路径                                         | 接口描述       |
|------------|-----------| -------- |----------------------------------------------|------------|
| **等级规则管理** | 创建等级配置    | POST | `/admin/api/v1/level/rule`                 | 创建新的等级规则   |
|            | 更新等级规则    | PUT | `/admin/api/v1/level/rule/{userId}`      | 更新指定的等级规则  |
|            | 删除等级规则    | DELETE | `/admin/api/v1/level/rule/{userId}`      | 删除指定的等级规则  |
| **等级账户管理** | 查询等级账户列表  | GET | `/admin/api/v1/level/accounts`               | 查询所有等级账户配置 |
|            | 创建/编辑等级账户 | POST | `/admin/api/v1/level/accounts`               | 创建/编辑等级账户  |
| **等级数据分析** | 等级分布统计    | GET | `/admin/api/v1/level/analytics/distribution` | 查询用户等级分布统计 |
|            | 等级流转分析    | GET | `/admin/api/v1/level/analytics/flow`         | 查询等级流转趋势分析 |

#### 1.3.3 接口特性说明

**对外服务API特性：**
- 支持高并发访问，提供完善的限流策略
- 所有查询类接口支持缓存机制，提升响应速度
- 提供实时等级计算和批量计算能力
- 支持等级权益的灵活查询和验证
- 提供完整的等级变更历史追踪

**Web管理端API特性：**
- 提供完整的等级配置CRUD操作
- 支持等级规则的在线测试和验证
- 提供丰富的数据分析和统计功能
- 支持用户等级的手动调整和批量操作
- 所有管理接口支持分页查询和条件筛选

**通用特性：**
- 统一的响应格式和错误码体系
- 完善的参数校验和异常处理
- 支持链路追踪和日志记录
- 提供详细的接口文档和示例
- 支持多租户和多账户体系

## 2. 通用规范

### 2.1 请求格式
- Content-Type: application/json
- 字符编码: UTF-8
- 请求方法: GET, POST, PUT, DELETE

### 2.2 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req_*********"
}
```

### 2.3 错误码定义
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | success | 成功 |
| 400 | 参数错误 | 请求参数不正确 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 500 | 系统内部错误 | 服务器内部错误 |
| 10001 | 用户等级不存在 | 用户等级信息不存在 |
| 10002 | 等级配置不存在 | 等级配置信息不存在 |
| 10003 | 等级计算失败 | 等级计算过程中出现错误 |
| 10004 | 等级权益查询失败 | 等级权益信息查询失败 |

## 3. 用户等级管理API

### 3.1 查询用户等级信息

**基本信息**
- **接口路径**：`GET /api/v1/level/user/{userId}`
- **接口描述**：查询指定用户的等级信息，包括当前等级、升级进度、权益等
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Path | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 否 | 账户代码 | "DEFAULT" | 长度1-32 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "DEFAULT" |
| currentLevel | Integer | 当前等级 | 3 |
| currentLevelName | String | 当前等级名称 | "黄金会员" |
| levelUpTime | String | 升级时间 | "2024-01-15T10:30:00Z" |
| effectiveTime | String | 生效时间 | "2024-01-15T10:30:00Z" |
| expireTime | String | 过期时间 | null |
| nextLevel | Integer | 下一等级 | 4 |
| nextLevelName | String | 下一等级名称 | "铂金会员" |
| progress | Double | 升级进度 | 0.65 |
| totalGrowth | Long | 总成长值 | 15000 |
| currentLevelGrowth | Long | 当前等级所需成长值 | 10000 |
| nextLevelGrowth | Long | 下一等级所需成长值 | 20000 |
| remainingGrowth | Long | 距离下一等级剩余成长值 | 5000 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "currentLevel": 3,
    "currentLevelName": "黄金会员",
    "levelUpTime": "2024-01-15T10:30:00Z",
    "effectiveTime": "2024-01-15T10:30:00Z",
    "expireTime": null,
    "nextLevel": 4,
    "nextLevelName": "铂金会员",
    "progress": 0.65,
    "totalGrowth": 15000,
    "currentLevelGrowth": 10000,
    "nextLevelGrowth": 20000,
    "remainingGrowth": 5000
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户等级不存在 | 确认用户ID是否正确 |
| 10002 | 账户代码无效 | 确认账户代码是否正确 |
| 10003 | 等级计算失败 | 联系系统管理员 |

### 3.2 批量查询用户等级信息

**基本信息**
- **接口路径**：`POST /api/v1/level/user/batch`
- **接口描述**：批量查询多个用户的等级信息
- **访问权限**：需要API认证
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userIds | Array | Body | 是 | 用户ID列表 | ["user_123456", "user_123457"] | 最多100个 |
| accountCode | String | Body | 是 | 账户代码 | "DEFAULT" | 长度1-32 |

**请求示例**

```json
{
  "userIds": ["user_123456", "user_123457", "user_123458"],
  "accountCode": "DEFAULT"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| list | Array | 用户等级信息列表 | - |
| list[].userId | String | 用户ID | "user_123456" |
| list[].currentLevel | Integer | 当前等级 | 3 |
| list[].currentLevelName | String | 当前等级名称 | "黄金会员" |
| list[].totalGrowth | Long | 总成长值 | 15000 |
| list[].effectiveTime | String | 生效时间 | "2024-01-15T10:30:00Z" |
| list[].expireTime | String | 过期时间 | null |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "userId": "user_123456",
      "currentLevel": 3,
      "currentLevelName": "黄金会员",
      "totalGrowth": 15000,
      "effectiveTime": "2024-01-15T10:30:00Z",
      "expireTime": null
    },
    {
      "userId": "user_123457",
      "currentLevel": 2,
      "currentLevelName": "白银会员",
      "totalGrowth": 8000,
      "effectiveTime": "2024-01-10T09:15:00Z",
      "expireTime": null
    }
  ]
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户列表为空 | 检查userIds参数 |
| 10002 | 用户数量超出限制 | 单次查询最多100个用户 |
| 10003 | 账户代码无效 | 确认账户代码是否正确 |

### 3.3 计算用户等级

**基本信息**
- **接口路径**：`POST /api/v1/level/calculate`
- **接口描述**：基于用户成长值重新计算等级
- **访问权限**：需要API认证
- **限流规则**：200次/分钟
- **幂等性**：支持，基于userId和accountCode

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| forceUpdate | Boolean | Body | 否 | 是否强制更新 | true | 默认false |
| transactionId | String | Body | 否 | 交易ID | "tx_789012" | 长度1-64 |
| businessId | String | Body | 否 | 业务单号 | "calc_20240120001" | 长度1-64 |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "DEFAULT",
  "forceUpdate": true,
  "transactionId": "tx_789012",
  "businessId": "calc_20240120001"
}
```

**响应参数**

| 参数名             | 类型 | 描述 | 示例 |
|-----------------| ---- | ---- | ---- |
| userId          | String | 用户ID | "user_123456" |
| accountCode     | String | 账户代码 | "DEFAULT" |
| sourceLevel     | Integer | 原等级 | 2 |
| targetLevel     | Integer | 新等级 | 3 |
| levelChanged    | Boolean | 等级是否变更 | true |
| changeType      | String | 变更类型 | "UPGRADE" |
| calculationTime | String | 计算时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "sourceLevel": 2,
    "targetLevel": 3,
    "levelChanged": true,
    "changeType": "UPGRADE",
    "calculationTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 账户代码无效 | 确认账户代码是否正确 |
| 10003 | 等级计算失败 | 检查成长值数据完整性 |
| 10004 | 等级配置不存在 | 确认等级配置是否正确 |

### 3.4 批量计算用户等级

**基本信息**
- **接口路径**：`POST /api/v1/level/calculate/batch`
- **接口描述**：批量计算多个用户的等级
- **访问权限**：需要API认证
- **限流规则**：20次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userIds | Array | Body | 是 | 用户ID列表 | ["user_123456", "user_123457"] | 最多50个 |
| accountCode | String | Body | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| forceUpdate | Boolean | Body | 否 | 是否强制更新 | true | 默认false |
| businessId | String | Body | 否 | 业务单号 | "batch_calc_20240120001" | 长度1-64 |
| transactionId | String | Body | 否 | 交易ID | "tx_789012" | 长度1-64 |

**请求示例**

```json
{
  "userIds": ["user_123456", "user_123457", "user_123458"],
  "accountCode": "DEFAULT",
  "forceUpdate": true,
  "businessId": "batch_calc_20240120001",
  "transactionId": "tx_789012"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| totalCount | Integer | 总处理数量 | 3 |
| successCount | Integer | 成功数量 | 3 |
| failureCount | Integer | 失败数量 | 0 |
| results | Array | 处理结果列表 | - |
| results[].userId | String | 用户ID | "user_123456" |
| results[].success | Boolean | 是否成功 | true |
| results[].sourceLevel | Integer | 原等级 | 2 |
| results[].targetLevel | Integer | 新等级 | 3 |
| results[].levelChanged | Boolean | 等级是否变更 | true |
| results[].changeType | String | 变更类型 | "UPGRADE" |
| results[].errorMessage | String | 错误信息 | null |
| calculationTime | String | 计算时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 3,
    "successCount": 3,
    "failureCount": 0,
    "results": [
      {
        "userId": "user_123456",
        "success": true,
        "sourceLevel": 2,
        "targetLevel": 3,
        "levelChanged": true,
        "changeType": "UPGRADE",
        "rewardPoint": 500,
        "errorMessage": null
      },
      {
        "userId": "user_123457",
        "success": true,
        "sourceLevel": 1,
        "targetLevel": 1,
        "levelChanged": false,
        "changeType": "NONE",
        "rewardPoint": 0,
        "errorMessage": null
      }
    ],
    "calculationTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户列表为空 | 检查userIds参数 |
| 10002 | 用户数量超出限制 | 单次计算最多50个用户 |
| 10003 | 账户代码无效 | 确认账户代码是否正确 |
| 10004 | 部分用户计算失败 | 检查失败用户的具体错误信息 |

### 3.5 用户等级升级

**基本信息**
- **接口路径**：`POST /api/v1/level/upgrade`
- **接口描述**：手动升级用户等级，支持跨级升级
- **访问权限**：需要管理员权限
- **限流规则**：10次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名              | 类型 | 位置 | 必填 | 描述     | 示例                     | 约束 |
|------------------| ---- | ---- | ---- |--------|------------------------| ---- |
| userId           | String | Body | 是 | 用户唯一标识 | "user_123456"          | 长度1-64 |
| accountCode      | String | Body | 是 | 账户代码   | "DEFAULT"              | 长度1-32 |
| targetLevel      | Integer | Body | 是 | 目标等级   | 4                      | 必须大于当前等级 |
| reason           | String | Body | 是 | 升级原因   | "VIP用户特殊升级"            | 长度1-200 |
| effectiveTime    | String | Body | 否 | 生效时间   | "2024-01-20T14:30:00Z" | 格式：ISO8601 |
| expireTime       | String | Body | 否 | 过期时间   | null                   | 格式：ISO8601 |
| sendNotification | Boolean | Body | 否 | 是否发送通知 | true                   | 默认true |
| sendPrivilege    | Boolean | Body | 否 | 是否发放权益 | false                  | 默认false |
| operatorId       | String | Body | 是 | 操作人ID  | "admin_001"            | 长度1-64 |
| businessId | String | Body | 是 | 业务单号   | "biz_20240115001" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789012" | 长度1-64 |
| remark           | String | Body | 否 | 备注信息   | "系统升级"                 | 长度1-500 |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "DEFAULT",
  "targetLevel": 4,
  "reason": "VIP用户特殊升级",
  "effectiveTime": "2024-01-20T14:30:00Z",
  "expireTime": null,
  "sendNotification": true,
  "operatorId": "admin_001",
  "businessId": "upgrade_20240120001",
  "remark": "系统升级"
}
```

**响应参数**

| 参数名                         | 类型 | 描述 | 示例 |
|-----------------------------| ---- | ---- | ---- |
| success                     | Boolean | 是否成功 | true |
| userId                      | String | 用户ID | "user_123456" |
| accountCode                 | String | 账户代码 | "DEFAULT" |
| fromLevel                   | Integer | 原等级 | 2 |
| toLevel                     | Integer | 目标等级 | 4 |
| changeType                  | String | 变更类型 | "UPGRADE" |
| reason                      | String | 升级原因 | "VIP用户特殊升级" |
| effectiveTime               | String | 生效时间 | "2024-01-20T14:30:00Z" |
| expireTime                  | String | 过期时间 | null |
| gainPrivileges              | Array | 新获得的权益 | - |
| gainPrivileges[].type        | String | 权益类型 | "EXCLUSIVE_SERVICE" |
| gainPrivileges[].name        | String | 权益名称 | "专属服务" |
| gainPrivileges[].description | String | 权益描述 | "享受专属客户经理服务" |
| operationTime               | String | 操作时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "fromLevel": 2,
    "toLevel": 4,
    "changeType": "UPGRADE",
    "reason": "VIP用户特殊升级",
    "effectiveTime": "2024-01-20T14:30:00Z",
    "expireTime": null,
    "gainPrivileges": [
      {
        "type": "EXCLUSIVE_SERVICE",
        "name": "专属服务",
        "description": "享受专属客户经理服务"
      },
      {
        "type": "ANNUAL_GIFT",
        "name": "年度礼品",
        "description": "每年享受价值500元礼品"
      }
    ],
    "changeRecordId": "change_001",
    "operationTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 目标等级无效 | 确认目标等级配置是否存在 |
| 10003 | 目标等级不能低于当前等级 | 使用降级接口进行降级操作 |
| 10004 | 业务单号重复 | 使用新的业务单号 |
| 10005 | 操作权限不足 | 确认操作人是否有管理员权限 |

### 3.6 用户等级降级

**基本信息**
- **接口路径**：`POST /api/v1/level/downgrade`
- **接口描述**：手动降级用户等级，用于违规处罚或特殊业务场景
- **访问权限**：需要管理员权限
- **限流规则**：5次/分钟
- **幂等性**：支持，基于businessId参数

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Body | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Body | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| targetLevel | Integer | Body | 是 | 目标等级 | 1 | 必须小于当前等级 |
| reason | String | Body | 是 | 降级原因 | "违规行为处罚" | 长度1-200 |
| effectiveTime | String | Body | 否 | 生效时间 | "2024-01-20T14:30:00Z" | 格式：ISO8601 |
| expireTime | String | Body | 否 | 过期时间 | "2024-03-20T14:30:00Z" | 格式：ISO8601 |
| penaltyType | String | Body | 是 | 处罚类型 | "TEMPORARY" | TEMPORARY/PERMANENT |
| sendNotification | Boolean | Body | 否 | 是否发送通知 | true | 默认true |
| operatorId | String | Body | 是 | 操作人ID | "admin_002" | 长度1-64 |
| businessId | String | Body | 是 | 业务单号   | "biz_20240115001" | 长度1-64，唯一 |
| transactionId | String | Body | 否 | 交易ID   | "tx_789012" | 长度1-64 |
| remark | String | Body | 否 | 备注信息 | "用户存在刷单行为，临时降级处理" | 长度1-500 |

**请求示例**

```json
{
  "userId": "user_123456",
  "accountCode": "DEFAULT",
  "targetLevel": 1,
  "reason": "违规行为处罚",
  "effectiveTime": "2024-01-20T14:30:00Z",
  "expireTime": "2024-03-20T14:30:00Z",
  "penaltyType": "TEMPORARY",
  "sendNotification": true,
  "operatorId": "admin_002",
  "businessId": "downgrade_20240120001",
  "remark": "用户存在刷单行为，临时降级处理"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| success | Boolean | 是否成功 | true |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "DEFAULT" |
| fromLevel | Integer | 原等级 | 3 |
| toLevel | Integer | 目标等级 | 1 |
| changeType | String | 变更类型 | "DOWNGRADE" |
| reason | String | 降级原因 | "违规行为处罚" |
| effectiveTime | String | 生效时间 | "2024-01-20T14:30:00Z" |
| expireTime | String | 过期时间 | "2024-03-20T14:30:00Z" |
| penaltyType | String | 处罚类型 | "TEMPORARY" |
| lostPrivileges | Array | 失去的权益 | - |
| lostPrivileges[].type | String | 权益类型 | "VIP_SERVICE" |
| lostPrivileges[].name | String | 权益名称 | "VIP服务" |
| lostPrivileges[].description | String | 权益描述 | "享受VIP专属服务" |
| recoveryTime | String | 恢复时间 | "2024-03-20T14:30:00Z" |
| changeRecordId | String | 变更记录ID | "change_002" |
| operationTime | String | 操作时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "fromLevel": 3,
    "toLevel": 1,
    "changeType": "DOWNGRADE",
    "reason": "违规行为处罚",
    "effectiveTime": "2024-01-20T14:30:00Z",
    "expireTime": "2024-03-20T14:30:00Z",
    "penaltyType": "TEMPORARY",
    "lostPrivileges": [
      {
        "type": "VIP_SERVICE",
        "name": "VIP服务",
        "description": "享受VIP专属服务"
      },
      {
        "type": "BIRTHDAY_GIFT",
        "name": "生日礼品",
        "description": "生日当月享受专属礼品"
      }
    ],
    "recoveryTime": "2024-03-20T14:30:00Z",
    "changeRecordId": "change_002",
    "operationTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 目标等级无效 | 确认目标等级配置是否存在 |
| 10003 | 目标等级不能高于当前等级 | 使用升级接口进行升级操作 |
| 10004 | 业务单号重复 | 使用新的业务单号 |
| 10005 | 操作权限不足 | 确认操作人是否有管理员权限 |
| 10006 | 临时降级必须设置过期时间 | 临时处罚类型需要设置expireTime |

## 4. 等级权益管理API

### 4.1 查询用户等级权益

**基本信息**
- **接口路径**：`GET /api/v1/level/privileges/{userId}`
- **接口描述**：查询用户当前等级的所有权益
- **访问权限**：需要API认证
- **限流规则**：500次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Path | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 否 | 账户代码 | "DEFAULT" | 长度1-32 |
| privilegeType | String | Query | 否 | 权益类型过滤 | "POINT_MULTIPLIER" | 可选值见权益类型枚举 |

**请求示例**

```
GET /api/level/privileges/user_123456?accountCode=DEFAULT&privilegeType=POINT_MULTIPLIER
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "DEFAULT" |
| currentLevel | Integer | 当前等级 | 3 |
| levelName | String | 等级名称 | "黄金会员" |
| privileges | Array | 权益列表 | - |
| privileges[].type | String | 权益类型 | "POINT_MULTIPLIER" |
| privileges[].name | String | 权益名称 | "积分倍数" |
| privileges[].value | String | 权益值 | "1.2" |
| privileges[].description | String | 权益描述 | "购物积分1.2倍" |
| privileges[].status | String | 权益状态 | "ACTIVE" |
| privileges[].effectiveTime | String | 生效时间 | "2024-01-15T10:30:00Z" |
| privileges[].expireTime | String | 过期时间 | null |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "currentLevel": 3,
    "levelName": "黄金会员",
    "privileges": [
      {
        "type": "POINT_MULTIPLIER",
        "name": "积分倍数",
        "value": "1.2",
        "description": "购物积分1.2倍",
        "status": "ACTIVE",
        "effectiveTime": "2024-01-15T10:30:00Z",
        "expireTime": null
      },
      {
        "type": "DISCOUNT_RATE",
        "name": "专属折扣",
        "value": "0.95",
        "description": "享受95折优惠",
        "status": "ACTIVE",
        "effectiveTime": "2024-01-15T10:30:00Z",
        "expireTime": null
      },
      {
        "type": "FREE_SHIPPING",
        "name": "免邮门槛",
        "value": "99",
        "description": "满99元免邮",
        "status": "ACTIVE",
        "effectiveTime": "2024-01-15T10:30:00Z",
        "expireTime": null
      }
    ]
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 用户等级不存在 | 确认用户是否已初始化等级 |
| 10003 | 账户代码无效 | 确认账户代码是否正确 |
| 10004 | 权益配置不存在 | 确认等级权益配置是否正确 |

### 4.2 检查用户权益

**基本信息**
- **接口路径**：`GET /api/v1/level/privileges/{userId}/check`
- **接口描述**：检查用户是否拥有特定权益
- **访问权限**：需要API认证
- **限流规则**：1000次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Path | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| privilegeType | String | Query | 是 | 权益类型 | "POINT_MULTIPLIER" | 见权益类型枚举 |
| accountCode | String | Query | 否 | 账户代码 | "DEFAULT" | 长度1-32 |
| checkTime | String | Query | 否 | 检查时间点 | "2024-01-20T14:30:00Z" | 格式：ISO8601 |

**请求示例**

```
GET /api/level/privileges/user_123456/check?privilegeType=POINT_MULTIPLIER&accountCode=DEFAULT
```

**响应参数**

| 参数名            | 类型 | 描述 | 示例 |
|----------------| ---- | ---- | ---- |
| userId         | String | 用户ID | "user_123456" |
| accountCode    | String | 账户代码 | "DEFAULT" |
| privilegeType  | String | 权益类型 | "POINT_MULTIPLIER" |
| hasPrivilege   | Boolean | 是否拥有权益 | true |
| privilegeValue | String | 权益值 | "1.2" |
| privilegeName  | String | 权益名称 | "积分倍数" |
| level          | Integer | 当前等级 | 3 |
| levelName      | String | 等级名称 | "黄金会员" |
| effectiveTime  | String | 权益生效时间 | "2024-01-15T10:30:00Z" |
| expireTime     | String | 权益过期时间 | null |
| checkTime      | String | 检查时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "privilegeType": "POINT_MULTIPLIER",
    "hasPrivilege": true,
    "privilegeValue": "1.2",
    "privilegeName": "积分倍数",
    "level": 3,
    "levelName": "黄金会员",
    "effectiveTime": "2024-01-15T10:30:00Z",
    "expireTime": null,
    "checkTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 权益类型无效 | 确认权益类型是否在枚举范围内 |
| 10003 | 用户等级不存在 | 确认用户是否已初始化等级 |
| 10004 | 账户代码无效 | 确认账户代码是否正确 |

### 4.3 获取积分倍数

**基本信息**
- **接口路径**：`GET /api/v1/level/multiplier/{userId}`
- **接口描述**：获取用户当前等级的积分倍数和相关权益值
- **访问权限**：需要API认证
- **限流规则**：1000次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Path | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 否 | 账户代码 | "DEFAULT" | 长度1-32 |
| businessType | String | Query | 否 | 业务类型 | "PURCHASE" | 用于获取特定业务的倍数 |

**请求示例**

```
GET /api/level/multiplier/user_123456?accountCode=DEFAULT&businessType=PURCHASE
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "DEFAULT" |
| level | Integer | 当前等级 | 3 |
| levelName | String | 等级名称 | "黄金会员" |
| pointMultiplier | BigDecimal | 积分倍数 | 1.2 |
| discountRate | BigDecimal | 折扣率 | 0.95 |
| freeShippingThreshold | BigDecimal | 免邮门槛 | 99.00 |
| businessMultipliers | Object | 业务类型倍数 | - |
| businessMultipliers.PURCHASE | BigDecimal | 购物积分倍数 | 1.2 |
| businessMultipliers.REVIEW | BigDecimal | 评价积分倍数 | 1.5 |
| businessMultipliers.SHARE | BigDecimal | 分享积分倍数 | 2.0 |
| effectiveTime | String | 生效时间 | "2024-01-15T10:30:00Z" |
| expireTime | String | 过期时间 | null |
| queryTime | String | 查询时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "user_123456",
    "accountCode": "DEFAULT",
    "currentLevel": 3,
    "levelName": "黄金会员",
    "pointMultiplier": 1.2,
    "discountRate": 0.95,
    "freeShippingThreshold": 99.00,
    "businessMultipliers": {
      "PURCHASE": 1.2,
      "REVIEW": 1.5,
      "SHARE": 2.0
    },
    "effectiveTime": "2024-01-15T10:30:00Z",
    "expireTime": null,
    "queryTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 用户等级不存在 | 确认用户是否已初始化等级 |
| 10003 | 账户代码无效 | 确认账户代码是否正确 |
| 10004 | 等级配置不存在 | 确认等级配置是否正确 |
| 10005 | 业务类型无效 | 确认业务类型是否在枚举范围内 |

## 5. 等级规则管理API

### 5.1 查询等级规则列表

**基本信息**
- **接口路径**：`GET /admin/api/v1/level/rule`
- **接口描述**：查询指定账户的所有等级规则
- **访问权限**：需要管理员权限
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Query | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| status | String | Query | 否 | 配置状态 | "ACTIVE" | ACTIVE/INACTIVE |
| page | Integer | Query | 否 | 页码 | 1 | 最小值1 |
| size | Integer | Query | 否 | 每页大小 | 20 | 1-100 |

**请求示例**

```
GET /api/level/config?accountCode=DEFAULT&status=ACTIVE&page=1&size=20
```

**响应参数**

| 参数名 | 类型 | 描述     | 示例 |
| ------ | ---- |--------| ---- |
| total | Long | 总记录数   | 4 |
| page | Integer | 当前页码   | 1 |
| size | Integer | 每页大小   | 20 |
| list | Array | 等级规则列表 | - |
| list[].id | Long | 配置ID   | 1 |
| list[].accountCode | String | 账户代码   | "DEFAULT" |
| list[].level | Integer | 等级值    | 1 |
| list[].levelName | String | 等级名称   | "青铜会员" |
| list[].levelIcon | String | 等级图标   | "bronze.png" |
| list[].description | String | 等级描述   | "新用户等级" |
| list[].pointMultiplier | BigDecimal | 积分倍数   | 1.0 |
| list[].discountRate | BigDecimal | 折扣率    | 1.0 |
| list[].freeShippingThreshold | BigDecimal | 免邮门槛   | 199.00 |
| list[].upgradeRewardPoint | Long | 升级奖励积分 | 0 |
| list[].privileges | Array | 权益列表   | - |
| list[].privileges[].type | String | 权益类型   | "BASIC_SERVICE" |
| list[].privileges[].name | String | 权益名称   | "基础服务" |
| list[].privileges[].description | String | 权益描述   | "享受基础客服服务" |
| list[].status | String | 配置状态   | "ACTIVE" |
| list[].createTime | String | 创建时间   | "2024-01-01T00:00:00Z" |
| list[].updateTime | String | 更新时间   | "2024-01-15T10:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 4,
    "page": 1,
    "size": 20,
    "list": [
      {
        "id": 1,
        "accountCode": "DEFAULT",
        "level": 1,
        "levelName": "青铜会员",
        "levelIcon": "bronze.png",
        "description": "新用户等级",
        "pointMultiplier": 1.0,
        "discountRate": 1.0,
        "freeShippingThreshold": 199.00,
        "upgradeRewardPoint": 0,
        "privileges": [
          {
            "type": "BASIC_SERVICE",
            "name": "基础服务",
            "description": "享受基础客服服务"
          }
        ],
        "status": "ACTIVE",
        "createTime": "2024-01-01T00:00:00Z",
        "updateTime": "2024-01-15T10:30:00Z"
      },
      {
        "id": 2,
        "accountCode": "DEFAULT",
        "level": 2,
        "levelName": "白银会员",
        "levelIcon": "silver.png",
        "description": "进阶用户等级",
        "pointMultiplier": 1.1,
        "discountRate": 0.98,
        "freeShippingThreshold": 149.00,
        "upgradeRewardPoint": 200,
        "privileges": [
          {
            "type": "PRIORITY_SERVICE",
            "name": "优先服务",
            "description": "享受优先客服服务"
          }
        ],
        "status": "ACTIVE",
        "createTime": "2024-01-01T00:00:00Z",
        "updateTime": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 账户代码无效 | 确认账户代码是否正确 |
| 10002 | 状态参数无效 | 确认状态值是否在枚举范围内 |
| 10003 | 分页参数无效 | 确认页码和每页大小是否在有效范围内 |
| 10004 | 权限不足 | 确认是否有管理员权限 |

### 5.2 查询单个等级规则

**基本信息**
- **接口路径**：`GET /api/v1/level/rule/{ruleId}`
- **接口描述**：查询指定等级规则信息
- **访问权限**：需要管理员权限
- **限流规则**：200次/分钟

**请求参数**

| 参数名         | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
|-------------| ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Query | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| ruleId      | Long | Path | 是 | 等级配置ID | 3 | 必须为正整数 |

**请求示例**

```
GET /api/v1/level/rule/3
```

**响应参数**

| 参数名 | 类型 | 描述     | 示例 |
| ------ | ---- |--------| ---- |
| id | Long | 规则ID   | 3 |
| accountCode | String | 账户代码   | "DEFAULT" |
| level | Integer | 等级值    | 3 |
| levelName | String | 等级名称   | "黄金会员" |
| levelIcon | String | 等级图标   | "gold.png" |
| description | String | 等级描述   | "高级用户等级" |
| pointMultiplier | BigDecimal | 积分倍数   | 1.2 |
| discountRate | BigDecimal | 折扣率    | 0.95 |
| freeShippingThreshold | BigDecimal | 免邮门槛   | 99.00 |
| upgradeRewardPoint | Long | 升级奖励积分 | 500 |
| upgradeRule | String | 升级规则   | "totalGrowth >= 10000" |
| recalculateRule | String | 重算规则   | "monthly" |
| privileges | Array | 权益列表   | - |
| privileges[].type | String | 权益类型   | "VIP_SERVICE" |
| privileges[].name | String | 权益名称   | "VIP服务" |
| privileges[].description | String | 权益描述   | "享受VIP专属服务" |
| privileges[].value | String | 权益值    | "1" |
| status | String | 配置状态   | "ACTIVE" |
| createTime | String | 创建时间   | "2024-01-01T00:00:00Z" |
| updateTime | String | 更新时间   | "2024-01-15T10:30:00Z" |
| creatorId | String | 创建人ID  | "admin_001" |
| updaterId | String | 更新人ID  | "admin_002" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 3,
    "accountCode": "DEFAULT",
    "level": 3,
    "levelName": "黄金会员",
    "levelIcon": "gold.png",
    "description": "高级用户等级",
    "pointMultiplier": 1.2,
    "discountRate": 0.95,
    "freeShippingThreshold": 99.00,
    "upgradeRewardPoint": 500,
    "upgradeRule": "totalGrowth >= 10000",
    "recalculateRule": "monthly",
    "privileges": [
      {
        "type": "VIP_SERVICE",
        "name": "VIP服务",
        "description": "享受VIP专属服务",
        "value": "1"
      },
      {
        "type": "BIRTHDAY_GIFT",
        "name": "生日礼品",
        "description": "生日当月享受专属礼品",
        "value": "1"
      }
    ],
    "status": "ACTIVE",
    "createTime": "2024-01-01T00:00:00Z",
    "updateTime": "2024-01-15T10:30:00Z",
    "creatorId": "admin_001",
    "updaterId": "admin_002"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案         |
| ------ | ---- |--------------|
| 10001 | 配置不存在 | 确认规则ID是否正确   |
| 10002 | 配置ID无效 | 确认规则ID格式是否正确 |
| 10003 | 权限不足 | 确认是否有管理员权限   |
| 10004 | 配置已删除 | 该配置已被删除，无法查询 |

### 5.3 创建等级规则

**基本信息**
- **接口路径**：`POST /api/v1/level/rule`
- **接口描述**：创建新的等级规则
- **访问权限**：需要管理员权限
- **限流规则**：20次/分钟
- **幂等性**：不支持，每次调用都会创建新规则

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Body | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| level | Integer | Body | 是 | 等级值 | 4 | 1-99，同账户下唯一 |
| levelName | String | Body | 是 | 等级名称 | "铂金会员" | 长度1-50 |
| levelIcon | String | Body | 否 | 等级图标 | "platinum.png" | 长度1-200 |
| description | String | Body | 否 | 等级描述 | "顶级用户等级" | 长度1-500 |
| pointMultiplier | BigDecimal | Body | 否 | 积分倍数 | 1.5 | 0.1-10.0 |
| discountRate | BigDecimal | Body | 否 | 折扣率 | 0.90 | 0.1-1.0 |
| freeShippingThreshold | BigDecimal | Body | 否 | 免邮门槛 | 0.00 | ≥0 |
| upgradeRewardPoint | Long | Body | 否 | 升级奖励积分 | 1000 | ≥0 |
| upgradeRule | String | Body | 否 | 升级规则 | "totalGrowth >= 20000" | 长度1-1000 |
| recalculateRule | String | Body | 否 | 重算规则 | "monthly" | daily/weekly/monthly |
| privileges | Array | Body | 否 | 权益列表 | - | 最多20个 |
| privileges[].type | String | Body | 是 | 权益类型 | "EXCLUSIVE_SERVICE" | 见权益类型枚举 |
| privileges[].name | String | Body | 是 | 权益名称 | "专属服务" | 长度1-50 |
| privileges[].description | String | Body | 否 | 权益描述 | "享受专属客户经理服务" | 长度1-200 |
| privileges[].value | String | Body | 否 | 权益值 | "1" | 长度1-100 |

**请求示例**

```json
{
  "accountCode": "DEFAULT",
  "level": 4,
  "levelName": "铂金会员",
  "levelIcon": "platinum.png",
  "description": "顶级用户等级",
  "requiredGrowth": 20000,
  "pointMultiplier": 1.5,
  "discountRate": 0.90,
  "freeShippingThreshold": 0.00,
  "upgradeRewardPoint": 1000,
  "upgradeRule": "totalGrowth >= 20000",
  "recalculateRule": "monthly",
  "privileges": [
    {
      "type": "EXCLUSIVE_SERVICE",
      "name": "专属服务",
      "description": "享受专属客户经理服务",
      "value": "1"
    },
    {
      "type": "ANNUAL_GIFT",
      "name": "年度礼品",
      "description": "每年享受价值500元礼品",
      "value": "500"
    }
  ]
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| id | Long | 配置ID | 4 |
| accountCode | String | 账户代码 | "DEFAULT" |
| level | Integer | 等级值 | 4 |
| levelName | String | 等级名称 | "铂金会员" |
| status | String | 配置状态 | "ACTIVE" |
| createTime | String | 创建时间 | "2024-01-20T14:30:00Z" |
| creatorId | String | 创建人ID | "admin_001" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 4,
    "accountCode": "DEFAULT",
    "level": 4,
    "levelName": "铂金会员",
    "status": "ACTIVE",
    "createTime": "2024-01-20T14:30:00Z",
    "creatorId": "admin_001"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 账户代码无效 | 确认账户代码是否正确 |
| 10002 | 等级值重复 | 同账户下等级值必须唯一 |
| 10003 | 等级名称重复 | 同账户下等级名称必须唯一 |
| 10004 | 参数验证失败 | 检查必填参数和格式约束 |
| 10005 | 权益类型无效 | 确认权益类型是否在枚举范围内 |
| 10006 | 权限不足 | 确认是否有管理员权限 |

### 5.4 更新等级规则

**基本信息**
- **接口路径**：`PUT /admin/api/v1/level/rule/{ruleId}`
- **接口描述**：更新指定的等级规则
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟
- **幂等性**：支持，相同参数多次调用结果一致

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| ruleId | Long | Path | 是 | 等级规则ID | 4 | 必须为正整数 |
| levelName | String | Body | 否 | 等级名称 | "铂金会员Plus" | 长度1-50 |
| levelIcon | String | Body | 否 | 等级图标 | "platinum_plus.png" | 长度1-200 |
| description | String | Body | 否 | 等级描述 | "顶级用户等级升级版" | 长度1-500 |
| pointMultiplier | BigDecimal | Body | 否 | 积分倍数 | 1.6 | 0.1-10.0 |
| discountRate | BigDecimal | Body | 否 | 折扣率 | 0.88 | 0.1-1.0 |
| freeShippingThreshold | BigDecimal | Body | 否 | 免邮门槛 | 0.00 | ≥0 |
| upgradeRewardPoint | Long | Body | 否 | 升级奖励积分 | 1200 | ≥0 |
| upgradeRule | String | Body | 否 | 升级规则 | "totalGrowth >= 25000" | 长度1-1000 |
| recalculateRule | String | Body | 否 | 重算规则 | "monthly" | daily/weekly/monthly |
| privileges | Array | Body | 否 | 权益列表 | - | 最多20个 |
| privileges[].type | String | Body | 是 | 权益类型 | "EXCLUSIVE_SERVICE" | 见权益类型枚举 |
| privileges[].name | String | Body | 是 | 权益名称 | "专属服务" | 长度1-50 |
| privileges[].description | String | Body | 否 | 权益描述 | "享受专属客户经理服务" | 长度1-200 |
| privileges[].value | String | Body | 否 | 权益值 | "1" | 长度1-100 |
| status | String | Body | 否 | 配置状态 | "ACTIVE" | ACTIVE/INACTIVE |

**请求示例**

```json
{
  "levelName": "铂金会员Plus",
  "levelIcon": "platinum_plus.png",
  "description": "顶级用户等级升级版",
  "pointMultiplier": 1.6,
  "discountRate": 0.88,
  "upgradeRewardPoint": 1200,
  "upgradeRule": "totalGrowth >= 25000",
  "privileges": [
    {
      "type": "EXCLUSIVE_SERVICE",
      "name": "专属服务Plus",
      "description": "享受专属客户经理服务和优先处理",
      "value": "1"
    },
    {
      "type": "ANNUAL_GIFT",
      "name": "年度礼品Plus",
      "description": "每年享受价值800元礼品",
      "value": "800"
    }
  ],
  "status": "ACTIVE"
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| id | Long | 配置ID | 4 |
| accountCode | String | 账户代码 | "DEFAULT" |
| level | Integer | 等级值 | 4 |
| levelName | String | 等级名称 | "铂金会员Plus" |
| levelIcon | String | 等级图标 | "platinum_plus.png" |
| description | String | 等级描述 | "顶级用户等级升级版" |
| pointMultiplier | BigDecimal | 积分倍数 | 1.6 |
| discountRate | BigDecimal | 折扣率 | 0.88 |
| freeShippingThreshold | BigDecimal | 免邮门槛 | 0.00 |
| upgradeRewardPoint | Long | 升级奖励积分 | 1200 |
| status | String | 配置状态 | "ACTIVE" |
| updateTime | String | 更新时间 | "2024-01-20T15:00:00Z" |
| updaterId | String | 更新人ID | "admin_002" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 4,
    "accountCode": "DEFAULT",
    "level": 4,
    "levelName": "铂金会员Plus",
    "levelIcon": "platinum_plus.png",
    "description": "顶级用户等级升级版",
    "requiredGrowth": 25000,
    "pointMultiplier": 1.6,
    "discountRate": 0.88,
    "freeShippingThreshold": 0.00,
    "upgradeRewardPoint": 1200,
    "status": "ACTIVE",
    "updateTime": "2024-01-20T15:00:00Z",
    "updaterId": "admin_002"
  }
}
```

**错误码**

| 错误码 | 描述     | 解决方案           |
| ------ |--------|----------------|
| 10001 | 规则不存在  | 确认规则ID是否正确     |
| 10002 | 等级名称重复 | 同账户下等级名称必须唯一   |
| 10003 | 参数验证失败 | 检查参数格式和约束条件    |
| 10004 | 权益类型无效 | 确认权益类型是否在枚举范围内 |
| 10005 | 配置已删除  | 已删除的配置无法更新     |
| 10006 | 权限不足   | 确认是否有管理员权限     |

### 5.5 删除等级配置

**基本信息**
- **接口路径**：`DELETE /admin/api/v1/level/rule/{ruleId}`
- **接口描述**：删除指定的等级配置（软删除）
- **访问权限**：需要管理员权限
- **限流规则**：10次/分钟
- **幂等性**：支持，重复删除同一配置返回相同结果

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述     | 示例 | 约束 |
| ------ | ---- | ---- | ---- |--------| ---- | ---- |
| accountCode | String | Query | 是 | 账户代码   | "DEFAULT" | 长度1-32 |
| ruleId | Long | Path | 是 | 等级规则ID | 4 | 必须为正整数 |
| force | Boolean | Query | 否 | 是否强制删除 | false | 默认false，软删除 |

**请求示例**

```
DELETE /api/level/rule/4?force=false
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| id | Long | 配置ID | 4 |
| accountCode | String | 账户代码 | "DEFAULT" |
| level | Integer | 等级值 | 4 |
| levelName | String | 等级名称 | "铂金会员Plus" |
| deleted | Boolean | 是否已删除 | true |
| deleteType | String | 删除类型 | "SOFT" |
| deleteTime | String | 删除时间 | "2024-01-20T15:30:00Z" |
| affectedUsers | Long | 受影响用户数 | 156 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 4,
    "accountCode": "DEFAULT",
    "level": 4,
    "levelName": "铂金会员Plus",
    "deleted": true,
    "deleteType": "SOFT",
    "deleteTime": "2024-01-20T15:30:00Z",
    "deleterId": "admin_003",
    "affectedUsers": 156
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 配置不存在 | 确认配置ID是否正确 |
| 10002 | 配置已删除 | 该配置已被删除 |
| 10003 | 配置正在使用中 | 存在用户使用该等级，无法删除 |
| 10004 | 权限不足 | 确认是否有管理员权限 |
| 10005 | 系统默认等级无法删除 | 系统默认等级配置不允许删除 |

## 6. 等级变更记录API

### 6.1 查询用户等级变更记录

**基本信息**
- **接口路径**：`GET /api/v1/level/record/{userId}`
- **接口描述**：查询用户的等级变更历史记录
- **访问权限**：需要API认证
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Path | 是 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 否 | 账户代码 | "DEFAULT" | 长度1-32 |
| changeType | String | Query | 否 | 变更类型 | "UPGRADE" | UPGRADE/DOWNGRADE/RESET |
| page | Integer | Query | 否 | 页码 | 1 | 最小值1 |
| size | Integer | Query | 否 | 每页大小 | 20 | 1-100 |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01T00:00:00Z" | 格式：ISO8601 |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31T23:59:59Z" | 格式：ISO8601 |

**请求示例**

```
GET /api/level/record/user_123456?accountCode=DEFAULT&changeType=UPGRADE&page=1&size=20&startTime=2024-01-01T00:00:00Z&endTime=2024-01-31T23:59:59Z
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| total | Long | 总记录数 | 5 |
| page | Integer | 当前页码 | 1 |
| size | Integer | 每页大小 | 20 |
| records | Array | 变更记录列表 | - |
| records[].id | Long | 记录ID | 101 |
| records[].userId | String | 用户ID | "user_123456" |
| records[].accountCode | String | 账户代码 | "DEFAULT" |
| records[].fromLevel | Integer | 原等级 | 2 |
| records[].toLevel | Integer | 目标等级 | 3 |
| records[].fromLevelName | String | 原等级名称 | "白银会员" |
| records[].toLevelName | String | 目标等级名称 | "黄金会员" |
| records[].changeType | String | 变更类型 | "UPGRADE" |
| records[].changeReason | String | 变更原因 | "成长值达到升级条件" |
| records[].growthValue | Long | 成长值 | 15000 |
| records[].rewardPoint | Long | 奖励积分 | 500 |
| records[].effectiveTime | String | 生效时间 | "2024-01-15T10:30:00Z" |
| records[].expireTime | String | 过期时间 | null |
| records[].operatorId | String | 操作人ID | "system" |
| records[].businessId | String | 业务单号 | "upgrade_20240115001" |
| records[].createTime | String | 创建时间 | "2024-01-15T10:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 5,
    "page": 1,
    "size": 20,
    "records": [
      {
        "id": 101,
        "userId": "user_123456",
        "accountCode": "DEFAULT",
        "fromLevel": 2,
        "toLevel": 3,
        "fromLevelName": "白银会员",
        "toLevelName": "黄金会员",
        "changeType": "UPGRADE",
        "changeReason": "成长值达到升级条件",
        "growthValue": 15000,
        "rewardPoint": 500,
        "effectiveTime": "2024-01-15T10:30:00Z",
        "expireTime": null,
        "operatorId": "system",
        "businessId": "upgrade_20240115001",
        "createTime": "2024-01-15T10:30:00Z"
      },
      {
        "id": 100,
        "userId": "user_123456",
        "accountCode": "DEFAULT",
        "fromLevel": 1,
        "toLevel": 2,
        "fromLevelName": "青铜会员",
        "toLevelName": "白银会员",
        "changeType": "UPGRADE",
        "changeReason": "成长值达到升级条件",
        "growthValue": 8000,
        "rewardPoint": 200,
        "effectiveTime": "2024-01-10T09:15:00Z",
        "expireTime": null,
        "operatorId": "system",
        "businessId": "upgrade_20240110001",
        "createTime": "2024-01-10T09:15:00Z"
      }
    ]
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 用户不存在 | 确认用户ID是否正确 |
| 10002 | 账户代码无效 | 确认账户代码是否正确 |
| 10003 | 时间范围无效 | 确认开始时间不能晚于结束时间 |
| 10004 | 分页参数无效 | 确认页码和每页大小是否在有效范围内 |

### 6.2 查询等级变更统计

**基本信息**
- **接口路径**：`GET /api/v1/level/record/statistics`
- **接口描述**：查询等级变更的统计信息
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Query | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| startTime | String | Query | 是 | 开始时间 | "2024-01-01T00:00:00Z" | 格式：ISO8601 |
| endTime | String | Query | 是 | 结束时间 | "2024-01-31T23:59:59Z" | 格式：ISO8601 |
| changeType | String | Query | 否 | 变更类型 | "UPGRADE" | UPGRADE/DOWNGRADE/ALL |
| dimension | String | Query | 否 | 统计维度 | "DAILY" | DAILY/WEEKLY/MONTHLY |
| includeDistribution | Boolean | Query | 否 | 是否包含等级分布 | true | 默认true |

**请求示例**

```
GET /api/level/record/statistics?accountCode=DEFAULT&startTime=2024-01-01T00:00:00Z&endTime=2024-01-31T23:59:59Z&changeType=ALL&dimension=DAILY&includeDistribution=true
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| accountCode | String | 账户代码 | "DEFAULT" |
| timeRange | Object | 时间范围 | - |
| timeRange.startTime | String | 开始时间 | "2024-01-01T00:00:00Z" |
| timeRange.endTime | String | 结束时间 | "2024-01-31T23:59:59Z" |
| totalChanges | Long | 总变更次数 | 1250 |
| upgradeCount | Long | 升级次数 | 1100 |
| downgradeCount | Long | 降级次数 | 150 |
| resetCount | Long | 重置次数 | 0 |
| levelDistribution | Array | 等级分布 | - |
| levelDistribution[].level | Integer | 等级值 | 1 |
| levelDistribution[].levelName | String | 等级名称 | "青铜会员" |
| levelDistribution[].userCount | Long | 用户数量 | 5000 |
| levelDistribution[].percentage | BigDecimal | 占比 | 50.0 |
| levelDistribution[].changeCount | Long | 变更次数 | 800 |
| timeSeriesData | Array | 时间序列数据 | - |
| timeSeriesData[].date | String | 日期 | "2024-01-15" |
| timeSeriesData[].upgradeCount | Long | 升级次数 | 45 |
| timeSeriesData[].downgradeCount | Long | 降级次数 | 5 |
| timeSeriesData[].totalCount | Long | 总变更次数 | 50 |
| topChangeReasons | Array | 主要变更原因 | - |
| topChangeReasons[].reason | String | 变更原因 | "成长值达到升级条件" |
| topChangeReasons[].count | Long | 次数 | 800 |
| topChangeReasons[].percentage | BigDecimal | 占比 | 64.0 |
| statisticsTime | String | 统计时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "accountCode": "DEFAULT",
    "timeRange": {
      "startTime": "2024-01-01T00:00:00Z",
      "endTime": "2024-01-31T23:59:59Z"
    },
    "totalChanges": 1250,
    "upgradeCount": 1100,
    "downgradeCount": 150,
    "resetCount": 0,
    "levelDistribution": [
      {
        "level": 1,
        "levelName": "青铜会员",
        "userCount": 5000,
        "percentage": 50.0,
        "changeCount": 800
      },
      {
        "level": 2,
        "levelName": "白银会员",
        "userCount": 3000,
        "percentage": 30.0,
        "changeCount": 300
      },
      {
        "level": 3,
        "levelName": "黄金会员",
        "userCount": 1500,
        "percentage": 15.0,
        "changeCount": 120
      },
      {
        "level": 4,
        "levelName": "铂金会员",
        "userCount": 500,
        "percentage": 5.0,
        "changeCount": 30
      }
    ],
    "timeSeriesData": [
      {
        "date": "2024-01-15",
        "upgradeCount": 45,
        "downgradeCount": 5,
        "totalCount": 50
      },
      {
        "date": "2024-01-16",
        "upgradeCount": 52,
        "downgradeCount": 3,
        "totalCount": 55
      }
    ],
    "topChangeReasons": [
      {
        "reason": "成长值达到升级条件",
        "count": 800,
        "percentage": 64.0
      },
      {
        "reason": "VIP用户特殊升级",
        "count": 200,
        "percentage": 16.0
      },
      {
        "reason": "违规行为处罚",
        "count": 100,
        "percentage": 8.0
      }
    ],
    "statisticsTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 账户代码无效 | 确认账户代码是否正确 |
| 10002 | 时间范围无效 | 确认开始时间不能晚于结束时间 |
| 10003 | 时间范围过大 | 单次查询时间范围不能超过1年 |
| 10004 | 权限不足 | 确认是否有管理员权限 |
| 10005 | 统计维度无效 | 确认统计维度是否在枚举范围内 |

### 6.3 等级事务查询

**基本信息**
- **接口路径**：`GET /api/v1/level/transaction`
- **接口描述**：查询等级事务记录，支持按多种条件筛选和分页查询
- **访问权限**：需要API认证
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| userId | String | Query | 否 | 用户唯一标识 | "user_123456" | 长度1-64 |
| accountCode | String | Query | 是 | 账户代码 | "MALL_APP" | 长度1-32 |
| transactionId | String | Query | 否 | 事务ID | "LT20240115001" | 长度1-64 |
| businessId | String | Query | 否 | 业务单号 | "biz_20240115001" | 长度1-64 |
| transactionType | String | Query | 否 | 事务类型 | "LEVEL_UPGRADE" | 见事务类型枚举 |
| status | String | Query | 否 | 事务状态 | "SUCCESS" | PENDING/PROCESSING/SUCCESS/FAILED/CANCELLED |
| startTime | String | Query | 否 | 开始时间 | "2024-01-01 00:00:00" | 格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | Query | 否 | 结束时间 | "2024-01-31 23:59:59" | 格式：yyyy-MM-dd HH:mm:ss |
| pageNum | Integer | Query | 否 | 页码 | 1 | 默认1，最小1 |
| pageSize | Integer | Query | 否 | 每页记录数 | 20 | 默认20，范围1-100 |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| total | Integer | 总记录数 | 150 |
| pageNum | Integer | 当前页码 | 1 |
| pageSize | Integer | 每页记录数 | 20 |
| pages | Integer | 总页数 | 8 |
| list | Array | 事务记录列表 | - |
| list[].transactionId | String | 事务ID | "LT20240115001" |
| list[].userId | String | 用户ID | "user_123456" |
| list[].accountCode | String | 账户代码 | "MALL_APP" |
| list[].businessId | String | 业务单号 | "biz_20240115001" |
| list[].businessType | String | 业务类型 | "GROWTH_CHANGE" |
| list[].transactionType | String | 事务类型 | "LEVEL_UPGRADE" |
| list[].status | String | 事务状态 | "SUCCESS" |
| list[].description | String | 事务描述 | "成长值达到升级条件" |
| list[].sourceLevel | Integer | 原等级 | 2 |
| list[].targetLevel | Integer | 新等级 | 3 |
| list[].sourceLevelName | String | 原等级名称 | "银牌会员" |
| list[].targetLevelName | String | 新等级名称 | "金牌会员" |
| list[].growthValue | Long | 成长值 | 1500 |
| list[].processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| list[].errorMessage | String | 错误信息 | null |
| list[].retryCount | Integer | 重试次数 | 0 |
| list[].createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| list[].updateTime | String | 更新时间 | "2024-01-15 10:00:01" |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 150,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 8,
    "list": [
      {
        "transactionId": "LT20240115001",
        "userId": "user_123456",
        "accountCode": "MALL_APP",
        "businessId": "biz_20240115001",
        "businessType": "GROWTH_CHANGE",
        "transactionType": "LEVEL_UPGRADE",
        "status": "SUCCESS",
        "description": "成长值达到升级条件",
        "sourceLevel": 2,
        "targetLevel": 3,
        "sourceLevelName": "银牌会员",
        "targetLevelName": "金牌会员",
        "growthValue": 1500,
        "processedTime": "2024-01-15 10:00:01",
        "errorMessage": null,
        "retryCount": 0,
        "createTime": "2024-01-15 10:00:00",
        "updateTime": "2024-01-15 10:00:01"
      }
    ]
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**事务类型枚举**

| 类型 | 描述 |
| ---- | ---- |
| LEVEL_UPGRADE | 等级升级 |
| LEVEL_DOWNGRADE | 等级降级 |
| LEVEL_RESET | 等级重置 |
| LEVEL_MANUAL_ADJUST | 等级手动调整 |
| LEVEL_BATCH_UPDATE | 等级批量更新 |

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查参数格式和取值范围 |
| 1003 | 账户代码无效 | 确认账户代码是否正确 |
| 1004 | 时间范围无效 | 确认开始时间小于结束时间 |
| 1005 | 页码超出范围 | 调整页码参数 |

### 6.4 等级事务详情查询

**基本信息**
- **接口路径**：`GET /api/v1/level/transaction/{transactionId}`
- **接口描述**：根据事务ID查询等级事务的详细信息
- **访问权限**：需要API认证
- **限流规则**：200次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| transactionId | String | Path | 是 | 事务ID | "LT20240115001" | 长度1-64 |
| includeDetail | Boolean | Query | 否 | 是否包含详细信息 | true | 默认false |

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| transactionId | String | 事务ID | "LT20240115001" |
| userId | String | 用户ID | "user_123456" |
| accountCode | String | 账户代码 | "MALL_APP" |
| businessId | String | 业务单号 | "biz_20240115001" |
| businessType | String | 业务类型 | "GROWTH_CHANGE" |
| transactionType | String | 事务类型 | "LEVEL_UPGRADE" |
| status | String | 事务状态 | "SUCCESS" |
| description | String | 事务描述 | "成长值达到升级条件" |
| sourceLevel | Integer | 原等级 | 2 |
| targetLevel | Integer | 新等级 | 3 |
| sourceLevelName | String | 原等级名称 | "银牌会员" |
| targetLevelName | String | 新等级名称 | "金牌会员" |
| growthValue | Long | 成长值 | 1500 |
| processedTime | String | 处理时间 | "2024-01-15 10:00:01" |
| errorMessage | String | 错误信息 | null |
| retryCount | Integer | 重试次数 | 0 |
| maxRetryCount | Integer | 最大重试次数 | 3 |
| operatorId | String | 操作人ID | "system" |
| extraData | String | 扩展数据 | "{\"remark\":\"系统自动处理\"}" |
| createTime | String | 创建时间 | "2024-01-15 10:00:00" |
| updateTime | String | 更新时间 | "2024-01-15 10:00:01" |
| version | Integer | 版本号 | 1 |
| detail | Object | 详细信息（当includeDetail=true时返回） | - |
| detail.ruleInfo | Object | 规则信息 | - |
| detail.ruleInfo.ruleId | Long | 规则ID | 1001 |
| detail.ruleInfo.ruleName | String | 规则名称 | "等级升级规则" |
| detail.ruleInfo.ruleExpression | String | 规则表达式 | "user.growthValue >= 1000" |
| detail.levelInfo | Object | 等级信息 | - |
| detail.levelInfo.levelConfig | Array | 等级配置 | - |
| detail.executionLog | Array | 执行日志 | - |
| detail.executionLog[].step | String | 执行步骤 | "RULE_EVALUATION" |
| detail.executionLog[].timestamp | String | 时间戳 | "2024-01-15 10:00:00.123" |
| detail.executionLog[].message | String | 日志信息 | "规则评估完成，满足升级条件" |
| detail.executionLog[].data | Object | 相关数据 | {} |

**响应示例**

```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "transactionId": "LT20240115001",
    "userId": "user_123456",
    "accountCode": "MALL_APP",
    "businessId": "biz_20240115001",
    "businessType": "GROWTH_CHANGE",
    "transactionType": "LEVEL_UPGRADE",
    "status": "SUCCESS",
    "description": "成长值达到升级条件",
    "sourceLevel": 2,
    "targetLevel": 3,
    "sourceLevelName": "银牌会员",
    "targetLevelName": "金牌会员",
    "growthValue": 1500,
    "processedTime": "2024-01-15 10:00:01",
    "errorMessage": null,
    "retryCount": 0,
    "maxRetryCount": 3,
    "operatorId": "system",
    "extraData": "{\"remark\":\"系统自动处理\"}",
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:01",
    "version": 1,
    "detail": {
      "ruleInfo": {
        "ruleId": 1001,
        "ruleName": "等级升级规则",
        "ruleExpression": "user.growthValue >= 1000"
      },
      "levelInfo": {
        "levelConfig": [
          {
            "level": 2,
            "levelName": "银牌会员",
            "minGrowthValue": 500,
            "maxGrowthValue": 999
          },
          {
            "level": 3,
            "levelName": "金牌会员",
            "minGrowthValue": 1000,
            "maxGrowthValue": 2999
          }
        ]
      },
      "executionLog": [
        {
          "step": "TRANSACTION_CREATE",
          "timestamp": "2024-01-15 10:00:00.001",
          "message": "创建等级事务",
          "data": {}
        },
        {
          "step": "RULE_EVALUATION",
          "timestamp": "2024-01-15 10:00:00.123",
          "message": "规则评估完成，满足升级条件",
          "data": {
            "ruleId": 1001,
            "userGrowthValue": 1500,
            "targetLevel": 3
          }
        },
        {
          "step": "LEVEL_UPDATE",
          "timestamp": "2024-01-15 10:00:00.456",
          "message": "更新用户等级",
          "data": {
            "sourceLevel": 2,
            "targetLevel": 3
          }
        },
        {
          "step": "TRANSACTION_COMPLETE",
          "timestamp": "2024-01-15 10:00:01.000",
          "message": "事务处理完成",
          "data": {}
        }
      ]
    }
  },
  "timestamp": *************,
  "traceId": "trace-*********"
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 1001 | 参数错误 | 检查事务ID格式 |
| 2301 | 事务不存在 | 确认事务ID是否正确 |
| 2302 | 事务已过期 | 事务记录已超过保存期限 |

## 7. 等级账户管理API

### 7.1 查询等级账户列表

**基本信息**
- **接口路径**：`GET /admin/api/v1/level/accounts`
- **接口描述**：查询所有等级账户配置
- **访问权限**：需要管理员权限
- **限流规则**：100次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| brand | String | Query | 否 | 品牌标识 | "DEFAULT" | 长度1-32 |
| status | String | Query | 否 | 账户状态 | "ACTIVE" | ACTIVE/INACTIVE |
| page | Integer | Query | 否 | 页码 | 1 | 最小值1 |
| size | Integer | Query | 否 | 每页大小 | 20 | 1-100 |

**请求示例**

```
GET /api/level/accounts?brand=DEFAULT&status=ACTIVE&page=1&size=20
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| total | Long | 总记录数 | 3 |
| page | Integer | 当前页码 | 1 |
| size | Integer | 每页大小 | 20 |
| list | Array | 账户列表 | - |
| list[].id | Long | 账户ID | 1 |
| list[].accountCode | String | 账户代码 | "DEFAULT" |
| list[].accountName | String | 账户名称 | "默认等级账户" |
| list[].accountDescription | String | 账户描述 | "系统默认的等级账户" |
| list[].brand | String | 品牌标识 | "DEFAULT" |
| list[].status | String | 账户状态 | "ACTIVE" |
| list[].levelCount | Integer | 等级数量 | 4 |
| list[].rankingFactors | String | 排名因子 | "growth_value,purchase_amount" |
| list[].benefitConfig | Object | 权益配置 | - |
| list[].benefitConfig.pointMultiplier | Boolean | 积分倍数 | true |
| list[].benefitConfig.discountRate | Boolean | 折扣率 | true |
| list[].benefitConfig.freeShipping | Boolean | 免邮服务 | true |
| list[].benefitConfig.priorityService | Boolean | 优先服务 | true |
| list[].benefitConfig.exclusiveService | Boolean | 专属服务 | false |
| list[].notifyConfig | Object | 通知配置 | - |
| list[].notifyConfig.upgradeNotify | Boolean | 升级通知 | true |
| list[].notifyConfig.downgradeNotify | Boolean | 降级通知 | true |
| list[].notifyConfig.expireNotify | Boolean | 过期通知 | true |
| list[].notifyConfig.privilegeNotify | Boolean | 权益通知 | false |
| list[].createTime | String | 创建时间 | "2024-01-01T00:00:00Z" |
| list[].updateTime | String | 更新时间 | "2024-01-15T10:30:00Z" |
| list[].creatorId | String | 创建人ID | "admin_001" |
| list[].updaterId | String | 更新人ID | "admin_002" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 3,
    "page": 1,
    "size": 20,
    "list": [
      {
        "id": 1,
        "accountCode": "DEFAULT",
        "accountName": "默认等级账户",
        "accountDescription": "系统默认的等级账户",
        "brand": "DEFAULT",
        "status": "ACTIVE",
        "levelCount": 4,
        "rankingFactors": "growth_value,purchase_amount,activity_score",
        "benefitConfig": {
          "pointMultiplier": true,
          "discountRate": true,
          "freeShipping": true,
          "priorityService": true,
          "exclusiveService": false
        },
        "notifyConfig": {
          "upgradeNotify": true,
          "downgradeNotify": true,
          "expireNotify": true,
          "privilegeNotify": false
        },
        "createTime": "2024-01-01T00:00:00Z",
        "updateTime": "2024-01-15T10:30:00Z",
        "creatorId": "admin_001",
        "updaterId": "admin_002"
      }
    ]
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 品牌标识无效 | 确认品牌标识是否正确 |
| 10002 | 状态参数无效 | 确认状态值是否在枚举范围内 |
| 10003 | 分页参数无效 | 确认页码和每页大小是否在有效范围内 |
| 10004 | 权限不足 | 确认是否有管理员权限 |

### 7.2 创建/编辑等级账户

**基本信息**
- **接口路径**：`POST /admin/api/v1/level/accounts`
- **接口描述**：创建新的等级账户
- **访问权限**：需要管理员权限
- **限流规则**：10次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Body | 是 | 账户代码 | "VIP_ACCOUNT" | 长度1-32，全局唯一 |
| accountName | String | Body | 是 | 账户名称 | "VIP等级账户" | 长度1-100 |
| accountDescription | String | Body | 否 | 账户描述 | "VIP用户专属等级账户" | 长度1-500 |
| brand | String | Body | 是 | 品牌标识 | "VIP" | 长度1-32 |
| levelCount | Integer | Body | 否 | 预设等级数量 | 5 | 1-20 |
| rankingFactors | String | Body | 否 | 排名因子 | "growth_value,vip_score" | 逗号分隔，最多10个 |
| benefitConfig | Object | Body | 否 | 权益配置 | - | - |
| benefitConfig.pointMultiplier | Boolean | Body | 否 | 积分倍数 | true | 默认false |
| benefitConfig.discountRate | Boolean | Body | 否 | 折扣率 | true | 默认false |
| benefitConfig.freeShipping | Boolean | Body | 否 | 免邮服务 | true | 默认false |
| benefitConfig.priorityService | Boolean | Body | 否 | 优先服务 | true | 默认false |
| benefitConfig.exclusiveService | Boolean | Body | 否 | 专属服务 | true | 默认false |
| notifyConfig | Object | Body | 否 | 通知配置 | - | - |
| notifyConfig.upgradeNotify | Boolean | Body | 否 | 升级通知 | true | 默认true |
| notifyConfig.downgradeNotify | Boolean | Body | 否 | 降级通知 | true | 默认true |
| notifyConfig.expireNotify | Boolean | Body | 否 | 过期通知 | true | 默认false |
| notifyConfig.privilegeNotify | Boolean | Body | 否 | 权益通知 | true | 默认false |

**请求示例**

```json
{
  "accountCode": "VIP_ACCOUNT",
  "accountName": "VIP等级账户",
  "accountDescription": "VIP用户专属等级账户",
  "brand": "VIP",
  "levelCount": 5,
  "rankingFactors": "growth_value,vip_score,consumption_frequency",
  "benefitConfig": {
    "pointMultiplier": true,
    "discountRate": true,
    "freeShipping": true,
    "priorityService": true,
    "exclusiveService": true
  },
  "notifyConfig": {
    "upgradeNotify": true,
    "downgradeNotify": true,
    "expireNotify": true,
    "privilegeNotify": true
  }
}
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| id | Long | 账户ID | 2 |
| accountCode | String | 账户代码 | "VIP_ACCOUNT" |
| accountName | String | 账户名称 | "VIP等级账户" |
| brand | String | 品牌标识 | "VIP" |
| status | String | 账户状态 | "ACTIVE" |
| levelCount | Integer | 等级数量 | 5 |
| createTime | String | 创建时间 | "2024-01-20T14:30:00Z" |
| creatorId | String | 创建人ID | "admin_001" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 2,
    "accountCode": "VIP_ACCOUNT",
    "accountName": "VIP等级账户",
    "brand": "VIP",
    "status": "ACTIVE",
    "levelCount": 5,
    "createTime": "2024-01-20T14:30:00Z",
    "creatorId": "admin_001"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 账户代码重复 | 账户代码必须全局唯一 |
| 10002 | 账户名称重复 | 同品牌下账户名称必须唯一 |
| 10003 | 品牌标识无效 | 确认品牌标识是否正确 |
| 10004 | 参数验证失败 | 检查必填参数和格式约束 |
| 10005 | 排名因子无效 | 确认排名因子是否在枚举范围内 |
| 10006 | 权限不足 | 确认是否有管理员权限 |


## 8. 等级数据分析API

### 8.1 等级分布统计

**基本信息**
- **接口路径**：`GET /admin/api/v1/level/analytics/distribution`
- **接口描述**：查询用户等级分布统计
- **访问权限**：需要管理员权限
- **限流规则**：50次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Query | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| timeRange | String | Query | 否 | 时间范围 | "LAST_30_DAYS" | LAST_7_DAYS/LAST_30_DAYS/LAST_90_DAYS |
| includeInactive | Boolean | Query | 否 | 是否包含非活跃用户 | false | 默认false |
| groupBy | String | Query | 否 | 分组维度 | "LEVEL" | LEVEL/REGION/AGE_GROUP |

**请求示例**

```
GET /api/level/analytics/distribution?accountCode=DEFAULT&timeRange=LAST_30_DAYS&includeInactive=false&groupBy=LEVEL
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| accountCode | String | 账户代码 | "DEFAULT" |
| totalUsers | Long | 总用户数 | 10000 |
| activeUsers | Long | 活跃用户数 | 8500 |
| timeRange | String | 时间范围 | "LAST_30_DAYS" |
| distribution | Array | 等级分布 | - |
| distribution[].level | Integer | 等级值 | 1 |
| distribution[].levelName | String | 等级名称 | "青铜会员" |
| distribution[].userCount | Long | 用户数量 | 5000 |
| distribution[].activeUserCount | Long | 活跃用户数量 | 4200 |
| distribution[].percentage | BigDecimal | 占比 | 50.0 |
| distribution[].avgGrowthValue | Long | 平均成长值 | 2500 |
| distribution[].avgStayDays | Integer | 平均停留天数 | 45 |
| distribution[].upgradeRate | BigDecimal | 升级率 | 15.2 |
| trendData | Array | 趋势数据 | - |
| trendData[].date | String | 日期 | "2024-01-15" |
| trendData[].levelDistribution | Object | 当日等级分布 | - |
| statisticsTime | String | 统计时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "accountCode": "DEFAULT",
    "totalUsers": 10000,
    "activeUsers": 8500,
    "timeRange": "LAST_30_DAYS",
    "distribution": [
      {
        "level": 1,
        "levelName": "青铜会员",
        "userCount": 5000,
        "activeUserCount": 4200,
        "percentage": 50.0,
        "avgGrowthValue": 2500,
        "avgStayDays": 45,
        "upgradeRate": 15.2
      },
      {
        "level": 2,
        "levelName": "白银会员",
        "userCount": 3000,
        "activeUserCount": 2700,
        "percentage": 30.0,
        "avgGrowthValue": 7500,
        "avgStayDays": 60,
        "upgradeRate": 12.5
      },
      {
        "level": 3,
        "levelName": "黄金会员",
        "userCount": 1500,
        "activeUserCount": 1350,
        "percentage": 15.0,
        "avgGrowthValue": 15000,
        "avgStayDays": 90,
        "upgradeRate": 8.3
      },
      {
        "level": 4,
        "levelName": "铂金会员",
        "userCount": 500,
        "activeUserCount": 450,
        "percentage": 5.0,
        "avgGrowthValue": 35000,
        "avgStayDays": 120,
        "upgradeRate": 2.1
      }
    ],
    "trendData": [
      {
        "date": "2024-01-15",
        "levelDistribution": {
          "level1": 4950,
          "level2": 2980,
          "level3": 1480,
          "level4": 490
        }
      }
    ],
    "statisticsTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 账户代码无效 | 确认账户代码是否正确 |
| 10002 | 时间范围无效 | 确认时间范围是否在枚举范围内 |
| 10003 | 分组维度无效 | 确认分组维度是否在枚举范围内 |
| 10004 | 权限不足 | 确认是否有管理员权限 |

### 8.2 等级流转分析

**基本信息**
- **接口路径**：`GET /admin/api/v1/level/analytics/flow`
- **接口描述**：查询等级流转趋势分析
- **访问权限**：需要管理员权限
- **限流规则**：30次/分钟

**请求参数**

| 参数名 | 类型 | 位置 | 必填 | 描述 | 示例 | 约束 |
| ------ | ---- | ---- | ---- | ---- | ---- | ---- |
| accountCode | String | Query | 是 | 账户代码 | "DEFAULT" | 长度1-32 |
| startTime | String | Query | 是 | 开始时间 | "2024-01-01T00:00:00Z" | 格式：ISO8601 |
| endTime | String | Query | 是 | 结束时间 | "2024-01-31T23:59:59Z" | 格式：ISO8601 |
| flowType | String | Query | 否 | 流转类型 | "ALL" | ALL/UPGRADE/DOWNGRADE |
| minFlowCount | Integer | Query | 否 | 最小流转次数 | 10 | 过滤小流量数据 |

**请求示例**

```
GET /api/level/analytics/flow?accountCode=DEFAULT&startTime=2024-01-01T00:00:00Z&endTime=2024-01-31T23:59:59Z&flowType=ALL&minFlowCount=10
```

**响应参数**

| 参数名 | 类型 | 描述 | 示例 |
| ------ | ---- | ---- | ---- |
| accountCode | String | 账户代码 | "DEFAULT" |
| timeRange | Object | 时间范围 | - |
| timeRange.startTime | String | 开始时间 | "2024-01-01T00:00:00Z" |
| timeRange.endTime | String | 结束时间 | "2024-01-31T23:59:59Z" |
| totalFlows | Long | 总流转次数 | 1150 |
| upgradeFlows | Long | 升级流转次数 | 1000 |
| downgradeFlows | Long | 降级流转次数 | 150 |
| flowMatrix | Array | 流转矩阵 | - |
| flowMatrix[].fromLevel | Integer | 原等级 | 1 |
| flowMatrix[].toLevel | Integer | 目标等级 | 2 |
| flowMatrix[].fromLevelName | String | 原等级名称 | "青铜会员" |
| flowMatrix[].toLevelName | String | 目标等级名称 | "白银会员" |
| flowMatrix[].userCount | Long | 流转用户数 | 800 |
| flowMatrix[].flowRate | BigDecimal | 流转率 | 16.0 |
| flowMatrix[].avgDays | Integer | 平均流转天数 | 30 |
| retentionRate | Object | 等级保持率 | - |
| retentionRate.level1 | BigDecimal | 1级保持率 | 84.0 |
| retentionRate.level2 | BigDecimal | 2级保持率 | 90.0 |
| retentionRate.level3 | BigDecimal | 3级保持率 | 96.7 |
| retentionRate.level4 | BigDecimal | 4级保持率 | 98.0 |
| flowTrends | Array | 流转趋势 | - |
| flowTrends[].date | String | 日期 | "2024-01-15" |
| flowTrends[].upgradeCount | Long | 升级次数 | 45 |
| flowTrends[].downgradeCount | Long | 降级次数 | 5 |
| flowTrends[].totalFlowCount | Long | 总流转次数 | 50 |
| statisticsTime | String | 统计时间 | "2024-01-20T14:30:00Z" |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "accountCode": "DEFAULT",
    "timeRange": {
      "startTime": "2024-01-01T00:00:00Z",
      "endTime": "2024-01-31T23:59:59Z"
    },
    "totalFlows": 1150,
    "upgradeFlows": 1000,
    "downgradeFlows": 150,
    "flowMatrix": [
      {
        "fromLevel": 1,
        "toLevel": 2,
        "fromLevelName": "青铜会员",
        "toLevelName": "白银会员",
        "userCount": 800,
        "flowRate": 16.0,
        "avgDays": 30
      },
      {
        "fromLevel": 2,
        "toLevel": 3,
        "fromLevelName": "白银会员",
        "toLevelName": "黄金会员",
        "userCount": 300,
        "flowRate": 10.0,
        "avgDays": 45
      },
      {
        "fromLevel": 3,
        "toLevel": 2,
        "fromLevelName": "黄金会员",
        "toLevelName": "白银会员",
        "userCount": 50,
        "flowRate": 3.3,
        "avgDays": 15
      }
    ],
    "retentionRate": {
      "level1": 84.0,
      "level2": 90.0,
      "level3": 96.7,
      "level4": 98.0
    },
    "flowTrends": [
      {
        "date": "2024-01-15",
        "upgradeCount": 45,
        "downgradeCount": 5,
        "totalFlowCount": 50
      },
      {
        "date": "2024-01-16",
        "upgradeCount": 52,
        "downgradeCount": 3,
        "totalFlowCount": 55
      }
    ],
    "statisticsTime": "2024-01-20T14:30:00Z"
  }
}
```

**错误码**

| 错误码 | 描述 | 解决方案 |
| ------ | ---- | -------- |
| 10001 | 账户代码无效 | 确认账户代码是否正确 |
| 10002 | 时间范围无效 | 确认开始时间不能晚于结束时间 |
| 10003 | 时间范围过大 | 单次查询时间范围不能超过1年 |
| 10004 | 流转类型无效 | 确认流转类型是否在枚举范围内 |
| 10005 | 权限不足 | 确认是否有管理员权限 |

## 9. 错误处理

### 9.1 业务异常响应
```json
{
  "code": 10001,
  "message": "用户等级不存在",
  "data": null,
  "timestamp": "2024-01-20T14:30:00Z",
  "requestId": "req_*********",
  "details": {
    "userId": 123456,
    "accountCode": "DEFAULT"
  }
}
```

### 9.2 参数验证异常响应
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null,
  "timestamp": "2024-01-20T14:30:00Z",
  "requestId": "req_*********",
  "details": {
    "field": "userId",
    "rejectedValue": null,
    "message": "用户ID不能为空"
  }
}
```

## 10. 接口安全

### 10.1 认证方式
- API Key认证
- JWT Token认证
- OAuth 2.0认证

### 10.2 权限控制
- 基于角色的访问控制(RBAC)
- 接口级权限验证
- 数据级权限隔离

### 10.3 限流策略
- 基于用户的限流: 1000次/分钟
- 基于IP的限流: 10000次/分钟
- 基于接口的限流: 根据接口复杂度设定

## 11. 性能指标

### 11.1 响应时间要求
- 查询接口: < 100ms
- 计算接口: < 500ms
- 批量接口: < 2s
- 统计接口: < 1s

### 11.2 并发能力
- 支持10000+ QPS
- 支持100万+ 日活用户
- 99.9%可用性保证

## 12. 版本管理

### 12.1 API版本策略
- URL路径版本控制: `/api/v1/level/`
- 向后兼容原则
- 废弃接口提前通知

### 12.2 变更日志
- v1.0.0: 初始版本发布
- v1.1.0: 新增批量操作接口
- v1.2.0: 新增数据分析接口

## 13. 监控和日志

### 13.1 接口监控
- 请求量监控
- 响应时间监控
- 错误率监控
- 业务指标监控

### 13.2 日志规范
- 请求日志记录
- 业务操作日志
- 错误异常日志
- 性能分析日志

## 14. 测试用例

### 14.1 功能测试
- 正常流程测试
- 异常场景测试
- 边界条件测试
- 并发测试

### 14.2 性能测试
- 压力测试
- 负载测试
- 稳定性测试
- 容量测试

## 15. 总结

等级API设计遵循RESTful规范，提供了完整的等级管理功能，包括：

1. **用户等级管理**: 等级查询、计算、批量操作
2. **权益管理**: 权益查询、验证、倍数获取
3. **配置管理**: 等级配置的CRUD操作
4. **记录管理**: 等级变更记录查询和统计
5. **账户管理**: 等级账户的配置管理
6. **数据分析**: 等级分布和流转分析

API设计注重性能、安全性和可扩展性，能够支撑大规模用户和高并发场景，为业务发展提供强有力的技术支撑。