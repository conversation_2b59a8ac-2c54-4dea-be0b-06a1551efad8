micronaut:
  application:
    name: account-web
  server:
    port: 8080
    context-path: /api

  openapi:
    views:
      #编译期生成swagger-ui所需文件
      spec: swagger-ui.enabled=true
  router:
    static-resources:
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**
  data:
    jdbc:
      repositories:
        enabled: true

datasources:
  default:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************
    username: root
    password: root
    dialect: MYSQL

flyway:
  datasources:
    default:
      enabled: true
      locations: classpath:/migration
      baseline-on-migrate: true
      baseline-version: 0
      placeholder-replacement: false

logger:
  levels:
    root: INFO
    com.fzucxl: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
