﻿package com.fzucxl.open.point.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 解冻积分参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointUnfreezeParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 冻结记录ID
     */
    private String freezeId;    
    /**
     * 解冻积分数量
     */
    private Long point;    
    /**
     * 解冻原因
     */
    private String reason;    
    /**
     * 业务单号
     */
    private String businessId;    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 备注信息
     */
    private String remark;    
    /**
     * 回调通知地址
     */
    private String notifyUrl;}
