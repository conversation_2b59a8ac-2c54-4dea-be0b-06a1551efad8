﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章进度参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeProgressQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 交易ID
     */
    private String transactionId;}
