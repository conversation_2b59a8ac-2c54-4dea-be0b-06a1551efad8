﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询勋章交易详情参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeTransactionDetailQueryParam extends Extensible {    
    /**
     * 交易ID
     */
    private String transactionId;    
    /**
     * 是否包含详细信息
     */
    private Boolean includeDetail;}
