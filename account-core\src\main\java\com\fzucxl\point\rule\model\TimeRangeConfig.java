package com.fzucxl.point.rule.model;

/**
 * 时间范围配置模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TimeRangeConfig {
    
    private String type; // ABSOLUTE, RELATIVE
    private Integer value; // 数值
    private String unit; // DAYS, MONTHS, YEARS, HOURS, MINUTES
    private String startTime; // 绝对时间的开始时间
    private String endTime; // 绝对时间的结束时间
    private String timeField; // 时间字段名
    
    public TimeRangeConfig() {
        this.type = "RELATIVE";
        this.unit = "DAYS";
        this.timeField = "created_time";
    }
    
    public TimeRangeConfig(String type, Integer value, String unit) {
        this.type = type;
        this.value = value;
        this.unit = unit;
        this.timeField = "created_time";
    }
    
    // Getter and Setter methods
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Integer getValue() {
        return value;
    }
    
    public void setValue(Integer value) {
        this.value = value;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public String getTimeField() {
        return timeField;
    }
    
    public void setTimeField(String timeField) {
        this.timeField = timeField;
    }
    
    @Override
    public String toString() {
        return "TimeRangeConfig{" +
                "type='" + type + '\'' +
                ", value=" + value +
                ", unit='" + unit + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", timeField='" + timeField + '\'' +
                '}';
    }
}