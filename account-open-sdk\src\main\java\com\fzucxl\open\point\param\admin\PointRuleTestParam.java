﻿package com.fzucxl.open.point.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 测试积分规则参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleTestParam extends Extensible {    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 测试数据
     */
    private java.util.Map<String, Object> testData;    
    /**
     * 测试用户ID
     */
    private Long userId;}
