﻿package com.fzucxl.open.badge.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 手动发放勋章参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAwardParam extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 发放来源
     */
    private String source;    
    /**
     * 业务ID
     */
    private String businessId;    
    /**
     * 发放原因
     */
    private String reason;    
    /**
     * 额外数据
     */
    private java.util.Map<String, Object> extraData;}
