-- 等级账户表
CREATE TABLE IF NOT EXISTS `level_account` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_code` varchar(100) NOT NULL COMMENT '账户编码',
    `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
    `account_name` varchar(200) NOT NULL COMMENT '账户名称',
    `description` varchar(500) DEFAULT NULL COMMENT '账户描述',
    `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '账户状态',
    `level_count` int(11) DEFAULT NULL COMMENT '等级数量',
    `ranking_factor` varchar(500) DEFAULT NULL COMMENT '排序因子',
    `basic_config` text DEFAULT NULL COMMENT '基础配置',
    `benefit_config` text DEFAULT NULL COMMENT '权益配置',
    `notify_config` text DEFAULT NULL COMMENT '通知配置',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_account_code` (`account_code`),
    UNIQUE KEY `uk_account_brand` (`account_code`, `brand_code`),
    KEY `idx_brand_code` (`brand_code`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级账户表';

-- 等级记录表
CREATE TABLE IF NOT EXISTS `level_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `from_level` int(11) DEFAULT NULL COMMENT '源等级',
  `to_level` int(11) DEFAULT NULL COMMENT '目标等级',
  `source` varchar(50) NOT NULL COMMENT '来源：SYSTEM-系统，MANUAL-手动，EVENT-事件，RULE-规则',
  `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
  `record_time` datetime NOT NULL COMMENT '记录时间',
  `expire_time` datetime DEFAULT NULL COMMENT '失效时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `extra_data` text DEFAULT NULL COMMENT '扩展数据',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级记录表';
-- 等级规则表
CREATE TABLE IF NOT EXISTS `level_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `level` int(11) NOT NULL COMMENT '等级编码',
  `level_name` varchar(200) NOT NULL COMMENT '等级名称',
  `level_icon` varchar(500) DEFAULT NULL COMMENT '等级图标',
  `description` varchar(500) DEFAULT NULL COMMENT '等级描述',
  `point_multiplier` decimal(10,2) NOT NULL DEFAULT 1.00 COMMENT '积分倍数',
  `discount_rate` decimal(10,2) DEFAULT NULL COMMENT '等级折扣率',
  `free_shipping_threshold` decimal(10,2) DEFAULT NULL COMMENT '免运费额度',
  `upgrade_reward_point` bigint(20) NOT NULL DEFAULT 0 COMMENT '升级奖励积分',
  `upgrade_rule` text DEFAULT NULL COMMENT '升级规则',
  `recalculate_rule` text DEFAULT NULL COMMENT '重新计算规则',
  `privilege` text DEFAULT NULL COMMENT '权益',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_brand_level` (`account_code`, `brand_code`, `level`),
  KEY `idx_account_code` (`account_code`),
  KEY `idx_brand_code` (`brand_code`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级规则表';
-- 等级交易表
CREATE TABLE IF NOT EXISTS `level_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `business_id` varchar(100) NOT NULL COMMENT '业务ID',
  `transaction_id` varchar(100) NOT NULL COMMENT '交易ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型',
  `from_level` int(11) DEFAULT NULL COMMENT '来源等级',
  `to_level` int(11) DEFAULT NULL COMMENT '目标等级',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '交易状态',
  `description` varchar(500) DEFAULT NULL COMMENT '交易描述',
  `effective_time` datetime DEFAULT NULL COMMENT '生效时间',
  `expire_time` datetime DEFAULT NULL COMMENT '失效时间',
  `completeTime` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `input_data` text DEFAULT NULL COMMENT '输入数据',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transaction_id` (`transaction_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级交易表';
-- 用户等级表
CREATE TABLE IF NOT EXISTS `user_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `account_code` varchar(100) NOT NULL COMMENT '账户编码',
  `brand_code` varchar(100) NOT NULL COMMENT '品牌编码',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '当前等级',
  `level_name` varchar(200) DEFAULT NULL COMMENT '等级名称',
  `level_up_time` datetime DEFAULT NULL COMMENT '等级升级时间',
  `effective_time` datetime DEFAULT NULL COMMENT '生效时间',
  `expire_time` datetime DEFAULT NULL COMMENT '失效时间',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_account_brand` (`user_id`, `account_code`, `brand_code`),
  KEY `idx_account_brand` (`account_code`, `brand_code`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级表';
