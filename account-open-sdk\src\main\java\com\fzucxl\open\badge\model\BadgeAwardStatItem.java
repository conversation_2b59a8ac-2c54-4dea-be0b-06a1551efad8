﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章发放统计项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeAwardStatItem extends Extensible {    
    /**
     * 统计日期
     */
    private String date;    
    /**
     * 勋章类型
     */
    private String badgeType;    
    /**
     * 发放数量
     */
    private Long awardCount;    
    /**
     * 获得用户数
     */
    private Long userCount;}
