﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级特权
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelPrivilege extends Extensible {    
    /**
     * 特权ID
     */
    private String privilegeId;    
    /**
     * 特权代码
     */
    private String privilegeCode;    
    /**
     * 特权名称
     */
    private String privilegeName;    
    /**
     * 特权类型
     */
    private String privilegeType;    
    /**
     * 特权值
     */
    private String privilegeValue;    
    /**
     * 特权描述
     */
    private String description;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;}
