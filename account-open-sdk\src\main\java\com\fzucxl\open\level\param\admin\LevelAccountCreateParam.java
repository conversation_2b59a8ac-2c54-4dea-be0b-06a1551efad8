﻿package com.fzucxl.open.level.param.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建等级账户参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelAccountCreateParam extends Extensible {    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 账户名称
     */
    private String accountName;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 账户描述
     */
    private String description;    
    /**
     * 状态：ACTIVE(启用), INACTIVE(禁用)
     */
    private String status;    
    /**
     * 操作员ID
     */
    private String operatorId;    
    /**
     * 交易ID
     */
    private String transactionId;}
