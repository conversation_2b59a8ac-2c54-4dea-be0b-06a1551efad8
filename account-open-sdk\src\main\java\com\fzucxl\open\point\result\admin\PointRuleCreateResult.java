﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建积分规则结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointRuleCreateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 规则ID
     */
    private String ruleId;    
    /**
     * 规则代码
     */
    private String ruleCode;    
    /**
     * 创建时间
     */
    private String createTime;}
