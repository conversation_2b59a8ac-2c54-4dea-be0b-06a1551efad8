﻿package com.fzucxl.open.growth.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 成长值排行项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GrowthRankingItem extends Extensible {    
    /**
     * 排名
     */
    private Integer rank;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 成长值
     */
    private Long growthValue;    
    /**
     * 账户代码
     */
    private String accountCode;}
