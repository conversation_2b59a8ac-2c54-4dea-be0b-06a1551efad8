﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章记录模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeRecord extends Extensible {    
    /**
     * 记录ID
     */
    private Long recordId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 操作类型
     */
    private String operationType;    
    /**
     * 操作时间
     */
    private String operationTime;    
    /**
     * 操作人ID
     */
    private Long operatorId;    
    /**
     * 操作原因
     */
    private String reason;    
    /**
     * 扩展数据
     */
    private String extraData;}
