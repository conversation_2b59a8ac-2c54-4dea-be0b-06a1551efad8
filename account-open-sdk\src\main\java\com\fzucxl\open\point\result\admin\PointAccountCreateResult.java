﻿package com.fzucxl.open.point.result.admin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 创建积分账户结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointAccountCreateResult extends Extensible {    
    /**
     * 是否成功
     */
    private Boolean success;    
    /**
     * 账户ID
     */
    private String accountId;    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 创建时间
     */
    private String createTime;}
