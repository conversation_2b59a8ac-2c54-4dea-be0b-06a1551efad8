﻿package com.fzucxl.open.badge.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户勋章参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeQueryParam extends Extensible {    
    /**
     * 用户唯一标识
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 勋章类型：ACHIEVEMENT(成就勋章), ACTIVITY(活动勋章), LEVEL(等级勋章), SPECIAL(特殊勋章)
     */
    private String badgeType;    
    /**
     * 状态：OBTAINED(已获得), NOT_OBTAINED(未获得), EXPIRED(已过期)
     */
    private String status;    
    /**
     * 展示状态：SHOW(展示), HIDE(隐藏)
     */
    private String displayStatus;    
    /**
     * 页码
     */
    private Integer pageNum;    
    /**
     * 页大小
     */
    private Integer pageSize;    
    /**
     * 交易ID，用于关联上下游交易
     */
    private String transactionId;}
