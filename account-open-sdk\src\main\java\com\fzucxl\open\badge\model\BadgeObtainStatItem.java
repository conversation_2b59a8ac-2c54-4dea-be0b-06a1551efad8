﻿package com.fzucxl.open.badge.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 勋章获得统计项模型
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BadgeObtainStatItem extends Extensible {    
    /**
     * 勋章ID
     */
    private Long badgeId;    
    /**
     * 勋章名称
     */
    private String badgeName;    
    /**
     * 获得数量
     */
    private Long obtainCount;    
    /**
     * 获得率
     */
    private java.math.BigDecimal obtainRate;}
