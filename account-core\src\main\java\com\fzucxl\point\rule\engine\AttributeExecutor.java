package com.fzucxl.point.rule.engine;

import com.fzucxl.entity.attribute.AttributeMeta;
import com.fzucxl.point.rule.repository.AttributeMetaRepository;
import io.micronaut.data.jdbc.runtime.JdbcOperations;
import io.micronaut.transaction.annotation.Transactional;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;
import java.util.*;

/**
 * 通用属性执行器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Singleton
public class AttributeExecutor {
    
    private static final Logger log = LoggerFactory.getLogger(AttributeExecutor.class);
    
    @Inject
    private AttributeMetaRepository metaRepository;
    
    @Inject
    private DynamicSqlGenerator sqlGenerator;
    
    @Inject
    private JdbcOperations jdbcOperations;

    
    /**
     * 执行属性表达式（带参数）
     */
    @Transactional(readOnly = true)
    public BigDecimal executeAttribute(String attributeCode, Map<String, Object> params) {
        // 获取属性表达式元数据
        AttributeMeta meta = metaRepository.findByAttributeCodeAndStatus(attributeCode, "ACTIVE")
                .orElse(null);

        if (meta == null) {
            log.warn("属性元数据不存在或已禁用: {}", attributeCode);
            return BigDecimal.ZERO;
        }

        // 生成SQL
        String sql = sqlGenerator.generateQuery(meta, params);

        // 执行查询
        BigDecimal result = executeQuery(sql, params);

        // 返回结果，如果为null则返回默认值
        return result != null ? result : meta.getDefaultValue();
    }


    /**
     * 批量获取动态变量的值
     *
     * @param variableNames 变量名列表
     * @param contextParams 上下文参数
     * @return 变量值映射
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getVariableValues(Set<String> variableNames, Map<String, Object> contextParams) {
        if (variableNames == null || variableNames.isEmpty()) {
            return Collections.emptyMap();
        }

        String userId = contextParams.getOrDefault("userId", "").toString();

        Map<String, Object> result = new HashMap<>();

        // 查询所有相关的属性元数据
        List<AttributeMeta> metaList = metaRepository.findByAttributeCodeIn(new ArrayList<>(variableNames));

        // 按表达式代码建立映射
        Map<String, AttributeMeta> metaMap = new HashMap<>();
        for (AttributeMeta meta : metaList) {
            metaMap.put(meta.getAttributeCode(), meta);
        }

        // 为每个变量获取值
        for (String variableName : variableNames) {
            try {
                Object value = getVariableValue(variableName, metaMap.get(variableName), contextParams);
                result.put(variableName, value);

                log.debug("获取动态变量值: variable={}, value={}", variableName, value);
            } catch (Exception e) {
                log.error("获取动态变量值失败: variable={}, userId={}", variableName, userId, e);
                // 失败时设置默认值
                result.put(variableName, getDefaultValue(variableName, metaMap.get(variableName)));
            }
        }

        return result;
    }


    /**
     * 获取单个变量的值
     */
    private Object getVariableValue(String variableName, AttributeMeta meta, Map<String, Object> contextParams) {
        if (meta == null) {
            log.warn("未找到变量的元数据配置: variable={}", variableName);
            return getDefaultValue(variableName, null);
        }
        // 生成SQL
        String sql = sqlGenerator.generateQuery(meta, contextParams);

        // 执行查询
        return executeQuery(sql, contextParams);
    }


    /**
     * 执行SQL查询
     */
    private BigDecimal executeQuery(String sql, Map<String, Object> params) {
        
        try {
            Object result = jdbcOperations.prepareStatement(sql, statement -> {
                statement.setString(1, (String) params.get("userId"));
                try (var rs = statement.executeQuery()) {
                    if (rs.next()) {
                        return rs.getObject(1);
                    }
                    return null;
                }
            });
            
            if (result == null) {
                return BigDecimal.ZERO;
            }
            
            if (result instanceof Number) {
                return new BigDecimal(result.toString());
            } else {
                return new BigDecimal(result.toString());
            }
            
        } catch (Exception e) {
            log.error("执行SQL查询失败: {}", sql, e);
            throw new RuntimeException("执行SQL查询失败", e);
        }
    }
    
    /**
     * 测试属性表达式
     */
    @Transactional(readOnly = true)
    public BigDecimal testAttribute(String expressionCode, Map<String, Object> testParams) {
        log.info("测试属性表达式: expressionCode={}, params={}",
            expressionCode, testParams);
            
        return executeAttribute(expressionCode, testParams);
    }

    /**
     * 获取默认值
     */
    private Object getDefaultValue(String variableName, AttributeMeta meta) {
        if (meta != null && meta.getDefaultValue() != null) {
            return meta.getDefaultValue();
        }

        // 根据变量名推断默认值类型
        if (variableName.contains("count") || variableName.contains("num")) {
            return 0L;
        } else if (variableName.contains("amount") || variableName.contains("sum")) {
            return 0.0;
        } else {
            return 0;
        }
    }
}