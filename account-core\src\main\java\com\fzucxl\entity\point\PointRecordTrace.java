package com.fzucxl.entity.point;

import com.fzucxl.open.base.point.PointTransactionType;
import com.fzucxl.open.base.point.PointTraceType;
import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分明细追踪实体
 * 
 * <AUTHOR>
 */
@MappedEntity("point_record_trace")
@Data
public class PointRecordTrace {
    
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 品牌编码
     */

    private String brandCode;
    /**
     * 源交易id
     */
    private String sourceTransactionId;

    /**
     * 交易id
     */
    private String transactionId;

    /**
     * 追踪类型
     */
    private PointTraceType traceType;

    /**
     * 积分
     */
    private Long point;

    /**
     * 源明细id
     */
    private Long sourceRecordId;

    /**
     * 目标明细id
     */
    private Long targetRecordId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 交易类型
     */
    private PointTransactionType transactionType;

    /**
     * 追踪原因
     */
    private String traceReason;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}