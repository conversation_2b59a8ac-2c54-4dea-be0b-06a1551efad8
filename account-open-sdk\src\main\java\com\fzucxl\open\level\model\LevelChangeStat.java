﻿package com.fzucxl.open.level.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 等级变更统计
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LevelChangeStat extends Extensible {    
    /**
     * 统计日期
     */
    private String statDate;    
    /**
     * 升级次数
     */
    private Long upgradeCount;    
    /**
     * 降级次数
     */
    private Long downgradeCount;    
    /**
     * 总变更次数
     */
    private Long totalChange;}
