package com.fzucxl.point.rule.dto;

import io.micronaut.core.annotation.Introspected;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 创建积分规则请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Introspected
@Data
public class CreateRuleRequest {
    
    @NotBlank(message = "账户代码不能为空")
    private String accountCode;
    
    @NotBlank(message = "规则代码不能为空")
    private String ruleCode;
    
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;
    
    private String description;
    
    @NotBlank(message = "属性代码不能为空")
    private String attributeCode;
    
    @NotBlank(message = "操作符不能为空")
    private String operator;
    
    private Object threshold;
    
    private Integer priority;
    
    private LocalDateTime effectiveTime;
    
    private LocalDateTime expireTime;
    
    @NotBlank(message = "创建人不能为空")
    private String creatorId;
    
    private String updaterId;
    
    private java.util.List<String> additionalConditions;
    
    private java.util.Map<String, Object> ruleParams;
    
    // 构造函数
    public CreateRuleRequest() {
        this.priority = 0;
    }

}