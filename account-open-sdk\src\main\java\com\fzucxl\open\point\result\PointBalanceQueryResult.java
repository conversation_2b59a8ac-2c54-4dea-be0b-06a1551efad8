﻿package com.fzucxl.open.point.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fzucxl.open.common.Extensible;

/**
 * 查询用户积分余额结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointBalanceQueryResult extends Extensible {    
    /**
     * 用户ID
     */
    private Long userId;    
    /**
     * 账户代码
     */
    private String accountCode;    
    /**
     * 总积分（历史累计）
     */
    private Long totalPoint;    
    /**
     * 可用积分
     */
    private Long availablePoint;    
    /**
     * 冻结积分
     */
    private Long frozenPoint;    
    /**
     * 已过期积分
     */
    private Long expiredPoint;    
    /**
     * 30天内即将过期积分
     */
    private Long expiringSoonPoint;    
    /**
     * 下次过期时间
     */
    private String nextExpireDate;    
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;}
