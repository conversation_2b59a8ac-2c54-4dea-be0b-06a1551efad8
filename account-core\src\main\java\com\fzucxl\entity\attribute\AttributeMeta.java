package com.fzucxl.entity.attribute;

import io.micronaut.data.annotation.GeneratedValue;
import io.micronaut.data.annotation.Id;
import io.micronaut.data.annotation.MappedEntity;
import io.micronaut.data.annotation.TypeDef;
import io.micronaut.data.model.DataType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 属性表达式元数据实体
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedEntity("attribute_meta")
@Data
public class AttributeMeta {
    
    @Id
    @GeneratedValue
    private Long id;
    
    private String attributeCode;
    private String attributeName;
    private String description;
    private String dataType;
    private String dataSource;
    private String targetTable;
    private String aggregateFunction;
    private String aggregateField;
    
    @TypeDef(type = DataType.JSON)
    private String filterConditions;
    
    @TypeDef(type = DataType.JSON)
    private String timeRangeConfig;
    
    private Integer cacheTtl;
    private BigDecimal defaultValue;
    private String status;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 构造函数
    public AttributeMeta() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
        this.status = "ACTIVE";
        this.cacheTtl = 300;
        this.defaultValue = BigDecimal.ZERO;
    }
}